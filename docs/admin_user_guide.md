# Admin Interface User Guide

## Overview

The Ghostwrote admin interface provides comprehensive tools for managing users, jobs, organizations, and platform operations. This guide covers all administrative features and best practices.

## Getting Started

### Accessing the Admin Interface

1. **Login Requirements**: You must have an admin role (superadmin, support, or readonly)
2. **Access URL**: Navigate to `/super_admin` after logging in
3. **Dashboard**: The main dashboard provides overview statistics and navigation

### Admin Roles & Permissions

#### Superadmin

- **Full Access**: All administrative functions
- **User Management**: Create, edit, delete users and roles
- **System Configuration**: Access to all settings and configurations
- **Audit Access**: Full audit log viewing and management

#### Support Admin

- **Limited Access**: Most administrative functions except sensitive operations
- **User Support**: Help users with account issues and basic modifications
- **Content Management**: Manage jobs, conversations, and content moderation
- **Audit Access**: View audit logs but cannot delete

#### Readonly Admin

- **View Only**: Can view all data but cannot make changes
- **Reporting**: Generate reports and export data
- **Monitoring**: Monitor system health and user activity
- **No Audit Access**: Cannot view sensitive audit information

## Core Features

### Dashboard

The admin dashboard provides:

- **User Statistics**: Total users, new registrations, active users
- **Job Metrics**: Total jobs, active postings, applications
- **System Health**: Recent activity and alerts
- **Quick Actions**: Navigation to frequently used admin sections

### User Management

#### User Index

- **Search**: Find users by name, email, or organization
- **Filters**: Filter by role, verification status, onboarding completion
- **Bulk Actions**: Perform actions on multiple users simultaneously
- **Export**: Download user data in CSV format

#### User Details

- **Profile Information**: View complete user profiles
- **Role Management**: Assign and modify user roles
- **Account Status**: Manage verification and account status
- **Activity History**: View user's platform activity

#### User Actions

```
✅ Superadmin: Create, Edit, Delete, Assign Roles
✅ Support: Edit (limited), View, Basic Support Actions
✅ Readonly: View Only
```

### Job Management

#### Job Index

- **Category Filters**: Filter by job type (social media, newsletter, lead magnet)
- **Status Filters**: Filter by draft, published, expired status
- **Search**: Find jobs by title, description, or organization
- **Bulk Operations**: Manage multiple jobs efficiently

#### Job Details

- **Job Information**: Complete job details and requirements
- **Applications**: View and manage job applications
- **Performance Metrics**: Track job performance and engagement
- **Status Management**: Change job status and visibility

#### Job Actions

```
✅ Superadmin: Full CRUD operations
✅ Support: Edit status, manage applications
✅ Readonly: View only
```

### Organization Management

#### Organization Index

- **Search**: Find organizations by name or industry
- **Member Count**: View organization size and activity
- **Status Tracking**: Monitor organization health and engagement
- **Export**: Download organization data

#### Organization Details

- **Organization Profile**: Complete organization information
- **Members**: View and manage organization members
- **Jobs**: See all jobs posted by organization
- **Activity**: Track organization platform usage

### Communication Management

#### Chat Requests

- **Status Monitoring**: Track pending, accepted, declined requests
- **User Relationships**: View scout-talent connections
- **Moderation**: Review and moderate chat request content
- **Analytics**: Monitor chat request patterns and success rates

#### Conversations

- **Active Monitoring**: View ongoing conversations
- **Content Moderation**: Review conversation content for policy compliance
- **User Support**: Assist with conversation-related issues
- **Analytics**: Track conversation engagement and outcomes

### Audit & Security

#### Audit Logs

- **Action Tracking**: View all administrative actions
- **User Attribution**: See which admin performed each action
- **Change History**: Track what was changed and when
- **Search & Filter**: Find specific actions or time periods

#### Jobs Dashboard

- **Background Job Monitoring**: View active job queues and processing status
- **Failed Job Management**: Monitor and retry failed background jobs
- **Performance Metrics**: Track job processing times and queue health
- **Queue Management**: Monitor different job queues and their status
- **Access**: Available via "Jobs Dashboard" in the navigation menu at `/jobs`

#### Security Features

- **Session Management**: Monitor admin sessions and activity
- **Permission Enforcement**: Role-based access control
- **Activity Monitoring**: Track suspicious or unusual activity
- **Audit Trail**: Complete record of all administrative actions

## Advanced Features

### Search & Filtering

#### Global Search

- **Cross-Model Search**: Search across users, jobs, organizations
- **Advanced Filters**: Combine multiple criteria
- **Saved Searches**: Save frequently used search combinations
- **Export Results**: Download filtered data

#### Filter Combinations

```
Users: Role + Verification + Date Range + Text Search
Jobs: Category + Status + Organization + Date Range
Organizations: Size + Industry + Activity Level
```

### Bulk Operations

#### Available Bulk Actions

- **User Management**: Role assignment, status changes, notifications
- **Job Management**: Status updates, category changes, bulk deletion
- **Content Moderation**: Bulk approval/rejection of content
- **Data Export**: Bulk export with custom filters

#### Safety Features

- **Confirmation Prompts**: Prevent accidental bulk changes
- **Preview Mode**: See what will be affected before applying
- **Undo Capability**: Reverse recent bulk operations where possible
- **Audit Logging**: All bulk actions are logged for accountability

### Data Export

#### Export Formats

- **CSV**: Standard comma-separated values
- **Excel**: Formatted spreadsheets with multiple sheets
- **JSON**: Machine-readable format for integrations

#### Export Options

- **Filtered Data**: Export only filtered/searched results
- **Complete Datasets**: Export entire model data
- **Custom Fields**: Select specific fields to include
- **Scheduled Exports**: Set up recurring exports

## Best Practices

### Security Guidelines

1. **Role Assignment**: Only assign minimum necessary permissions
2. **Regular Audits**: Review admin actions and access patterns
3. **Session Management**: Log out when finished, use secure connections
4. **Data Handling**: Follow data protection guidelines for user information

### Operational Guidelines

1. **Documentation**: Document significant administrative actions
2. **Communication**: Coordinate with team before major changes
3. **Testing**: Test changes in staging before production
4. **Monitoring**: Regularly check system health and user feedback

### Data Management

1. **Regular Backups**: Ensure data is backed up before major operations
2. **Data Quality**: Maintain clean, accurate data through regular reviews
3. **Privacy Compliance**: Follow GDPR/privacy guidelines for user data
4. **Retention Policies**: Follow data retention and deletion policies

## Troubleshooting

### Common Issues

#### Access Problems

- **Permission Denied**: Check user role assignments
- **Session Expired**: Re-login and verify admin status
- **Page Not Found**: Verify admin routes are properly configured

#### Data Issues

- **Search Not Working**: Check search indexes and database connectivity
- **Export Failures**: Verify file permissions and disk space
- **Slow Performance**: Check database query optimization

#### User Support

- **Account Issues**: Use user management tools to diagnose problems
- **Role Problems**: Verify role assignments and permissions
- **Data Discrepancies**: Use audit logs to track changes

### Getting Help

1. **Documentation**: Check this guide and technical documentation
2. **Audit Logs**: Review recent actions for clues about issues
3. **System Logs**: Check application logs for error messages
4. **Team Support**: Contact development team for technical issues

## Quick Reference

### Keyboard Shortcuts

```
Ctrl/Cmd + K: Global search
Ctrl/Cmd + E: Export current view
Ctrl/Cmd + F: Filter current page
Esc: Close modals/cancel actions
```

### Common URLs

```
/super_admin - Main dashboard
/super_admin/admin_users - User management
/super_admin/admin_jobs - Job management
/super_admin/admin_organizations - Organization management
/super_admin/admin_audit_logs - Audit logs
```

### Status Indicators

```
🟢 Active/Published - Fully operational
🟡 Pending/Draft - Awaiting action
🔴 Inactive/Expired - Not operational
⚪ Unknown/Error - Needs investigation
```

---

**Last Updated**: 2025-06-29  
**Version**: 1.0  
**For Support**: Contact development team
