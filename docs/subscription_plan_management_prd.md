# Subscription Plan Management System - PRD

## Overview

This PRD outlines the implementation of a comprehensive subscription plan management system in the super admin section to replace hardcoded Stripe price IDs with dynamic, database-driven plan management, treating <PERSON><PERSON> as the source of truth.

## Problem Statement

The application currently has hardcoded Stripe price IDs scattered throughout the codebase:

- `price_1R9Q55DYYVPVcCCrWQOwsKmT` (Standard Plan)
- `price_1R9Q66DYYVPVcCCrnqiXNafF` (Premium Plan - Legacy)

This creates maintenance issues and makes it difficult to manage pricing changes, add new plans, or maintain consistency between the application and Stripe.

## Goals

### Primary Goals

1. **Establish Stripe as the source of truth** for all subscription plan data
2. **Centralize plan management** in super admin interface
3. **Eliminate hardcoded price IDs** throughout the codebase
4. **Maintain real-time sync** with Stripe pricing data
5. **Provide audit trails** for all plan changes
6. **Ensure data integrity** between local database and Stripe

### Secondary Goals

- Improve system maintainability and reduce technical debt
- Enable flexible pricing strategies and A/B testing
- Provide better error handling and system resilience
- Support future subscription feature expansion

## Solution Architecture

### Data Flow Architecture

1. **Stripe (Source of Truth)** - All plan creation and core updates happen in Stripe
2. **Local Database (Read-optimized Mirror)** - Local copy for performance and relationship management
3. **One-way Synchronization** - Data flows from Stripe to local database, never the reverse

### Database Design

**New `SubscriptionPlan` Model:**

```ruby
# Fields:
- stripe_price_id (string, unique, not null)
- stripe_product_id (string)
- name (string, not null)
- description (text)
- amount (integer) # cents
- currency (string, default: 'usd')
- billing_interval (string) # 'month', 'year'
- billing_interval_count (integer, default: 1)
- active (boolean, default: true)
- legacy (boolean, default: false)
- features (jsonb) # array of features
- metadata (jsonb) # additional Stripe metadata
- stripe_created_at (datetime)
- last_synced_at (datetime)
- created_at, updated_at (datetime)
```

### Synchronization Strategy

1. **Initial Import** - One-time import of all Stripe products and prices
2. **Webhook-based Updates** - Real-time updates when products/prices change in Stripe
3. **Manual Sync** - Admin-triggered synchronization for immediate updates
4. **Scheduled Background Jobs** - Daily/weekly sync to catch any missed updates

### Service Layer

1. **`SubscriptionPlanSyncService`** - Stripe API synchronization
   - Product and price fetching
   - Data mapping and transformation
   - Conflict resolution
2. **`SubscriptionPlanValidationService`** - Data integrity validation
3. **`SubscriptionPlanMigrationService`** - Transition management

### Admin Interface Features

1. **View all plans** - List with filtering and search
2. **View plan details** - Complete information with usage statistics
3. **Sync with Stripe** - Manual synchronization triggers
4. **Deactivate plans** - Soft delete without breaking existing subscriptions
5. **Usage statistics** - Active subscriptions and revenue data
6. **Audit trails** - Complete change history

## Technical Requirements

### Security

- Require superadmin role for plan management
- Validate all Stripe API interactions
- Audit log all plan changes
- Secure handling of Stripe credentials

### Performance

- Cache frequently accessed plan data
- Optimize database queries with proper indexes
- Background jobs for heavy sync operations
- Graceful degradation when Stripe API unavailable

### Data Integrity

- Validate plan data before saving
- Prevent deletion of plans with active subscriptions
- Maintain referential integrity with Pay gem tables
- Ensure pricing consistency between local and Stripe

## Implementation Plan

### Phase 1: Database Foundation

- [ ] Create SubscriptionPlan model and migration
- [ ] Create service classes structure
- [ ] Implement initial import from Stripe

### Phase 2: Synchronization Layer

- [ ] Implement SubscriptionPlanSyncService with all sync strategies
- [ ] Set up webhook handlers for product and price events
- [ ] Create scheduled background job for periodic sync
- [ ] Update StripePricingService to use SubscriptionPlan model

### Phase 3: Super Admin Interface

- [ ] Create admin controller with read operations
- [ ] Create admin views with stone color palette
- [ ] Add manual sync functionality and status pages

### Phase 4: Codebase Migration

- [ ] Update SubscriptionHelper and remove hardcoded mappings
- [ ] Update subscription controllers and views
- [ ] Update webhook handlers

### Phase 5: Testing and Validation

- [ ] Add comprehensive tests and validation
- [ ] Performance optimization and documentation

## Success Metrics

### Technical Metrics

- Zero hardcoded price IDs remaining in codebase
- 100% plan data sync accuracy with Stripe
- Sub-100ms response time for plan lookups
- Zero subscription flow disruptions during migration

### Business Metrics

- Reduced time to add new subscription plans (from hours to minutes)
- Improved pricing consistency across platform
- Enhanced audit trail compliance
- Reduced technical debt and maintenance overhead

## Risk Mitigation

### Implementation Risks

- **Phased rollout** to minimize disruption
- **Backward compatibility** during transition
- **Comprehensive testing** at each phase
- **Rollback procedures** for critical issues

### Operational Risks

- **Stripe API rate limiting** - Implement proper throttling
- **Data inconsistency** - Regular validation and sync checks
- **Performance impact** - Caching and optimization strategies
- **Webhook failures** - Implement retry mechanisms and fallback to scheduled sync

## Dependencies

### Technical Dependencies

- Existing Pay gem integration
- Stripe API access and credentials
- Super admin interface framework
- AdminAuditLog system
- Background job processor (Sidekiq)

### Business Dependencies

- Approval for subscription plan changes
- Coordination with billing/finance teams
- User communication for any pricing updates

## Timeline

**Estimated Duration:** 2-3 weeks
**Task Breakdown:** 20 tasks, ~20 minutes each
**Critical Path:** Database → Sync Service → Admin Interface → Migration → Testing

## Acceptance Criteria

1. ✅ All hardcoded Stripe price IDs removed from codebase
2. ✅ Super admin interface for plan management and synchronization
3. ✅ Multiple sync mechanisms implemented (initial, webhook, manual, scheduled)
4. ✅ Comprehensive audit trails for all changes
5. ✅ Zero disruption to existing subscription flows
6. ✅ Performance meets or exceeds current system
7. ✅ Complete test coverage for new functionality
8. ✅ Documentation updated for new system

---

**Document Version:** 1.1  
**Last Updated:** 2025-01-15  
**Owner:** Development Team  
**Stakeholders:** Engineering, Product, Finance
