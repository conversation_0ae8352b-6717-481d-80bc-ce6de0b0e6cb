# frozen_string_literal: true

module Talent
  module SubscriptionAccess
    extend ActiveSupport::Concern

    SAFE_VERBS = %w[GET HEAD OPTIONS].freeze

    included do
      helper_method :subscription_gate?, :mask_subscription_data? if respond_to?(:helper_method)
    end

    # Active-only: no trialing/past_due unlock
    def talent_subscription_active?(user = nil)
      user ||= Current.user
      pp = user&.payment_processor
      return false unless pp

      # Explicit status filter to avoid Pay's broader :active scopes including trialing
      pp.subscriptions.where(status: 'active').exists?
    end

    # Soft gate for safe verbs, hard redirect for mutations
    def require_talent_subscription!
      return if talent_subscription_active?

      if SAFE_VERBS.include?(request.request_method)
        @subscription_gate = true
        @mask_subscription_data = true
        # Do not redirect; views/layout will render overlay
      else
        redirect_to talent_upgrade_path, notice: 'Upgrade to access this feature.'
      end
    end

    def subscription_gate? = !!@subscription_gate
    def mask_subscription_data? = !!@mask_subscription_data
  end
end
