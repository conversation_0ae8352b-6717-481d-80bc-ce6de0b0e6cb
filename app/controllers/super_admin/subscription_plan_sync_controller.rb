# frozen_string_literal: true

module SuperAdmin
  # Controller for subscription plan synchronization monitoring and management
  class SubscriptionPlanSyncController < AdminBaseController
    before_action :ensure_super_admin

    # GET /super_admin/subscription_plan_sync
    def index
      @monitoring_service = SubscriptionPlanSyncMonitoringService.new
      @sync_status = @monitoring_service.get_sync_status
      @page_title = 'Subscription Plan Sync Monitoring'
    end

    # POST /super_admin/subscription_plan_sync/trigger
    def trigger
      @monitoring_service = SubscriptionPlanSyncMonitoringService.new
      
      sync_type = params[:sync_type] || 'full'
      force_sync = params[:force_sync] == 'true'
      price_ids = params[:price_ids].to_s.split(',').map(&:strip).reject(&:blank?)
      
      result = @monitoring_service.trigger_manual_sync(
        sync_type: sync_type,
        force_sync: force_sync,
        price_ids: price_ids
      )
      
      if result[:success]
        flash[:notice] = result[:message]
      else
        flash[:alert] = "Failed to trigger sync: #{result[:error]}"
      end
      
      redirect_to super_admin_subscription_plan_sync_index_path
    end

    # DELETE /super_admin/subscription_plan_sync/cleanup
    def cleanup
      @monitoring_service = SubscriptionPlanSyncMonitoringService.new
      result = @monitoring_service.cleanup_old_data
      
      if result[:success]
        flash[:notice] = result[:message]
      else
        flash[:alert] = "Failed to cleanup data: #{result[:error]}"
      end
      
      redirect_to super_admin_subscription_plan_sync_index_path
    end

    # GET /super_admin/subscription_plan_sync/status (AJAX endpoint)
    def status
      @monitoring_service = SubscriptionPlanSyncMonitoringService.new
      @sync_status = @monitoring_service.get_sync_status
      
      render json: {
        last_full_sync: @sync_status[:last_full_sync],
        last_health_check: @sync_status[:last_health_check],
        current_alerts: @sync_status[:current_alerts],
        system_health: @sync_status[:system_health],
        statistics: @sync_status[:statistics]
      }
    end

    # GET /super_admin/subscription_plan_sync/history (AJAX endpoint)
    def history
      @monitoring_service = SubscriptionPlanSyncMonitoringService.new
      @sync_history = @monitoring_service.get_sync_history(limit: 50)
      
      render json: {
        history: @sync_history
      }
    end

    private

    def ensure_super_admin
      unless current_user&.super_admin?
        flash[:alert] = 'Access denied. Super admin privileges required.'
        redirect_to root_path
      end
    end
  end
end
