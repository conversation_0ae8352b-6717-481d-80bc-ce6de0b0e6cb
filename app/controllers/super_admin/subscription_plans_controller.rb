# frozen_string_literal: true

class SuperAdmin::SubscriptionPlansController < SuperAdmin::AdminBaseController
  include Pagy::Backend
  before_action :set_subscription_plan, only: %i[show edit update destroy sync_plan]
  before_action :check_edit_permission, only: %i[edit update]
  before_action :check_create_permission, only: %i[new create]
  before_action :check_delete_permission, only: %i[destroy]

  def index
    @subscription_plans = SubscriptionPlan.includes(:subscriptions)

    # Apply search
    @subscription_plans = apply_search(@subscription_plans, %w[name description stripe_price_id])

    # Apply filters
    if params[:status].present? && params[:status] != 'all'
      case params[:status]
      when 'active'
        @subscription_plans = @subscription_plans.active
      when 'inactive'
        @subscription_plans = @subscription_plans.where(active: false)
      when 'legacy'
        @subscription_plans = @subscription_plans.legacy
      when 'available'
        @subscription_plans = @subscription_plans.available_for_signup
      end
    end

    if params[:billing_interval].present? && params[:billing_interval] != 'all'
      @subscription_plans = @subscription_plans.where(billing_interval: params[:billing_interval])
    end

    if params[:sync_status].present? && params[:sync_status] != 'all'
      case params[:sync_status]
      when 'needs_sync'
        @subscription_plans = @subscription_plans.needs_sync
      when 'recently_synced'
        @subscription_plans = @subscription_plans.where('last_synced_at > ?', 1.hour.ago)
      when 'never_synced'
        @subscription_plans = @subscription_plans.where(last_synced_at: nil)
      end
    end

    # Apply sorting
    @subscription_plans = apply_sorting(
      @subscription_plans,
      %w[name amount billing_interval active legacy created_at last_synced_at]
    )

    respond_to do |format|
      format.html do
        # Handle saved search
        handle_saved_search

        # Paginate for HTML only
        @pagy, @subscription_plans = pagy(@subscription_plans, items: @page_size)

        # Set up filter options
        @filter_options = {
          status: [
            ['All Plans', 'all'],
            ['Active', 'active'],
            ['Inactive', 'inactive'],
            ['Legacy', 'legacy'],
            ['Available for Signup', 'available']
          ],
          billing_interval: [
            ['All Intervals', 'all'],
            ['Monthly', 'month'],
            ['Yearly', 'year']
          ],
          sync_status: [
            ['All Sync Status', 'all'],
            ['Needs Sync', 'needs_sync'],
            ['Recently Synced', 'recently_synced'],
            ['Never Synced', 'never_synced']
          ]
        }

        # Get saved searches for current user
        @saved_searches = get_saved_searches
      end
      format.json do
        render json: {
          plans: @subscription_plans.limit(20).map do |plan|
            {
              id: plan.id,
              name: plan.name,
              stripe_price_id: plan.stripe_price_id,
              formatted_amount: plan.formatted_amount,
              billing_cycle: plan.billing_cycle_description,
              status: plan.status_text,
              active: plan.active?,
              legacy: plan.legacy?
            }
          end
        }
      end
    end
  end

  def show
    @subscription_count = @subscription_plan.subscription_count
    @active_subscriptions = @subscription_plan.active_subscriptions_count
    @recent_subscriptions = @subscription_plan.recent_subscriptions(5)
  end

  def new
    @subscription_plan = SubscriptionPlan.new
  end

  def create
    @subscription_plan = SubscriptionPlan.new(subscription_plan_params)

    # Clean up empty features
    if @subscription_plan.features.present?
      @subscription_plan.features = @subscription_plan.features.reject(&:blank?)
    end

    begin
      if @subscription_plan.save
        log_admin_action('subscription_plan_created', resource: @subscription_plan, details: {
          plan_attributes: @subscription_plan.attributes.except('created_at', 'updated_at')
        })

        # Optionally sync with Stripe if stripe_price_id is provided
        if @subscription_plan.stripe_price_id.present?
          sync_result = SubscriptionPlanSyncService.sync_plan_from_stripe(@subscription_plan.stripe_price_id)
          if sync_result[:success]
            flash[:notice] = 'Subscription plan was successfully created and synced with Stripe.'
          else
            flash[:notice] = 'Subscription plan was created, but Stripe sync failed. Please sync manually.'
          end
        else
          flash[:notice] = 'Subscription plan was successfully created.'
        end

        redirect_to super_admin_subscription_plan_path(@subscription_plan)
      else
        log_admin_action('subscription_plan_creation_failed', details: {
          errors: @subscription_plan.errors.full_messages,
          attempted_attributes: subscription_plan_params
        })
        render :new, status: :unprocessable_entity
      end
    rescue StandardError => e
      Rails.logger.error "SubscriptionPlansController#create error: #{e.message}"
      log_admin_action('subscription_plan_creation_error', details: {
        error_message: e.message,
        attempted_attributes: subscription_plan_params
      })
      flash.now[:alert] = "An error occurred while creating the subscription plan: #{e.message}"
      render :new, status: :unprocessable_entity
    end
  end

  def edit; end

  def update
    begin
      if @subscription_plan.update(subscription_plan_params)
        log_admin_action('subscription_plan_updated', resource: @subscription_plan, details: {
          changes: @subscription_plan.previous_changes,
          updated_fields: @subscription_plan.previous_changes.keys
        })

        # Note: Only metadata fields can be updated - pricing is managed in Stripe
        flash[:notice] = 'Subscription plan metadata was successfully updated. Pricing changes must be made in Stripe.'
        redirect_to super_admin_subscription_plan_path(@subscription_plan)
      else
        log_admin_action('subscription_plan_update_failed', resource: @subscription_plan, details: {
          errors: @subscription_plan.errors.full_messages,
          attempted_changes: subscription_plan_params
        })
        render :edit, status: :unprocessable_entity
      end
    rescue StandardError => e
      Rails.logger.error "SubscriptionPlansController#update error: #{e.message}"
      log_admin_action('subscription_plan_update_error', resource: @subscription_plan, details: {
        error_message: e.message,
        attempted_changes: subscription_plan_params
      })
      flash.now[:alert] = "An error occurred while updating the subscription plan: #{e.message}"
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    begin
      # Check if plan has active subscriptions
      if @subscription_plan.subscriptions.active.exists?
        flash[:alert] = 'Cannot delete subscription plan with active subscriptions. Please deactivate the plan instead.'
        redirect_to super_admin_subscription_plan_path(@subscription_plan)
        return
      end

      # Check if plan is assigned to any sections
      if @subscription_plan.available_for_any_section?
        assigned_sections = @subscription_plan.assigned_sections.join(' and ')
        flash[:alert] = "Cannot delete subscription plan that is assigned to #{assigned_sections} section(s). Remove section assignments first."
        redirect_to super_admin_subscription_plan_path(@subscription_plan)
        return
      end

      plan_name = @subscription_plan.name
      @subscription_plan.destroy!

      log_admin_action('subscription_plan_deleted', details: {
        deleted_plan_name: plan_name,
        deleted_plan_id: @subscription_plan.id
      })

      flash[:notice] = "Subscription plan '#{plan_name}' was successfully deleted."
      redirect_to super_admin_subscription_plans_path
    rescue StandardError => e
      Rails.logger.error "SubscriptionPlansController#destroy error: #{e.message}"
      log_admin_action('subscription_plan_deletion_error', resource: @subscription_plan, details: {
        error_message: e.message
      })
      flash[:alert] = "An error occurred while deleting the subscription plan: #{e.message}"
      redirect_to super_admin_subscription_plan_path(@subscription_plan)
    end
  end

  # Sync individual plan with Stripe
  def sync_plan
    # Check if plan has a Stripe Price ID
    if @subscription_plan.stripe_price_id.blank?
      flash[:alert] = 'Cannot sync plan: No Stripe Price ID associated with this plan. Please add a Stripe Price ID first.'
      redirect_to super_admin_subscription_plan_path(@subscription_plan)
      return
    end

    begin
      result = SubscriptionPlanSyncService.sync_plan_from_stripe(@subscription_plan.stripe_price_id)

      if result[:success]
        log_admin_action('subscription_plan_synced', resource: @subscription_plan, details: {
          sync_result: result
        })
        flash[:notice] = 'Subscription plan successfully synced with Stripe.'
      else
        log_admin_action('subscription_plan_sync_failed', resource: @subscription_plan, details: {
          sync_errors: result[:errors]
        })
        flash[:alert] = "Sync failed: #{result[:errors].join(', ')}"
      end
    rescue StandardError => e
      Rails.logger.error "SubscriptionPlansController#sync_plan error: #{e.message}"
      log_admin_action('subscription_plan_sync_error', resource: @subscription_plan, details: {
        error_message: e.message
      })
      flash[:alert] = "An error occurred during sync: #{e.message}"
    end

    redirect_to super_admin_subscription_plan_path(@subscription_plan)
  end

  # Sync all plans with Stripe
  def sync_all
    begin
      result = SubscriptionPlanSyncService.sync_all_from_stripe

      log_admin_action('subscription_plans_bulk_sync', details: {
        sync_result: result
      })

      if result[:success]
        flash[:notice] = "Successfully synced #{result[:synced_count]} plans with Stripe."
      else
        flash[:alert] = "Bulk sync completed with errors: #{result[:errors].join(', ')}"
      end
    rescue StandardError => e
      Rails.logger.error "SubscriptionPlansController#sync_all error: #{e.message}"
      log_admin_action('subscription_plans_bulk_sync_error', details: {
        error_message: e.message
      })
      flash[:alert] = "An error occurred during bulk sync: #{e.message}"
    end

    redirect_to super_admin_subscription_plans_path
  end

  # Discover all available Stripe products and prices
  def discover_products
    begin
      result = StripeProductDiscoveryService.discover_all_products

      log_admin_action('stripe_products_discovery', details: {
        discovery_result: result
      })

      if result[:success]
        @products = result[:products]
        @total_products = result[:total_products]
        @total_prices = result[:total_prices]

        # Calculate statistics
        @synced_prices = @products.sum { |p| p[:prices].count { |price| price[:synced] } }
        @unsynced_prices = @total_prices - @synced_prices

        render :discover_products
      else
        flash[:alert] = "Failed to discover Stripe products: #{result[:errors].join(', ')}"
        redirect_to super_admin_subscription_plans_path
      end
    rescue StandardError => e
      Rails.logger.error "SubscriptionPlansController#discover_products error: #{e.message}"
      log_admin_action('stripe_products_discovery_error', details: {
        error_message: e.message
      })
      flash[:alert] = "An error occurred during product discovery: #{e.message}"
      redirect_to super_admin_subscription_plans_path
    end
  end

  # Sync individual price from discovery page
  def sync_price
    stripe_price_id = params[:stripe_price_id]

    if stripe_price_id.blank?
      render json: { success: false, errors: ['Price ID is required'] }, status: :bad_request
      return
    end

    begin
      result = SubscriptionPlanSyncService.sync_plan_from_stripe(stripe_price_id)

      log_admin_action('stripe_price_synced_from_discovery', details: {
        stripe_price_id: stripe_price_id,
        sync_result: result
      })

      if result[:success]
        # Get the updated plan data for the response
        plan = SubscriptionPlan.find_by(stripe_price_id: stripe_price_id)

        render json: {
          success: true,
          action: result[:action],
          plan: {
            id: plan.id,
            name: plan.name,
            available_for_scout: plan.available_for_scout,
            available_for_talent: plan.available_for_talent
          },
          message: "Price #{result[:action] == :created ? 'synced and created' : 'updated'} successfully"
        }
      else
        render json: {
          success: false,
          errors: result[:errors]
        }, status: :unprocessable_entity
      end
    rescue StandardError => e
      Rails.logger.error "SubscriptionPlansController#sync_price error: #{e.message}"
      log_admin_action('stripe_price_sync_error', details: {
        stripe_price_id: stripe_price_id,
        error_message: e.message
      })

      render json: {
        success: false,
        errors: ["An error occurred during sync: #{e.message}"]
      }, status: :internal_server_error
    end
  end

  # Assign a Stripe price to app sections
  def assign_price_to_sections
    stripe_price_id = params[:stripe_price_id]
    sections = params[:sections] || []

    # Ensure sections is an array
    sections = [sections] unless sections.is_a?(Array)
    sections = sections.compact.reject(&:blank?)

    begin
      result = StripeProductDiscoveryService.assign_price_to_sections(stripe_price_id, sections)

      log_admin_action('stripe_price_section_assignment', details: {
        stripe_price_id: stripe_price_id,
        sections: sections,
        result: result
      })

      if result[:success]
        action_text = result[:action] == :created_and_assigned ? 'created and assigned' : 'updated'
        flash[:notice] = "Successfully #{action_text} plan #{stripe_price_id} for sections: #{sections.join(', ')}"
      else
        flash[:alert] = "Failed to assign price to sections: #{result[:errors].join(', ')}"
      end
    rescue StandardError => e
      Rails.logger.error "SubscriptionPlansController#assign_price_to_sections error: #{e.message}"
      log_admin_action('stripe_price_section_assignment_error', details: {
        stripe_price_id: stripe_price_id,
        sections: sections,
        error_message: e.message
      })
      flash[:alert] = "An error occurred during section assignment: #{e.message}"
    end

    # Redirect back to discovery page or plans index
    if params[:from] == 'discovery'
      redirect_to discover_products_super_admin_subscription_plans_path
    else
      redirect_to super_admin_subscription_plans_path
    end
  end

  private

  def set_subscription_plan
    @subscription_plan = SubscriptionPlan.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    flash[:alert] = 'Subscription plan not found.'
    redirect_to super_admin_subscription_plans_path
  end

  def subscription_plan_params
    # Only allow editing of metadata fields - pricing fields are managed in Stripe
    params.require(:subscription_plan).permit(
      :stripe_price_id, :stripe_product_id, :name, :description,
      # Removed: :amount, :currency, :billing_interval, :billing_interval_count (Stripe-managed)
      :active, :legacy, :available_for_scout, :available_for_talent,
      features: [], metadata: {}
    )
  end

  def resource_name
    'subscription_plan'
  end
end
