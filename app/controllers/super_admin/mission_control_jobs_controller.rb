# frozen_string_literal: true

# Custom controller for Mission Control Jobs that inherits from SuperAdmin::BaseController
# This ensures all Mission Control Jobs routes use our existing super admin authentication
#
# Mission Control Jobs is configured in config/application.rb to use this controller as the
# base_controller_class, which means all Mission Control Jobs routes will inherit from this
# controller and thus use our existing authentication and authorization system.
#
# The controller provides:
# - Authentication via SuperAdmin::BaseController (requires admin role)
# - Authorization via role-based access control (superadmin, support, readonly)
# - Session validation and Current.user context
# - Audit logging for administrative actions
#
# Access: Available at /jobs for all admin users
# Navigation: Integrated into super admin navigation bar
class SuperAdmin::MissionControlJobsController < SuperAdmin::BaseController
  # Override the layout to use Mission Control Jobs' own layout instead of admin layout
  # This allows Mission Control Jobs to render with its native UI while maintaining authentication
  layout false
end
