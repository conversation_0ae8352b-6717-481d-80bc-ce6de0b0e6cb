# frozen_string_literal: true

module Scout
  module Settings
    class SubscriptionsController < Scout::BaseController
      before_action :set_user_and_organization

      def show
        @subscription = Current.user.subscriptions.active.first # Get the first active subscription
        @loading = false

        if @subscription
          # Fetch additional subscription details
          fetch_subscription_details
          setup_billing_portal
        end
      end

      private

      def set_user_and_organization
        @user = Current.user
        @current_organization = Current.organization

        # Ensure user has an organization
        unless @current_organization
          redirect_to scout_root_path,
                      alert: 'You must be part of an organization to access billing settings.'
        end
      end

      def fetch_subscription_details
        begin
          # Fetch subscription plan details from our database
          @subscription_plan = SubscriptionPlan.find_by(stripe_price_id: @subscription.processor_plan)
          
          # Calculate next billing date
          @next_billing_date = calculate_next_billing_date
          
          Rails.logger.info "Successfully fetched subscription details for user #{Current.user.id}"
        rescue StandardError => e
          Rails.logger.error "Error fetching subscription details for user #{Current.user.id}: #{e.message}"
          # Continue with basic subscription info
        end
      end

      def setup_billing_portal
        # Ensure the default payment processor is set if needed
        Current.user.set_payment_processor(:stripe) unless Current.user.payment_processor
        pay_customer = Current.user.payment_processor

        if pay_customer&.respond_to?(:processor) && pay_customer.processor == :stripe && pay_customer.processor_id.present?
          begin
            # Use the existing pay_customer object directly
            @billing_portal_session = pay_customer.billing_portal(return_url: scout_settings_subscription_url)
            @billing_portal_url = @billing_portal_session.url
            Rails.logger.info "Successfully created billing portal session for user #{Current.user.id}"
          rescue Stripe::InvalidRequestError => e
            Rails.logger.error "Stripe InvalidRequestError creating Billing Portal session for user #{Current.user.id}: #{e.message}"
            handle_billing_portal_error("There was an issue accessing your billing details with Stripe (#{e.code}). Please contact support.")
          rescue Pay::Error => e
            Rails.logger.error "Pay::Error creating Billing Portal session for user #{Current.user.id}: #{e.message}"
            handle_billing_portal_error("Could not access the billing portal due to a payment system error. Please try again later.")
          rescue StandardError => e
            Rails.logger.error "Unexpected error creating Billing Portal session for user #{Current.user.id}: #{e.class} - #{e.message}"
            handle_billing_portal_error("An unexpected error occurred while accessing the billing portal.")
          end
        elsif pay_customer&.processor_id.present?
          # Fallback for pay_customer without processor method but with processor_id
          begin
            @billing_portal_session = pay_customer.billing_portal(return_url: scout_settings_subscription_url)
            @billing_portal_url = @billing_portal_session.url
            Rails.logger.info "Successfully created billing portal session for user #{Current.user.id} (fallback method)"
          rescue StandardError => e
            Rails.logger.error "Error creating billing portal session (fallback): #{e.message}"
            handle_billing_portal_error("Could not access the billing portal. Please try again later.")
          end
        else
          # User has subscription but no valid Stripe Pay::Customer record
          Rails.logger.warn "User #{Current.user.id} has active subscription #{@subscription.id} but no valid Stripe Pay::Customer record."
          handle_billing_portal_error("Your payment processor details seem to be missing or invalid. Please contact support.")
        end
      end

      def handle_billing_portal_error(message)
        flash.now[:alert] = message
        @billing_portal_url = nil
        @billing_portal_error = true
      end

      def calculate_next_billing_date
        # Calculate next billing date based on subscription created date and billing cycle
        return nil unless @subscription.created_at

        # Get billing cycle from database first, then fallback to hardcoded logic
        plan = SubscriptionPlan.find_by(stripe_price_id: @subscription.processor_plan)
        
        if plan&.billing_interval.present?
          interval = plan.billing_interval
          interval_count = plan.billing_interval_count || 1
        else
          # Fallback logic based on price ID patterns
          interval = @subscription.processor_plan.include?('year') ? 'year' : 'month'
          interval_count = 1
        end

        # Calculate next billing date
        case interval
        when 'month'
          @subscription.created_at + interval_count.months
        when 'year'
          @subscription.created_at + interval_count.years
        when 'week'
          @subscription.created_at + interval_count.weeks
        when 'day'
          @subscription.created_at + interval_count.days
        else
          @subscription.created_at + 1.month # Default fallback
        end
      end
    end
  end
end
