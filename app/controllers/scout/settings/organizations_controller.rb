class Scout::Settings::OrganizationsController < Scout::BaseController
  before_action :set_user_and_organization

  def show
    # Organization settings view
  end

  private

  def set_user_and_organization
    @user = Current.user
    @current_organization = Current.organization

    # Ensure user has an organization
    unless @current_organization
      redirect_to scout_root_path,
                  alert:
                    'You must be part of an organization to access settings.'
    end
  end
end
