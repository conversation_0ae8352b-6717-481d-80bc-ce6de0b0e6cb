# frozen_string_literal: true

module Talent
  class SubscriptionsController < Talent::BaseController
    # Ensure user is authenticated if BaseController doesn't handle it
    # before_action :authenticate_user!

    def create
      plan_id = params[:plan] # Get the Price ID from params

      # Validate plan_id presence (optional but recommended)
      unless plan_id.present?
        redirect_to talent_settings_subscription_path, alert: 'Subscription plan not specified.' # Redirect back to settings
        return
      end

      # Validate that the plan is available for new subscriptions (blocks premium plan)
      unless helpers.plan_available_for_new_subscriptions?(plan_id)
        redirect_to talent_settings_subscription_path, alert: 'The selected subscription plan is no longer available for new subscriptions.'
        return
      end

      begin
        # Ensure user is set up with Stripe processor
        Current.user.set_payment_processor(:stripe)

        # Use Pay gem method to create subscription via Stripe Checkout
        checkout_session = Current.user.payment_processor.checkout(
          mode: 'subscription',
          line_items: [{ price: plan_id, quantity: 1 }],
          success_url: success_talent_subscription_url, # Use the new success URL
          cancel_url: cancel_talent_subscription_url    # Use the new cancel URL
        )

        # Redirect to Stripe Checkout page
        redirect_to checkout_session.url, allow_other_host: true, status: :see_other

      rescue Pay::Error => e
        redirect_to cancel_talent_subscription_url, alert: "Subscription failed: #{e.message}" # Redirect to cancel URL on Pay::Error
      rescue StandardError => e
        # Catch other potential errors
        Rails.logger.error("Subscription Error: #{e.message}\n#{e.backtrace.join("\n")}")
        redirect_to cancel_talent_subscription_url, alert: 'An unexpected error occurred during subscription.' # Redirect to cancel URL
      end
    end

    def success
      # Optionally fetch subscription details or just show a success message
      flash[:notice] = 'Subscription successful!'
      # Redirect to settings page or dashboard after showing success
      redirect_to talent_settings_subscription_path, notice: 'Subscription activated successfully!'
    end

    def cancel
      # Show a cancellation message
      flash[:alert] = 'Subscription process was cancelled.'
      # Redirect back to the settings page
      redirect_to talent_settings_subscription_path, alert: 'Subscription process cancelled.'
    end

    # Optional: Add destroy action if you allow cancellation via your app
    # def destroy
    #   subscription = Current.user.subscription
    #   if subscription
    #     begin
    #       subscription.cancel_now! # Or .cancel to cancel at period end
    #       redirect_to talent_dashboard_path, notice: 'Subscription cancelled successfully.'
    #     rescue Pay::Error => e
    #       redirect_to talent_dashboard_path, alert: "Cancellation failed: #{e.message}"
    #     end
    #   else
    #     redirect_to talent_dashboard_path, alert: 'No active subscription found to cancel.'
    #   end
    # end

    private

    # Define talent_dashboard_path if not already available globally
    # def talent_dashboard_path
    #   # Your path helper for the talent dashboard
    # end
    # def talent_dashboard_url(options = {})
    #   # Your url helper for the talent dashboard
    # end
  end
end
