module Talent
  class JobApplicationsController < Talent::BaseController
    before_action :require_talent_subscription!,
                  only: %i[index new create show edit update withdraw]
    before_action :set_job_application, only: %i[show withdraw edit update]
    before_action :set_job, only: %i[new create]

    def index
      base_applications = Current.user.job_applications.includes(:job)

      # Filter by status if provided
      if params[:status].present?
        @job_applications =
          base_applications
            .where(status: params[:status])
            .order(created_at: :desc)
      else
        @job_applications = base_applications.order(created_at: :desc)
      end

      # Get counts for each status
      @all_applications_count = base_applications.count
      @applied_count = base_applications.applied.count
      @reviewed_count = base_applications.reviewed.count
      @qualified_count = base_applications.qualified.count
      @offered_count = base_applications.offered.count
      @accepted_count = base_applications.accepted.count
      @withdrawn_count = base_applications.withdrawn.count
    end

    def new
      @job_application = find_or_initialize_application
      render :new
    end

    def create
      @job_application = find_or_initialize_application

      if params[:job_application]
        @job_application.assign_attributes(job_application_params)
        @job_application.status = :applied

        if @job_application.valid?
          @job_application.save
          redirect_to talent_job_applications_path,
                      notice: 'Application submitted successfully!'
        else
          # Set error messages to display on the page
          @errors = @job_application.errors.full_messages
          flash.now[:alert] = @errors.join(', ')
          render :new, status: :unprocessable_entity
        end
      else
        flash.now[:alert] = 'Application data is missing'
        render :new, status: :unprocessable_entity
      end
    end

    def show
      if mask_subscription_data?
        @job_application = JobApplication.new
        return render :show
      end
    end

    def edit; end

    def update
      if @job_application.update(job_application_params)
        redirect_to talent_job_application_path(@job_application),
                    notice: 'Application updated successfully!'
      else
        @errors = @job_application.errors.full_messages
        flash.now[:alert] = @errors.join(', ')
        render :edit, status: :unprocessable_entity
      end
    end

    def withdraw
      if @job_application.applied? || @job_application.reviewed?
        @job_application.withdrawn!
        render :show
      else
        render json: {
                 error: 'Cannot withdraw application in current status',
               },
               status: :unprocessable_entity
      end
    end

    private

    def set_job
      return if mask_subscription_data?
      @job = Job.find(params[:job_id])
    end

    def find_or_initialize_application
      Current.user.job_applications.find_or_initialize_by(job_id: @job.id)
    end

    def set_job_application
      return if mask_subscription_data?
      @job_application = Current.user.job_applications.find(params[:id])
    end

    def job_application_params
      params
        .require(:job_application)
        .permit(:application_letter, :resume, :additional_info, documents: [])
    end
  end
end
