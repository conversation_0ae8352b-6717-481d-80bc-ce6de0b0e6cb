# frozen_string_literal: true

# Service for monitoring subscription plan synchronization status and health
# Provides insights for admin dashboard and system health checks
class SubscriptionPlanSyncMonitoringService
  include ActiveModel::Model
  include ActiveModel::Attributes

  attr_accessor :logger

  def initialize(logger: Rails.logger)
    @logger = logger
  end

  # Get comprehensive sync status for admin dashboard
  def get_sync_status
    {
      last_full_sync: get_last_sync_info('full'),
      last_bulk_sync: get_last_sync_info('bulk'),
      last_health_check: get_last_sync_info('health_check'),
      sync_history: get_sync_history,
      current_alerts: get_current_alerts,
      system_health: get_system_health,
      statistics: get_sync_statistics
    }
  end

  # Get last sync information for a specific type
  def get_last_sync_info(sync_type)
    Rails.cache.read("subscription_plan_sync_last_#{sync_type}") || {
      sync_type: sync_type,
      timestamp: nil,
      success: nil,
      message: 'No sync recorded'
    }
  end

  # Get sync history for all types
  def get_sync_history(limit: 20)
    history = {}
    
    %w[full bulk health_check].each do |sync_type|
      type_history = Rails.cache.read("subscription_plan_sync_history_#{sync_type}") || []
      history[sync_type] = type_history.first(limit)
    end
    
    # Combine and sort by timestamp
    all_history = history.values.flatten.compact
    all_history.sort_by { |entry| entry[:timestamp] || Time.at(0) }.reverse.first(limit)
  end

  # Get current alerts
  def get_current_alerts
    alerts = []
    
    # Check for sync alert
    sync_alert = Rails.cache.read('subscription_plan_sync_alert')
    alerts << sync_alert if sync_alert
    
    # Check for stale data
    stale_data_alert = check_stale_data
    alerts << stale_data_alert if stale_data_alert
    
    # Check for sync failures
    failure_alert = check_recent_failures
    alerts << failure_alert if failure_alert
    
    alerts
  end

  # Get system health related to subscription plan sync
  def get_system_health
    health = {
      status: 'healthy',
      issues: [],
      last_check: Time.current
    }
    
    # Check if Stripe is accessible
    unless stripe_accessible?
      health[:status] = 'degraded'
      health[:issues] << 'Stripe API not accessible'
    end
    
    # Check for recent sync failures
    if recent_sync_failures?
      health[:status] = 'degraded'
      health[:issues] << 'Recent sync failures detected'
    end
    
    # Check for stale data
    if stale_data_detected?
      health[:status] = health[:status] == 'healthy' ? 'warning' : 'degraded'
      health[:issues] << 'Stale subscription plan data detected'
    end
    
    health
  end

  # Get sync statistics
  def get_sync_statistics
    stats = {
      total_plans: SubscriptionPlan.count,
      synced_plans: SubscriptionPlan.where.not(stripe_price_id: nil).count,
      local_only_plans: SubscriptionPlan.where(stripe_price_id: nil).count,
      recently_synced: SubscriptionPlan.where('last_synced_at > ?', 24.hours.ago).count,
      stale_plans: SubscriptionPlan.where('last_synced_at < ? OR last_synced_at IS NULL', 24.hours.ago).count
    }
    
    # Calculate sync coverage percentage
    stats[:sync_coverage_percentage] = if stats[:total_plans] > 0
      ((stats[:synced_plans].to_f / stats[:total_plans]) * 100).round(1)
    else
      0.0
    end
    
    # Get average sync frequency
    stats[:average_sync_age_hours] = calculate_average_sync_age
    
    stats
  end

  # Trigger manual sync with monitoring
  def trigger_manual_sync(sync_type: 'full', force_sync: false, price_ids: [])
    logger.info "SubscriptionPlanSyncMonitoringService: Triggering manual #{sync_type} sync"
    
    # Enqueue the sync job
    job = SubscriptionPlanSyncJob.perform_later(
      sync_type: sync_type,
      force_sync: force_sync,
      price_ids: price_ids
    )
    
    # Store manual sync trigger info
    Rails.cache.write(
      'subscription_plan_manual_sync_trigger',
      {
        job_id: job.job_id,
        sync_type: sync_type,
        force_sync: force_sync,
        price_ids: price_ids,
        triggered_at: Time.current,
        status: 'queued'
      },
      expires_in: 1.hour
    )
    
    {
      success: true,
      job_id: job.job_id,
      message: "#{sync_type.capitalize} sync queued successfully"
    }
  rescue StandardError => e
    logger.error "SubscriptionPlanSyncMonitoringService: Failed to trigger manual sync: #{e.message}"
    {
      success: false,
      error: e.message
    }
  end

  # Clear old sync data and alerts
  def cleanup_old_data
    logger.info "SubscriptionPlanSyncMonitoringService: Cleaning up old sync data"
    
    # Clear old alerts
    Rails.cache.delete('subscription_plan_sync_alert')
    Rails.cache.delete('subscription_plan_manual_sync_trigger')
    
    # Note: We keep sync history and metrics as they expire automatically
    
    {
      success: true,
      message: 'Old sync data cleaned up successfully'
    }
  end

  private

  # Check if Stripe API is accessible
  def stripe_accessible?
    return false unless Stripe.api_key.present?
    
    Stripe::Price.list(limit: 1)
    true
  rescue StandardError => e
    logger.warn "SubscriptionPlanSyncMonitoringService: Stripe not accessible: #{e.message}"
    false
  end

  # Check for recent sync failures
  def recent_sync_failures?
    %w[full bulk health_check].any? do |sync_type|
      last_sync = get_last_sync_info(sync_type)
      last_sync[:timestamp] && 
        last_sync[:timestamp] > 24.hours.ago && 
        !last_sync[:success]
    end
  end

  # Check for stale data
  def stale_data_detected?
    stale_count = SubscriptionPlan.where(
      'last_synced_at < ? OR last_synced_at IS NULL', 
      48.hours.ago
    ).where.not(stripe_price_id: nil).count
    
    stale_count > 0
  end

  # Check for stale data alert
  def check_stale_data
    stale_plans = SubscriptionPlan.where(
      'last_synced_at < ? OR last_synced_at IS NULL', 
      48.hours.ago
    ).where.not(stripe_price_id: nil)
    
    if stale_plans.count > 0
      {
        type: 'stale_data',
        message: "#{stale_plans.count} subscription plans have not been synced in 48+ hours",
        timestamp: Time.current,
        severity: 'warning',
        details: {
          stale_count: stale_plans.count,
          oldest_sync: stale_plans.minimum(:last_synced_at)
        }
      }
    end
  end

  # Check for recent failures alert
  def check_recent_failures
    recent_failures = []
    
    %w[full bulk health_check].each do |sync_type|
      last_sync = get_last_sync_info(sync_type)
      if last_sync[:timestamp] && 
         last_sync[:timestamp] > 24.hours.ago && 
         !last_sync[:success]
        recent_failures << sync_type
      end
    end
    
    if recent_failures.any?
      {
        type: 'sync_failures',
        message: "Recent sync failures detected: #{recent_failures.join(', ')}",
        timestamp: Time.current,
        severity: 'error',
        details: {
          failed_sync_types: recent_failures
        }
      }
    end
  end

  # Calculate average sync age in hours
  def calculate_average_sync_age
    synced_plans = SubscriptionPlan.where.not(last_synced_at: nil)
    return 0 if synced_plans.empty?
    
    total_age_seconds = synced_plans.sum { |plan| Time.current - plan.last_synced_at }
    average_age_seconds = total_age_seconds / synced_plans.count
    (average_age_seconds / 1.hour).round(1)
  end
end
