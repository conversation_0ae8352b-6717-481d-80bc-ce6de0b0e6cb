# frozen_string_literal: true

# Service for validating subscription plan data and operations
# Ensures data integrity and business rule compliance
class SubscriptionPlanValidationService
  include ActiveModel::Model
  include ActiveModel::Attributes

  # Custom error classes
  class ValidationError < StandardError; end
  class BusinessRuleError < ValidationError; end
  class DataIntegrityError < ValidationError; end

  attr_accessor :logger

  def initialize(logger: Rails.logger)
    @logger = logger
  end

  # Validate Stripe connection and API access
  def validate_stripe_connection
    logger.info 'SubscriptionPlanValidationService: Validating Stripe connection'
    
    unless stripe_configured?
      raise ValidationError, 'Stripe API key is not configured'
    end

    begin
      # Test API access with a simple call
      Stripe::Account.retrieve
      logger.info 'SubscriptionPlanValidationService: Stripe connection validated successfully'
      true
    rescue Stripe::AuthenticationError => e
      error_msg = "Stripe authentication failed: #{e.message}"
      logger.error "SubscriptionPlanValidationService: #{error_msg}"
      raise ValidationError, error_msg
    rescue Stripe::StripeError => e
      error_msg = "Stripe API error: #{e.message}"
      logger.error "SubscriptionPlanValidationService: #{error_msg}"
      raise ValidationError, error_msg
    end
  end

  # Validate plan data from Stripe API
  def validate_plan_data(stripe_price_data)
    logger.info "SubscriptionPlanValidationService: Validating plan data for #{stripe_price_data&.id}"
    
    errors = []

    # Basic presence validations
    errors << 'Stripe price data is missing' if stripe_price_data.nil?
    errors << 'Price ID is missing' if stripe_price_data&.id.blank?
    errors << 'Price type must be recurring' if stripe_price_data&.type != 'recurring'
    errors << 'Unit amount is missing or invalid' if stripe_price_data&.unit_amount.blank? || stripe_price_data.unit_amount <= 0
    errors << 'Currency is missing' if stripe_price_data&.currency.blank?
    errors << 'Recurring data is missing' if stripe_price_data&.recurring.nil?

    # Recurring data validations
    if stripe_price_data&.recurring.present?
      recurring = stripe_price_data.recurring
      errors << 'Billing interval is missing' if recurring.interval.blank?
      errors << 'Billing interval count is invalid' if recurring.interval_count.blank? || recurring.interval_count <= 0
      errors << 'Invalid billing interval' unless %w[day week month year].include?(recurring.interval)
    end

    # Currency validation
    if stripe_price_data&.currency.present?
      errors << 'Unsupported currency' unless %w[usd eur gbp].include?(stripe_price_data.currency.downcase)
    end

    # Product validation
    if stripe_price_data&.product.present?
      if stripe_price_data.product.is_a?(String)
        errors << 'Product ID format is invalid' if stripe_price_data.product.blank?
      elsif stripe_price_data.product.respond_to?(:id)
        errors << 'Product ID is missing' if stripe_price_data.product.id.blank?
      else
        errors << 'Product data format is invalid'
      end
    else
      errors << 'Product information is missing'
    end

    if errors.any?
      error_msg = "Plan data validation failed: #{errors.join(', ')}"
      logger.error "SubscriptionPlanValidationService: #{error_msg}"
      raise DataIntegrityError, error_msg
    end

    logger.info "SubscriptionPlanValidationService: Plan data validation passed for #{stripe_price_data.id}"
    true
  end

  # Check if plan has active subscriptions
  def check_plan_usage(plan)
    logger.info "SubscriptionPlanValidationService: Checking usage for plan #{plan.stripe_price_id}"
    
    # Get active subscriptions count
    active_count = plan.active_subscriptions_count
    
    # Get total revenue (for informational purposes)
    total_revenue = plan.total_revenue
    
    usage_data = {
      active_subscriptions: active_count,
      total_revenue: total_revenue,
      can_be_deleted: plan.can_be_deleted?,
      can_be_deactivated: active_count == 0,
      usage_level: determine_usage_level(active_count)
    }

    logger.info "SubscriptionPlanValidationService: Plan #{plan.stripe_price_id} has #{active_count} active subscriptions"
    usage_data
  end

  # Validate proposed changes to a plan
  def validate_plan_changes(plan, proposed_changes)
    logger.info "SubscriptionPlanValidationService: Validating changes for plan #{plan.stripe_price_id}"
    
    errors = []
    warnings = []

    # Check if trying to modify immutable Stripe fields
    immutable_fields = %w[stripe_price_id amount currency billing_interval billing_interval_count]
    immutable_changes = proposed_changes.keys.map(&:to_s) & immutable_fields
    
    if immutable_changes.any?
      errors << "Cannot modify immutable Stripe fields: #{immutable_changes.join(', ')}"
    end

    # Validate status changes
    if proposed_changes.key?(:active)
      if proposed_changes[:active] == false && plan.active_subscriptions_count > 0
        errors << 'Cannot deactivate plan with active subscriptions'
      elsif proposed_changes[:active] == true && plan.legacy?
        warnings << 'Activating legacy plan may confuse users'
      end
    end

    # Validate legacy status changes
    if proposed_changes.key?(:legacy)
      if proposed_changes[:legacy] == false && plan.legacy? && plan.active_subscriptions_count > 0
        warnings << 'Removing legacy status from plan with active subscriptions'
      end
    end

    # Validate name changes
    if proposed_changes.key?(:name)
      if proposed_changes[:name].blank?
        errors << 'Plan name cannot be blank'
      elsif proposed_changes[:name].length > 255
        errors << 'Plan name is too long (maximum 255 characters)'
      end
    end

    # Validate features changes
    if proposed_changes.key?(:features)
      unless proposed_changes[:features].is_a?(Array)
        errors << 'Features must be an array'
      end
    end

    # Validate metadata changes
    if proposed_changes.key?(:metadata)
      unless proposed_changes[:metadata].is_a?(Hash)
        errors << 'Metadata must be a hash'
      end
    end

    validation_result = {
      valid: errors.empty?,
      errors: errors,
      warnings: warnings,
      safe_to_proceed: errors.empty?
    }

    if errors.any?
      error_msg = "Plan changes validation failed: #{errors.join(', ')}"
      logger.error "SubscriptionPlanValidationService: #{error_msg}"
    else
      logger.info "SubscriptionPlanValidationService: Plan changes validation passed for #{plan.stripe_price_id}"
    end

    if warnings.any?
      logger.warn "SubscriptionPlanValidationService: Warnings for plan #{plan.stripe_price_id}: #{warnings.join(', ')}"
    end

    validation_result
  end

  # Validate plan creation parameters
  def validate_plan_creation(plan_params)
    logger.info 'SubscriptionPlanValidationService: Validating plan creation parameters'
    
    errors = []

    # Required field validations
    required_fields = {
      name: 'Plan name is required',
      amount: 'Plan amount is required',
      billing_interval: 'Billing interval is required'
    }

    required_fields.each do |field, error_message|
      errors << error_message if plan_params[field].blank?
    end

    # Type and format validations
    if plan_params[:amount].present?
      unless plan_params[:amount].is_a?(Integer) && plan_params[:amount] > 0
        errors << 'Amount must be a positive integer (in cents)'
      end
    end

    if plan_params[:billing_interval].present?
      unless %w[day week month year].include?(plan_params[:billing_interval])
        errors << 'Invalid billing interval (must be day, week, month, or year)'
      end
    end

    if plan_params[:billing_interval_count].present?
      unless plan_params[:billing_interval_count].is_a?(Integer) && plan_params[:billing_interval_count] > 0
        errors << 'Billing interval count must be a positive integer'
      end
    end

    if plan_params[:currency].present?
      unless %w[usd eur gbp].include?(plan_params[:currency].downcase)
        errors << 'Unsupported currency (supported: USD, EUR, GBP)'
      end
    end

    # Business rule validations
    if plan_params[:name].present?
      existing_plan = SubscriptionPlan.find_by(name: plan_params[:name])
      if existing_plan
        errors << 'Plan name already exists'
      end
    end

    # Features validation
    if plan_params[:features].present?
      unless plan_params[:features].is_a?(Array)
        errors << 'Features must be an array'
      end
    end

    validation_result = {
      valid: errors.empty?,
      errors: errors,
      safe_to_proceed: errors.empty?
    }

    if errors.any?
      error_msg = "Plan creation validation failed: #{errors.join(', ')}"
      logger.error "SubscriptionPlanValidationService: #{error_msg}"
    else
      logger.info 'SubscriptionPlanValidationService: Plan creation validation passed'
    end

    validation_result
  end

  # Validate data consistency between local and Stripe
  def validate_data_consistency(plan)
    logger.info "SubscriptionPlanValidationService: Validating data consistency for plan #{plan.stripe_price_id}"
    
    begin
      stripe_price = Stripe::Price.retrieve(plan.stripe_price_id)
      
      inconsistencies = []
      
      # Check amount
      if plan.amount != stripe_price.unit_amount
        inconsistencies << "Amount mismatch: local=#{plan.amount}, stripe=#{stripe_price.unit_amount}"
      end
      
      # Check currency
      if plan.currency != stripe_price.currency
        inconsistencies << "Currency mismatch: local=#{plan.currency}, stripe=#{stripe_price.currency}"
      end
      
      # Check billing interval
      if plan.billing_interval != stripe_price.recurring.interval
        inconsistencies << "Billing interval mismatch: local=#{plan.billing_interval}, stripe=#{stripe_price.recurring.interval}"
      end
      
      # Check billing interval count
      if plan.billing_interval_count != stripe_price.recurring.interval_count
        inconsistencies << "Billing interval count mismatch: local=#{plan.billing_interval_count}, stripe=#{stripe_price.recurring.interval_count}"
      end

      consistency_result = {
        consistent: inconsistencies.empty?,
        inconsistencies: inconsistencies,
        needs_sync: inconsistencies.any?,
        last_checked: Time.current
      }

      if inconsistencies.any?
        logger.warn "SubscriptionPlanValidationService: Data inconsistencies found for plan #{plan.stripe_price_id}: #{inconsistencies.join(', ')}"
      else
        logger.info "SubscriptionPlanValidationService: Data consistency validated for plan #{plan.stripe_price_id}"
      end

      consistency_result
    rescue Stripe::InvalidRequestError => e
      error_msg = "Plan not found in Stripe: #{e.message}"
      logger.error "SubscriptionPlanValidationService: #{error_msg}"
      {
        consistent: false,
        inconsistencies: [error_msg],
        needs_sync: true,
        last_checked: Time.current
      }
    rescue Stripe::StripeError => e
      error_msg = "Stripe API error during consistency check: #{e.message}"
      logger.error "SubscriptionPlanValidationService: #{error_msg}"
      raise ValidationError, error_msg
    end
  end

  private

  # Check if Stripe is properly configured
  def stripe_configured?
    Stripe.api_key.present?
  rescue StandardError
    false
  end

  # Determine usage level based on active subscriptions
  def determine_usage_level(active_count)
    case active_count
    when 0
      'unused'
    when 1..10
      'low'
    when 11..100
      'medium'
    else
      'high'
    end
  end
end
