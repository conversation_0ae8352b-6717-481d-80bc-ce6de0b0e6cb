# frozen_string_literal: true

# Service for discovering all available Stripe products and prices
# Provides functionality to find products/prices not yet synced to local subscription plans
# and enables assignment of plans to specific app sections (scout/talent)
class StripeProductDiscoveryService
  include ActiveModel::Model
  include ActiveModel::Attributes

  # Custom error classes
  class DiscoveryError < StandardError; end
  class StripeConnectionError < DiscoveryError; end

  attr_accessor :logger

  def initialize(logger: Rails.logger)
    @logger = logger
  end

  # Class method wrappers for controller convenience
  def self.discover_all_products
    new.discover_all_products
  end

  def self.discover_unsynced_products
    new.discover_unsynced_products
  end

  def self.get_product_details(product_id)
    new.get_product_details(product_id)
  end

  def self.assign_price_to_sections(stripe_price_id, sections)
    new.assign_price_to_sections(stripe_price_id, sections)
  end

  # Discover all available Stripe products and their prices
  def discover_all_products
    logger.info "StripeProductDiscoveryService: Discovering all Stripe products"

    unless stripe_configured?
      error_msg = 'Stripe is not properly configured'
      logger.error "StripeProductDiscoveryService: #{error_msg}"
      return { success: false, errors: [error_msg] }
    end

    begin
      # Fetch all products from Stripe
      products = fetch_all_stripe_products
      
      # Fetch all prices for each product
      products_with_prices = products.map do |product|
        prices = fetch_prices_for_product(product.id)
        
        # Get local sync status for each price
        prices_with_status = prices.map do |price|
          local_plan = SubscriptionPlan.find_by(stripe_price_id: price.id)
          
          {
            id: price.id,
            unit_amount: price.unit_amount,
            currency: price.currency,
            recurring: price.recurring,
            active: price.active,
            created: price.created,
            metadata: price.metadata.to_h,
            local_plan: local_plan,
            synced: !local_plan.nil?,
            available_for_scout: local_plan&.available_for_scout || false,
            available_for_talent: local_plan&.available_for_talent || false
          }
        end

        {
          id: product.id,
          name: product.name,
          description: product.description,
          active: product.active,
          created: product.created,
          metadata: product.metadata.to_h,
          marketing_features: product.marketing_features || [],
          prices: prices_with_status
        }
      end

      logger.info "StripeProductDiscoveryService: Successfully discovered #{products_with_prices.count} products"
      
      {
        success: true,
        products: products_with_prices,
        total_products: products_with_prices.count,
        total_prices: products_with_prices.sum { |p| p[:prices].count }
      }

    rescue Stripe::StripeError => e
      error_msg = "Stripe API error during discovery: #{e.message}"
      logger.error "StripeProductDiscoveryService: #{error_msg}"
      { success: false, errors: [error_msg] }
    rescue StandardError => e
      error_msg = "Unexpected error during discovery: #{e.message}"
      logger.error "StripeProductDiscoveryService: #{error_msg}"
      { success: false, errors: [error_msg] }
    end
  end

  # Discover only products/prices that are not yet synced to local plans
  def discover_unsynced_products
    logger.info "StripeProductDiscoveryService: Discovering unsynced Stripe products"

    result = discover_all_products
    return result unless result[:success]

    # Filter to only include products that have unsynced prices
    unsynced_products = result[:products].map do |product|
      unsynced_prices = product[:prices].reject { |price| price[:synced] }
      
      if unsynced_prices.any?
        product.merge(prices: unsynced_prices)
      else
        nil
      end
    end.compact

    logger.info "StripeProductDiscoveryService: Found #{unsynced_products.count} products with unsynced prices"

    {
      success: true,
      products: unsynced_products,
      total_products: unsynced_products.count,
      total_unsynced_prices: unsynced_products.sum { |p| p[:prices].count }
    }
  end

  # Get detailed information about a specific product
  def get_product_details(product_id)
    logger.info "StripeProductDiscoveryService: Getting details for product #{product_id}"

    unless stripe_configured?
      error_msg = 'Stripe is not properly configured'
      return { success: false, errors: [error_msg] }
    end

    begin
      product = Stripe::Product.retrieve(product_id)
      prices = fetch_prices_for_product(product_id)

      # Get local sync status for each price
      prices_with_status = prices.map do |price|
        local_plan = SubscriptionPlan.find_by(stripe_price_id: price.id)
        
        {
          id: price.id,
          unit_amount: price.unit_amount,
          currency: price.currency,
          recurring: price.recurring,
          active: price.active,
          created: price.created,
          metadata: price.metadata.to_h,
          local_plan: local_plan,
          synced: !local_plan.nil?,
          available_for_scout: local_plan&.available_for_scout || false,
          available_for_talent: local_plan&.available_for_talent || false
        }
      end

      {
        success: true,
        product: {
          id: product.id,
          name: product.name,
          description: product.description,
          active: product.active,
          created: product.created,
          metadata: product.metadata.to_h,
          marketing_features: product.marketing_features || [],
          prices: prices_with_status
        }
      }

    rescue Stripe::InvalidRequestError => e
      error_msg = "Invalid product ID #{product_id}: #{e.message}"
      logger.error "StripeProductDiscoveryService: #{error_msg}"
      { success: false, errors: [error_msg] }
    rescue Stripe::StripeError => e
      error_msg = "Stripe API error for product #{product_id}: #{e.message}"
      logger.error "StripeProductDiscoveryService: #{error_msg}"
      { success: false, errors: [error_msg] }
    rescue StandardError => e
      error_msg = "Unexpected error getting product #{product_id}: #{e.message}"
      logger.error "StripeProductDiscoveryService: #{error_msg}"
      { success: false, errors: [error_msg] }
    end
  end

  # Assign a Stripe price to specific app sections (scout/talent)
  def assign_price_to_sections(stripe_price_id, sections)
    logger.info "StripeProductDiscoveryService: Assigning price #{stripe_price_id} to sections: #{sections}"

    unless stripe_configured?
      error_msg = 'Stripe is not properly configured'
      return { success: false, errors: [error_msg] }
    end

    begin
      # Validate sections parameter
      valid_sections = %w[scout talent]
      invalid_sections = sections - valid_sections
      if invalid_sections.any?
        error_msg = "Invalid sections: #{invalid_sections.join(', ')}. Valid sections are: #{valid_sections.join(', ')}"
        return { success: false, errors: [error_msg] }
      end

      # Check if plan already exists locally
      existing_plan = SubscriptionPlan.find_by(stripe_price_id: stripe_price_id)
      
      if existing_plan
        # Update existing plan's section assignments
        existing_plan.update!(
          available_for_scout: sections.include?('scout'),
          available_for_talent: sections.include?('talent')
        )
        
        logger.info "StripeProductDiscoveryService: Updated existing plan #{stripe_price_id} section assignments"
        
        {
          success: true,
          action: :updated,
          plan: existing_plan,
          sections: sections
        }
      else
        # Sync the plan from Stripe first, then update section assignments
        sync_result = SubscriptionPlanSyncService.sync_plan_from_stripe(stripe_price_id)
        
        if sync_result[:success]
          # Find the newly created plan and update section assignments
          new_plan = SubscriptionPlan.find_by(stripe_price_id: stripe_price_id)
          
          if new_plan
            new_plan.update!(
              available_for_scout: sections.include?('scout'),
              available_for_talent: sections.include?('talent')
            )
            
            logger.info "StripeProductDiscoveryService: Created and assigned plan #{stripe_price_id} to sections"
            
            {
              success: true,
              action: :created_and_assigned,
              plan: new_plan,
              sections: sections
            }
          else
            error_msg = "Plan was synced but could not be found locally"
            { success: false, errors: [error_msg] }
          end
        else
          error_msg = "Failed to sync plan from Stripe: #{sync_result[:errors].join(', ')}"
          { success: false, errors: [error_msg] }
        end
      end

    rescue Stripe::StripeError => e
      error_msg = "Stripe API error for price #{stripe_price_id}: #{e.message}"
      logger.error "StripeProductDiscoveryService: #{error_msg}"
      { success: false, errors: [error_msg] }
    rescue StandardError => e
      error_msg = "Unexpected error assigning price #{stripe_price_id}: #{e.message}"
      logger.error "StripeProductDiscoveryService: #{error_msg}"
      { success: false, errors: [error_msg] }
    end
  end

  private

  # Check if Stripe is properly configured
  def stripe_configured?
    Stripe.api_key.present?
  rescue StandardError
    false
  end

  # Fetch all products from Stripe
  def fetch_all_stripe_products
    products = []
    
    Stripe::Product.list(limit: 100).auto_paging_each do |product|
      products << product
    end
    
    logger.info "StripeProductDiscoveryService: Fetched #{products.count} products from Stripe"
    products
  end

  # Fetch all prices for a specific product
  def fetch_prices_for_product(product_id)
    prices = []

    Stripe::Price.list(product: product_id, limit: 100).auto_paging_each do |price|
      # Include both recurring (subscription) and one_time prices
      # This allows the system to handle both subscription plans and one-time purchases like job listings
      prices << price if %w[recurring one_time].include?(price.type)
    end

    logger.debug "StripeProductDiscoveryService: Fetched #{prices.count} prices (recurring and one-time) for product #{product_id}"
    prices
  end
end
