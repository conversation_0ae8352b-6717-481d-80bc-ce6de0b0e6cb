# frozen_string_literal: true

# Service for fetching subscription pricing data
# Now uses SubscriptionPlan model as primary source with Stripe API and fallback support
class StripePricingService
  # Cache expiration time (1 hour)
  CACHE_EXPIRATION = 1.hour

  # Legacy fallback pricing when all other sources fail
  # TODO: Remove after full migration to SubscriptionPlan model
  FALLBACK_PRICING = {
    'price_1R9Q55DYYVPVcCCrWQOwsKmT' => { amount: 9900, currency: 'usd' }, # Standard plan - $99.00 annually
    'price_1R9Q66DYYVPVcCCrnqiXNafF' => { amount: 9900, currency: 'usd' }  # Premium plan - $99.00 monthly (legacy)
  }.freeze

  class << self
    # Fetch price amount for a given Stripe price ID
    # Returns formatted amount string (e.g., "$99.00") or fallback value
    # Priority: 1) SubscriptionPlan model, 2) Cache, 3) Stripe API, 4) Fallback constants
    def fetch_price_amount(price_id)
      return 'Unknown Plan' unless price_id.present?

      # 1. Try SubscriptionPlan model first (primary source)
      subscription_plan = SubscriptionPlan.find_by(stripe_price_id: price_id)
      if subscription_plan
        Rails.logger.debug "StripePricingService: Using SubscriptionPlan model for #{price_id}"
        return subscription_plan.formatted_amount
      end

      # 2. Try to get from cache
      cached_price = Rails.cache.read(cache_key(price_id))
      if cached_price
        Rails.logger.debug "StripePricingService: Using cached price for #{price_id}: #{cached_price}"
        return format_amount(cached_price[:amount], cached_price[:currency])
      end

      # 3. Fetch from Stripe API
      price_data = fetch_from_stripe(price_id)
      if price_data
        # Cache the result
        Rails.cache.write(cache_key(price_id), price_data, expires_in: CACHE_EXPIRATION)
        Rails.logger.info "StripePricingService: Fetched and cached price for #{price_id}: #{price_data}"
        return format_amount(price_data[:amount], price_data[:currency])
      end

      # 4. Fall back to hardcoded values (legacy)
      fallback_data = FALLBACK_PRICING[price_id]
      if fallback_data
        Rails.logger.warn "StripePricingService: Using legacy fallback pricing for #{price_id}: #{fallback_data}"
        return format_amount(fallback_data[:amount], fallback_data[:currency])
      end

      # Ultimate fallback
      Rails.logger.error "StripePricingService: No pricing data available for #{price_id}"
      'See billing portal'
    end

    # Refresh cache for a specific price ID (used by webhooks)
    def refresh_price_cache(price_id)
      return unless price_id.present?

      Rails.logger.info "StripePricingService: Refreshing cache for price #{price_id}"
      
      # Remove from cache
      Rails.cache.delete(cache_key(price_id))
      
      # Fetch fresh data
      price_data = fetch_from_stripe(price_id)
      if price_data
        Rails.cache.write(cache_key(price_id), price_data, expires_in: CACHE_EXPIRATION)
        Rails.logger.info "StripePricingService: Successfully refreshed cache for #{price_id}: #{price_data}"
      else
        Rails.logger.warn "StripePricingService: Failed to refresh cache for #{price_id}, Stripe API unavailable"
      end
    end

    # Invalidate cache for a specific price ID (used by webhooks)
    def invalidate_price_cache(price_id)
      return unless price_id.present?

      Rails.logger.info "StripePricingService: Invalidating cache for price #{price_id}"
      Rails.cache.delete(cache_key(price_id))
    end

    # Invalidate all pricing cache (useful for bulk operations)
    def invalidate_all_cache
      Rails.logger.info "StripePricingService: Invalidating all pricing cache"

      # Invalidate cache for all known plans in database
      SubscriptionPlan.pluck(:stripe_price_id).each { |price_id| invalidate_price_cache(price_id) }

      # Also invalidate legacy fallback plans (for backward compatibility)
      FALLBACK_PRICING.keys.each { |price_id| invalidate_price_cache(price_id) }
    end

    # Sync plan data from SubscriptionPlan model to cache
    def sync_plan_to_cache(price_id)
      return unless price_id.present?

      subscription_plan = SubscriptionPlan.find_by(stripe_price_id: price_id)
      return unless subscription_plan

      Rails.logger.info "StripePricingService: Syncing plan #{price_id} from SubscriptionPlan model to cache"

      price_data = {
        amount: subscription_plan.amount,
        currency: subscription_plan.currency,
        recurring: {
          interval: subscription_plan.billing_interval,
          interval_count: subscription_plan.billing_interval_count
        }
      }

      Rails.cache.write(cache_key(price_id), price_data, expires_in: CACHE_EXPIRATION)
      Rails.logger.info "StripePricingService: Successfully synced plan #{price_id} to cache"
    end

    # Get price data without formatting (useful for calculations)
    # Priority: 1) SubscriptionPlan model, 2) Cache, 3) Stripe API, 4) Fallback constants
    def fetch_price_data(price_id)
      return nil unless price_id.present?

      # 1. Try SubscriptionPlan model first
      subscription_plan = SubscriptionPlan.find_by(stripe_price_id: price_id)
      if subscription_plan
        Rails.logger.debug "StripePricingService: Using SubscriptionPlan model data for #{price_id}"
        return {
          amount: subscription_plan.amount,
          currency: subscription_plan.currency,
          recurring: {
            interval: subscription_plan.billing_interval,
            interval_count: subscription_plan.billing_interval_count
          },
          plan_name: subscription_plan.name,
          plan_description: subscription_plan.description,
          features: subscription_plan.features,
          active: subscription_plan.active?,
          legacy: subscription_plan.legacy?
        }
      end

      # 2. Try cache
      cached_price = Rails.cache.read(cache_key(price_id))
      return cached_price if cached_price

      # 3. Fetch from Stripe
      price_data = fetch_from_stripe(price_id)
      if price_data
        Rails.cache.write(cache_key(price_id), price_data, expires_in: CACHE_EXPIRATION)
        return price_data
      end

      # 4. Return fallback data
      FALLBACK_PRICING[price_id]
    end

    # Check if a price ID is supported
    # Priority: 1) SubscriptionPlan model, 2) Fallback constants
    def supported_price?(price_id)
      return false unless price_id.present?

      # Check SubscriptionPlan model first
      return true if SubscriptionPlan.exists?(stripe_price_id: price_id)

      # Fall back to legacy constants
      FALLBACK_PRICING.key?(price_id)
    end

    # Get all available plans for signup (active, non-legacy plans)
    def available_plans_for_signup
      SubscriptionPlan.available_for_signup.by_amount
    end

    # Get plan by price ID with full details
    def find_plan(price_id)
      SubscriptionPlan.find_by(stripe_price_id: price_id)
    end

    # Get plan name for display
    def plan_name(price_id)
      plan = find_plan(price_id)
      return plan.name if plan

      # Legacy fallback based on known price IDs
      case price_id
      when 'price_1R9Q55DYYVPVcCCrWQOwsKmT'
        'Standard'
      when 'price_1R9Q66DYYVPVcCCrnqiXNafF'
        'Premium (Legacy)'
      else
        'Unknown Plan'
      end
    end

    private

    # Generate cache key for a price ID
    def cache_key(price_id)
      "stripe_price_#{price_id}"
    end

    # Fetch price data from Stripe API
    def fetch_from_stripe(price_id)
      return nil unless stripe_configured?

      begin
        # Use Stripe gem directly to fetch price
        price = Stripe::Price.retrieve(price_id)
        
        {
          amount: price.unit_amount,
          currency: price.currency,
          recurring: price.recurring
        }
      rescue Stripe::StripeError => e
        Rails.logger.error "StripePricingService: Stripe API error for #{price_id}: #{e.message}"
        nil
      rescue StandardError => e
        Rails.logger.error "StripePricingService: Unexpected error fetching price #{price_id}: #{e.message}"
        nil
      end
    end

    # Check if Stripe is properly configured
    def stripe_configured?
      Stripe.api_key.present?
    rescue StandardError
      false
    end

    # Format amount for display
    def format_amount(amount_cents, currency)
      return 'Invalid amount' unless amount_cents.is_a?(Integer) && currency.present?

      # Convert cents to dollars
      amount_dollars = amount_cents / 100.0
      
      # Format based on currency
      case currency.downcase
      when 'usd'
        "$#{'%.2f' % amount_dollars}"
      else
        "#{amount_dollars} #{currency.upcase}"
      end
    end
  end
end
