# frozen_string_literal: true

# Service for migrating from hardcoded subscription plans to database-driven plans
# Handles the transition period and ensures data integrity during migration
class SubscriptionPlanMigrationService
  include ActiveModel::Model
  include ActiveModel::Attributes

  # Custom error classes
  class MigrationError < StandardError; end
  class ValidationError < MigrationError; end

  attr_accessor :logger

  def initialize(logger: Rails.logger)
    @logger = logger
  end

  # Migrate hardcoded plans to database
  def migrate_hardcoded_plans
    logger.info 'SubscriptionPlanMigrationService: Starting migration of hardcoded plans'
    
    migration_results = { created: 0, updated: 0, errors: [] }
    
    hardcoded_plans.each do |plan_data|
      begin
        result = migrate_single_plan(plan_data)
        migration_results[result] += 1
        logger.info "SubscriptionPlanMigrationService: #{result.capitalize} plan #{plan_data[:stripe_price_id]}"
      rescue StandardError => e
        error_msg = "Failed to migrate plan #{plan_data[:stripe_price_id]}: #{e.message}"
        logger.error "SubscriptionPlanMigrationService: #{error_msg}"
        migration_results[:errors] << error_msg
      end
    end

    logger.info "SubscriptionPlanMigrationService: Migration completed. Created: #{migration_results[:created]}, Updated: #{migration_results[:updated]}, Errors: #{migration_results[:errors].count}"
    migration_results
  end

  # Validate migration was successful
  def validate_migration
    logger.info 'SubscriptionPlanMigrationService: Validating migration results'
    
    validation_results = {
      all_plans_migrated: true,
      missing_plans: [],
      data_inconsistencies: [],
      total_plans: hardcoded_plans.count,
      migrated_plans: 0
    }

    hardcoded_plans.each do |plan_data|
      plan = SubscriptionPlan.find_by(stripe_price_id: plan_data[:stripe_price_id])
      
      if plan.nil?
        validation_results[:all_plans_migrated] = false
        validation_results[:missing_plans] << plan_data[:stripe_price_id]
      else
        validation_results[:migrated_plans] += 1
        
        # Check data consistency
        inconsistencies = validate_plan_data_consistency(plan, plan_data)
        if inconsistencies.any?
          validation_results[:data_inconsistencies] << {
            plan_id: plan_data[:stripe_price_id],
            issues: inconsistencies
          }
        end
      end
    end

    if validation_results[:all_plans_migrated] && validation_results[:data_inconsistencies].empty?
      logger.info 'SubscriptionPlanMigrationService: Migration validation passed'
    else
      logger.error "SubscriptionPlanMigrationService: Migration validation failed. Missing: #{validation_results[:missing_plans].count}, Inconsistencies: #{validation_results[:data_inconsistencies].count}"
    end

    validation_results
  end

  # Find hardcoded references in codebase (for manual review)
  def find_hardcoded_references
    logger.info 'SubscriptionPlanMigrationService: Scanning for hardcoded price ID references'
    
    price_ids = hardcoded_plans.map { |plan| plan[:stripe_price_id] }
    references = []

    # Files to scan for hardcoded references
    files_to_scan = [
      'app/helpers/subscription_helper.rb',
      'app/services/stripe_pricing_service.rb',
      'app/controllers/talent/subscriptions_controller.rb',
      'app/controllers/talent/settings/subscriptions_controller.rb',
      'app/views/talent/settings/subscriptions/show.html.erb',
      'app/models/pay/webhooks/stripe/customer_subscription_updated.rb',
      'app/models/pay/webhooks/stripe/invoice_payment_succeeded.rb',
      'app/models/pay/webhooks/stripe/customer_subscription_deleted.rb'
    ]

    files_to_scan.each do |file_path|
      if File.exist?(Rails.root.join(file_path))
        content = File.read(Rails.root.join(file_path))
        
        price_ids.each do |price_id|
          if content.include?(price_id)
            references << {
              file: file_path,
              price_id: price_id,
              line_numbers: find_line_numbers(content, price_id)
            }
          end
        end
      end
    end

    logger.info "SubscriptionPlanMigrationService: Found #{references.count} hardcoded references"
    references
  end

  # Generate migration report
  def generate_migration_report
    logger.info 'SubscriptionPlanMigrationService: Generating migration report'
    
    migration_validation = validate_migration
    hardcoded_references = find_hardcoded_references
    
    report = {
      timestamp: Time.current,
      migration_status: migration_validation,
      hardcoded_references: hardcoded_references,
      recommendations: generate_recommendations(migration_validation, hardcoded_references),
      next_steps: generate_next_steps(migration_validation, hardcoded_references)
    }

    logger.info 'SubscriptionPlanMigrationService: Migration report generated'
    report
  end

  # Rollback migration (if needed)
  def rollback_migration
    logger.info 'SubscriptionPlanMigrationService: Rolling back migration'
    
    rollback_results = { deleted: 0, errors: [] }
    
    hardcoded_plans.each do |plan_data|
      begin
        plan = SubscriptionPlan.find_by(stripe_price_id: plan_data[:stripe_price_id])
        if plan
          plan.destroy!
          rollback_results[:deleted] += 1
          logger.info "SubscriptionPlanMigrationService: Deleted plan #{plan_data[:stripe_price_id]}"
        end
      rescue StandardError => e
        error_msg = "Failed to delete plan #{plan_data[:stripe_price_id]}: #{e.message}"
        logger.error "SubscriptionPlanMigrationService: #{error_msg}"
        rollback_results[:errors] << error_msg
      end
    end

    logger.info "SubscriptionPlanMigrationService: Rollback completed. Deleted: #{rollback_results[:deleted]}, Errors: #{rollback_results[:errors].count}"
    rollback_results
  end

  private

  # Hardcoded plan data from existing system
  def hardcoded_plans
    [
      {
        stripe_price_id: 'price_1R9Q55DYYVPVcCCrWQOwsKmT',
        name: 'Standard',
        description: 'Standard subscription plan with all essential features',
        amount: 9900, # $99.00 in cents
        currency: 'usd',
        billing_interval: 'year',
        billing_interval_count: 1,
        features: [
          'Access to all job listings',
          'Direct messaging with scouts',
          'Profile visibility',
          'Standard support'
        ],
        active: true,
        legacy: false
      },
      {
        stripe_price_id: 'price_1R9Q66DYYVPVcCCrnqiXNafF',
        name: 'Premium',
        description: 'Premium subscription plan with advanced features (Legacy)',
        amount: 9900, # $99.00 in cents
        currency: 'usd',
        billing_interval: 'month',
        billing_interval_count: 1,
        features: [
          'All Standard features',
          'Premium support',
          'Advanced analytics',
          'Priority listing'
        ],
        active: false, # No longer available for new subscriptions
        legacy: true
      }
    ]
  end

  # Migrate a single plan
  def migrate_single_plan(plan_data)
    existing_plan = SubscriptionPlan.find_by(stripe_price_id: plan_data[:stripe_price_id])
    
    if existing_plan
      # Update existing plan
      existing_plan.update!(
        name: plan_data[:name],
        description: plan_data[:description],
        amount: plan_data[:amount],
        currency: plan_data[:currency],
        billing_interval: plan_data[:billing_interval],
        billing_interval_count: plan_data[:billing_interval_count],
        features: plan_data[:features],
        active: plan_data[:active],
        legacy: plan_data[:legacy]
      )
      :updated
    else
      # Create new plan
      SubscriptionPlan.create!(plan_data)
      :created
    end
  end

  # Validate plan data consistency
  def validate_plan_data_consistency(plan, expected_data)
    inconsistencies = []
    
    # Check each field
    expected_data.each do |field, expected_value|
      actual_value = plan.send(field)
      
      if actual_value != expected_value
        inconsistencies << "#{field}: expected #{expected_value}, got #{actual_value}"
      end
    end
    
    inconsistencies
  end

  # Find line numbers where price ID appears
  def find_line_numbers(content, price_id)
    line_numbers = []
    content.lines.each_with_index do |line, index|
      if line.include?(price_id)
        line_numbers << index + 1
      end
    end
    line_numbers
  end

  # Generate recommendations based on migration status
  def generate_recommendations(migration_validation, hardcoded_references)
    recommendations = []
    
    if migration_validation[:missing_plans].any?
      recommendations << "Re-run migration for missing plans: #{migration_validation[:missing_plans].join(', ')}"
    end
    
    if migration_validation[:data_inconsistencies].any?
      recommendations << "Review and fix data inconsistencies in migrated plans"
    end
    
    if hardcoded_references.any?
      recommendations << "Update #{hardcoded_references.count} hardcoded references to use SubscriptionPlan model"
    end
    
    if recommendations.empty?
      recommendations << "Migration appears successful. Proceed with updating codebase references."
    end
    
    recommendations
  end

  # Generate next steps based on migration status
  def generate_next_steps(migration_validation, hardcoded_references)
    next_steps = []
    
    if migration_validation[:all_plans_migrated] && migration_validation[:data_inconsistencies].empty?
      next_steps << "Update SubscriptionHelper to use database lookups"
      next_steps << "Update StripePricingService to use SubscriptionPlan model"
      next_steps << "Update subscription controllers and views"
      next_steps << "Update webhook handlers"
      next_steps << "Remove hardcoded constants"
    else
      next_steps << "Fix migration issues before proceeding"
      next_steps << "Re-run migration validation"
    end
    
    next_steps
  end
end
