import { Controller } from "@hotwired/stimulus";
import {
  FORM_CATEGORIES,
  STEP_DEFINITIONS,
} from "../form_wizard/form_config.js";

export default class extends Controller {
  static targets = [
    "step",
    "stepIndicator",
    "stepTitle",
    "checkIcon",
    "currentIndicator",
    "currentDot",
    "futureDot",
    "categorySteps",
  ];

  static values = {
    currentStep: { type: Number, default: 0 },
    totalSteps: { type: Number, default: 8 },
    selectedCategory: { type: String, default: "" },
  };

  connect() {
    console.log("VerticalProgressController connected");
    console.log("Initial step targets:", this.stepTargets.length);
    this.initializeSteps();

    // Bind event handlers to preserve 'this' context
    this.boundHandleStepChanged = this.handleStepChanged.bind(this);
    this.boundHandleCategoryChanged = this.handleCategoryChanged.bind(this);
    this.boundHandleStepClick = this.handleStepClick.bind(this);

    // Listen for form wizard events on document
    document.addEventListener(
      "form-wizard:stepChanged",
      this.boundHandleStepChanged
    );
    document.addEventListener(
      "form-wizard:categoryChanged",
      this.boundHandleCategoryChanged
    );

    // Add click handlers to steps
    this.addStepClickHandlers();

    // Initial display update
    this.updateDisplay();
  }

  disconnect() {
    document.removeEventListener(
      "form-wizard:stepChanged",
      this.boundHandleStepChanged
    );
    document.removeEventListener(
      "form-wizard:categoryChanged",
      this.boundHandleCategoryChanged
    );

    // Remove click handlers
    this.removeStepClickHandlers();
  }

  initializeSteps() {
    // Set up initial step numbering
    this.updateStepNumbers();
  }

  addStepClickHandlers() {
    console.log(
      "VerticalProgress: Adding click handlers to",
      this.stepTargets.length,
      "steps"
    );
    this.stepTargets.forEach((step, index) => {
      const stepElement = step.querySelector(".group");
      if (stepElement) {
        stepElement.style.cursor = "pointer";
        stepElement.addEventListener("click", this.boundHandleStepClick);
        stepElement.dataset.stepIndex = index;
        console.log(
          "VerticalProgress: Added click handler to step",
          index,
          step.dataset.stepKey
        );
      }
    });
  }

  removeStepClickHandlers() {
    this.stepTargets.forEach((step) => {
      const stepElement = step.querySelector(".group");
      if (stepElement) {
        stepElement.removeEventListener("click", this.boundHandleStepClick);
        stepElement.style.cursor = "";
        delete stepElement.dataset.stepIndex;
      }
    });
  }

  handleStepClick(event) {
    event.preventDefault();
    const stepIndex = parseInt(event.currentTarget.dataset.stepIndex);

    console.log("VerticalProgress: Step clicked", {
      stepIndex,
      currentStepValue: this.currentStepValue,
      isClickable: stepIndex <= this.currentStepValue,
    });

    if (isNaN(stepIndex)) {
      console.warn(
        "Invalid step index:",
        event.currentTarget.dataset.stepIndex
      );
      return;
    }

    // Only allow navigation to completed steps or current step
    if (stepIndex <= this.currentStepValue) {
      this.navigateToStep(stepIndex);
    } else {
      console.log("VerticalProgress: Step not clickable - future step");
    }
  }

  navigateToStep(stepIndex) {
    console.log("VerticalProgress: Attempting to navigate to step", stepIndex);

    // Find the form wizard controller using a more direct approach
    const formElement = document.querySelector(
      '[data-controller*="form-wizard"]'
    );

    if (!formElement) {
      console.warn("Form wizard element not found");
      return;
    }

    // Dispatch a custom event that the form wizard controller can listen to
    const navigationEvent = new CustomEvent("vertical-progress:navigate", {
      detail: { targetStep: stepIndex },
      bubbles: true,
    });

    formElement.dispatchEvent(navigationEvent);
  }

  handleStepChanged(event) {
    console.log("VerticalProgress: Step changed", event.detail);
    const { currentStep, stepKey, totalSteps } = event.detail;
    this.currentStepValue = currentStep;
    this.totalStepsValue = totalSteps;
    this.updateDisplay();
  }

  handleCategoryChanged(event) {
    console.log("VerticalProgress: Category changed", event.detail);
    const { category } = event.detail;
    this.selectedCategoryValue = category;
    this.updateCategorySteps();
    this.updateStepNumbers();
  }

  updateCategorySteps() {
    const categoryStepsContainer = this.categoryStepsTarget;
    categoryStepsContainer.innerHTML = "";

    if (
      this.selectedCategoryValue &&
      FORM_CATEGORIES[this.selectedCategoryValue]
    ) {
      const categoryConfig = FORM_CATEGORIES[this.selectedCategoryValue];
      const steps = categoryConfig.steps;

      steps.forEach((stepKey, index) => {
        const stepDef = STEP_DEFINITIONS[stepKey];
        if (stepDef) {
          const stepElement = this.createCategoryStepElement(
            stepKey,
            stepDef,
            index
          );
          categoryStepsContainer.appendChild(stepElement);
        }
      });
    }

    // Update step numbers after adding category steps
    this.updateStepNumbers();

    // Re-add click handlers to all steps including new category steps
    this.addStepClickHandlers();
  }

  createCategoryStepElement(stepKey, stepDef, index) {
    const li = document.createElement("li");
    li.setAttribute("data-vertical-progress-target", "step");
    li.setAttribute("data-step-key", stepKey);
    // Add consistent spacing between category steps
    li.className = "mt-6";

    li.innerHTML = `
      <div class="flex items-center group ml-6">
        <span data-vertical-progress-target="stepIndicator"
              class="relative flex items-center justify-center flex-shrink-0 w-5 h-5">
          <!-- Checkmark icon (hidden by default) -->
          <svg data-vertical-progress-target="checkIcon"
               class="hidden w-full h-full group-hover:text-[#6100FF]"
               style="color: #6100FF;"
               viewBox="0 0 20 20"
               fill="currentColor"
               aria-hidden="true">
            <path fill-rule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z"
                  clip-rule="evenodd" />
          </svg>
          <!-- Current step indicator (hidden by default) -->
          <span data-vertical-progress-target="currentIndicator"
                class="absolute hidden h-4 w-4 rounded-full"
                style="background-color: rgba(97, 0, 255, 0.2);"></span>
          <span data-vertical-progress-target="currentDot"
                class="relative hidden h-2 w-2 rounded-full"
                style="background-color: #6100FF;"></span>
          <!-- Future step dot (visible by default) -->
          <div data-vertical-progress-target="futureDot"
               class="w-2 h-2 bg-stone-300 rounded-full group-hover:bg-stone-400"></div>
        </span>
        <span data-vertical-progress-target="stepTitle"
              class="ml-3 text-xs font-medium text-gray-500 group-hover:text-gray-900">
          ${stepDef.name}
        </span>
      </div>
    `;

    return li;
  }

  updateStepNumbers() {
    // Update total steps count
    this.totalStepsValue = this.stepTargets.length;
  }

  updateDisplay() {
    const allSteps = this.stepTargets;
    const currentStepIndex = this.currentStepValue;

    console.log("VerticalProgress updateDisplay:", {
      currentStepIndex,
      totalSteps: allSteps.length,
      stepKeys: allSteps.map((step) => step.dataset.stepKey),
    });

    if (allSteps.length === 0) {
      return;
    }

    allSteps.forEach((step, index) => {
      const checkIcon = step.querySelector(
        '[data-vertical-progress-target="checkIcon"]'
      );
      const currentIndicator = step.querySelector(
        '[data-vertical-progress-target="currentIndicator"]'
      );
      const currentDot = step.querySelector(
        '[data-vertical-progress-target="currentDot"]'
      );
      const futureDot = step.querySelector(
        '[data-vertical-progress-target="futureDot"]'
      );
      const title = step.querySelector(
        '[data-vertical-progress-target="stepTitle"]'
      );
      const groupElement = step.querySelector(".group");

      if (
        !checkIcon ||
        !currentIndicator ||
        !currentDot ||
        !futureDot ||
        !title ||
        !groupElement
      )
        return;

      // Update cursor style based on clickability
      const isClickable = index <= currentStepIndex;
      groupElement.style.cursor = isClickable ? "pointer" : "default";

      if (index < currentStepIndex) {
        // Completed step - show checkmark
        checkIcon.classList.remove("hidden");
        currentIndicator.classList.add("hidden");
        currentDot.classList.add("hidden");
        currentDot.classList.remove("block");
        futureDot.classList.add("hidden");
        title.className =
          "ml-3 text-xs font-medium text-gray-900 group-hover:text-gray-900";
        title.style.color = "";
      } else if (index === currentStepIndex) {
        // Current step - show nested circles
        checkIcon.classList.add("hidden");
        currentIndicator.classList.remove("hidden");
        currentDot.classList.remove("hidden");
        currentDot.classList.add("block");
        futureDot.classList.add("hidden");
        title.className = "ml-3 text-sm font-medium";
        title.style.color = "#6100FF";
      } else {
        // Future step - show gray dot
        checkIcon.classList.add("hidden");
        currentIndicator.classList.add("hidden");
        currentDot.classList.add("hidden");
        currentDot.classList.remove("block");
        futureDot.classList.remove("hidden");
        title.className =
          "ml-3 text-xs font-medium text-gray-500 group-hover:text-gray-900";
        title.style.color = "";
      }
    });
  }

  // Value change handlers
  currentStepValueChanged() {
    this.updateDisplay();
  }

  selectedCategoryValueChanged() {
    this.updateCategorySteps();
  }

  totalStepsValueChanged() {
    // Total steps changed - could trigger other updates if needed
  }
}
