import { Controller } from "@hotwired/stimulus";

// Connects to data-controller="price-sync"
export default class extends Controller {
  static targets = [
    "syncButton",
    "syncStatus",
    "syncLoading",
    "syncMessage",
    "assignmentForm",
  ];
  static values = {
    syncUrl: String,
    csrfToken: String,
  };

  connect() {
    // Add event listeners to all sync buttons
    this.element.addEventListener("click", this.handleSyncClick.bind(this));
  }

  handleSyncClick(event) {
    const button = event.target.closest(".sync-price-btn");
    if (!button) return;

    event.preventDefault();

    const priceId = button.dataset.priceId;
    const action = button.dataset.action;

    this.syncPrice(priceId, button);
  }

  async syncPrice(priceId, button) {
    const priceContainer = button.closest("[data-price-id]");
    if (!priceContainer) {
      console.error("Price container not found");
      return;
    }

    const syncStatusContainer = priceContainer.querySelector(
      ".sync-status-container"
    );
    const syncLoading = priceContainer.querySelector(".sync-loading");
    const syncMessage = priceContainer.querySelector(".sync-message");
    const assignmentForm = priceContainer.querySelector("form");

    try {
      // Show loading state
      if (syncStatusContainer) syncStatusContainer.classList.add("hidden");
      if (syncLoading) syncLoading.classList.remove("hidden");
      if (syncMessage) syncMessage.classList.add("hidden");
      button.disabled = true;

      // Make sync request
      const response = await fetch(this.syncUrlValue, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRF-Token": this.csrfTokenValue,
          Accept: "application/json",
        },
        body: JSON.stringify({
          stripe_price_id: priceId,
        }),
      });

      const result = await response.json();

      if (result.success) {
        // Update UI to show synced state
        this.updateSyncedState(priceContainer, result);

        // Show success message
        this.showMessage(syncMessage, result.message, "success");

        // Enable assignment form if it was disabled
        if (assignmentForm) {
          const submitButton = assignmentForm.querySelector(
            'button[type="submit"]'
          );
          if (submitButton) {
            submitButton.disabled = false;
            submitButton.textContent = "Import & Assign";
            submitButton.classList.remove("bg-stone-400", "cursor-not-allowed");
            submitButton.classList.add("bg-stone-800", "hover:bg-stone-900");
          }
        }
      } else {
        // Show error message
        this.showMessage(syncMessage, result.errors.join(", "), "error");

        // Restore original state
        if (syncStatusContainer) syncStatusContainer.classList.remove("hidden");
      }
    } catch (error) {
      console.error("Sync error:", error);
      this.showMessage(
        syncMessage,
        "An unexpected error occurred during sync",
        "error"
      );
      if (syncStatusContainer) syncStatusContainer.classList.remove("hidden");
    } finally {
      // Hide loading state
      if (syncLoading) syncLoading.classList.add("hidden");
      button.disabled = false;
    }
  }

  updateSyncedState(priceContainer, result) {
    const syncStatusContainer = priceContainer.querySelector(
      ".sync-status-container"
    );

    if (!syncStatusContainer) {
      console.error("Sync status container not found");
      return;
    }

    // Update the sync status HTML to show synced state
    syncStatusContainer.innerHTML = `
      <div class="flex items-center justify-between mb-2">
        <div class="flex items-center text-sm text-green-600">
          <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
          </svg>
          Synced to local plan
        </div>
        <button type="button" 
                class="sync-price-btn text-xs px-2 py-1 bg-stone-100 hover:bg-stone-200 text-stone-700 rounded border transition-colors"
                data-price-id="${priceContainer.dataset.priceId}"
                data-action="re-sync">
          Re-sync
        </button>
      </div>
      <div class="mt-2">
        ${this.generateSectionAssignments(result.plan)}
      </div>
    `;

    if (syncStatusContainer) syncStatusContainer.classList.remove("hidden");
  }

  generateSectionAssignments(plan) {
    if (plan.available_for_scout || plan.available_for_talent) {
      let badges = "";
      if (plan.available_for_scout) {
        badges +=
          '<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">Scout</span>';
      }
      if (plan.available_for_talent) {
        badges +=
          '<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">Talent</span>';
      }

      return `
        <div class="text-xs text-stone-600 mb-1">Available for:</div>
        <div class="flex flex-wrap gap-1">${badges}</div>
      `;
    } else {
      return '<div class="text-xs text-stone-500">Not assigned to any section</div>';
    }
  }

  showMessage(messageElement, text, type) {
    if (!messageElement) return;

    const bgColor =
      type === "success"
        ? "bg-green-50 text-green-800 border-green-200"
        : "bg-red-50 text-red-800 border-red-200";

    messageElement.innerHTML = `
      <div class="text-xs p-2 rounded border ${bgColor}">
        ${text}
      </div>
    `;
    messageElement.classList.remove("hidden");

    // Auto-hide success messages after 3 seconds
    if (type === "success") {
      setTimeout(() => {
        messageElement.classList.add("hidden");
      }, 3000);
    }
  }
}
