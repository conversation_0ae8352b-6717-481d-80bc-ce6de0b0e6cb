import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="settings-form"
export default class extends Controller {
  static targets = ["submit"]

  connect() {
    // Add form submission handler
    this.element.addEventListener("submit", this.handleSubmit.bind(this))
  }

  handleSubmit(event) {
    // Show loading state on submit button
    this.submitTarget.disabled = true
    this.submitTarget.innerHTML = `
      <svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      Saving...
    `
    
    // Re-enable button after a timeout as fallback
    setTimeout(() => {
      this.resetButton()
    }, 10000)
  }

  resetButton() {
    if (this.hasSubmitTarget) {
      this.submitTarget.disabled = false
      // Reset to original text based on the form type
      if (this.element.querySelector('[name="user[first_name]"]')) {
        this.submitTarget.innerHTML = "Update Profile"
      } else if (this.element.querySelector('[name="organization[name]"]')) {
        this.submitTarget.innerHTML = "Save Changes"
      } else {
        this.submitTarget.innerHTML = "Save"
      }
    }
  }

  disconnect() {
    // Clean up event listeners
    this.element.removeEventListener("submit", this.handleSubmit.bind(this))
  }
}
