<% content_for :title, @page_title %>

<div class="bg-white shadow rounded-lg">
  <div class="px-4 py-5 sm:p-6">
    <!-- Header -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-stone-900 mb-2"><%= @page_title %></h1>
      <p class="text-stone-600">Monitor and manage subscription plan synchronization with Stripe</p>
    </div>

    <!-- System Health Status -->
    <div class="mb-8">
      <h2 class="text-lg font-semibold text-stone-900 mb-4">System Health</h2>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- Health Status Card -->
        <div class="bg-<%= @sync_status[:system_health][:status] == 'healthy' ? 'green' : @sync_status[:system_health][:status] == 'warning' ? 'yellow' : 'red' %>-50 border border-<%= @sync_status[:system_health][:status] == 'healthy' ? 'green' : @sync_status[:system_health][:status] == 'warning' ? 'yellow' : 'red' %>-200 rounded-lg p-4">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <% if @sync_status[:system_health][:status] == 'healthy' %>
                <svg class="h-5 w-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                </svg>
              <% elsif @sync_status[:system_health][:status] == 'warning' %>
                <svg class="h-5 w-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                </svg>
              <% else %>
                <svg class="h-5 w-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                </svg>
              <% end %>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-stone-900">System Status</h3>
              <p class="text-sm text-stone-600 capitalize"><%= @sync_status[:system_health][:status] %></p>
            </div>
          </div>
          <% if @sync_status[:system_health][:issues].any? %>
            <div class="mt-2">
              <ul class="text-xs text-stone-600">
                <% @sync_status[:system_health][:issues].each do |issue| %>
                  <li>• <%= issue %></li>
                <% end %>
              </ul>
            </div>
          <% end %>
        </div>

        <!-- Statistics Cards -->
        <div class="bg-stone-50 border border-stone-200 rounded-lg p-4">
          <h3 class="text-sm font-medium text-stone-900 mb-2">Sync Coverage</h3>
          <div class="text-2xl font-bold text-stone-900"><%= @sync_status[:statistics][:sync_coverage_percentage] %>%</div>
          <p class="text-xs text-stone-600"><%= @sync_status[:statistics][:synced_plans] %> of <%= @sync_status[:statistics][:total_plans] %> plans synced</p>
        </div>

        <div class="bg-stone-50 border border-stone-200 rounded-lg p-4">
          <h3 class="text-sm font-medium text-stone-900 mb-2">Stale Plans</h3>
          <div class="text-2xl font-bold text-<%= @sync_status[:statistics][:stale_plans] > 0 ? 'red' : 'stone' %>-900"><%= @sync_status[:statistics][:stale_plans] %></div>
          <p class="text-xs text-stone-600">Plans not synced in 24h</p>
        </div>
      </div>
    </div>

    <!-- Current Alerts -->
    <% if @sync_status[:current_alerts].any? %>
      <div class="mb-8">
        <h2 class="text-lg font-semibold text-stone-900 mb-4">Current Alerts</h2>
        <div class="space-y-3">
          <% @sync_status[:current_alerts].each do |alert| %>
            <div class="bg-<%= alert[:severity] == 'error' ? 'red' : 'yellow' %>-50 border border-<%= alert[:severity] == 'error' ? 'red' : 'yellow' %>-200 rounded-lg p-4">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-<%= alert[:severity] == 'error' ? 'red' : 'yellow' %>-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                  </svg>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-stone-900"><%= alert[:message] %></h3>
                  <p class="text-xs text-stone-600 mt-1">
                    <%= alert[:type].humanize %> • <%= time_ago_in_words(alert[:timestamp]) %> ago
                  </p>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    <% end %>

    <!-- Last Sync Information -->
    <div class="mb-8">
      <h2 class="text-lg font-semibold text-stone-900 mb-4">Last Sync Information</h2>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- Full Sync -->
        <div class="bg-stone-50 border border-stone-200 rounded-lg p-4">
          <h3 class="text-sm font-medium text-stone-900 mb-2">Full Sync</h3>
          <% if @sync_status[:last_full_sync][:timestamp] %>
            <p class="text-xs text-stone-600 mb-1">
              <%= time_ago_in_words(@sync_status[:last_full_sync][:timestamp]) %> ago
            </p>
            <div class="flex items-center">
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-<%= @sync_status[:last_full_sync][:success] ? 'green' : 'red' %>-100 text-<%= @sync_status[:last_full_sync][:success] ? 'green' : 'red' %>-800">
                <%= @sync_status[:last_full_sync][:success] ? 'Success' : 'Failed' %>
              </span>
            </div>
            <% if @sync_status[:last_full_sync][:created_count] || @sync_status[:last_full_sync][:updated_count] %>
              <p class="text-xs text-stone-600 mt-2">
                Created: <%= @sync_status[:last_full_sync][:created_count] || 0 %>, 
                Updated: <%= @sync_status[:last_full_sync][:updated_count] || 0 %>
              </p>
            <% end %>
          <% else %>
            <p class="text-xs text-stone-600">No full sync recorded</p>
          <% end %>
        </div>

        <!-- Health Check -->
        <div class="bg-stone-50 border border-stone-200 rounded-lg p-4">
          <h3 class="text-sm font-medium text-stone-900 mb-2">Health Check</h3>
          <% if @sync_status[:last_health_check][:timestamp] %>
            <p class="text-xs text-stone-600 mb-1">
              <%= time_ago_in_words(@sync_status[:last_health_check][:timestamp]) %> ago
            </p>
            <div class="flex items-center">
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-<%= @sync_status[:last_health_check][:success] ? 'green' : 'red' %>-100 text-<%= @sync_status[:last_health_check][:success] ? 'green' : 'red' %>-800">
                <%= @sync_status[:last_health_check][:success] ? 'Success' : 'Failed' %>
              </span>
            </div>
          <% else %>
            <p class="text-xs text-stone-600">No health check recorded</p>
          <% end %>
        </div>

        <!-- Bulk Sync -->
        <div class="bg-stone-50 border border-stone-200 rounded-lg p-4">
          <h3 class="text-sm font-medium text-stone-900 mb-2">Last Bulk Sync</h3>
          <% if @sync_status[:last_bulk_sync][:timestamp] %>
            <p class="text-xs text-stone-600 mb-1">
              <%= time_ago_in_words(@sync_status[:last_bulk_sync][:timestamp]) %> ago
            </p>
            <div class="flex items-center">
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-<%= @sync_status[:last_bulk_sync][:success] ? 'green' : 'red' %>-100 text-<%= @sync_status[:last_bulk_sync][:success] ? 'green' : 'red' %>-800">
                <%= @sync_status[:last_bulk_sync][:success] ? 'Success' : 'Failed' %>
              </span>
            </div>
          <% else %>
            <p class="text-xs text-stone-600">No bulk sync recorded</p>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Manual Sync Controls -->
    <div class="mb-8">
      <h2 class="text-lg font-semibold text-stone-900 mb-4">Manual Sync Controls</h2>
      <div class="bg-stone-50 border border-stone-200 rounded-lg p-4">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <!-- Full Sync -->
          <div>
            <%= form_with url: trigger_super_admin_subscription_plan_sync_index_path, method: :post, local: true, class: "space-y-3" do |form| %>
              <%= form.hidden_field :sync_type, value: 'full' %>
              <div>
                <label class="flex items-center">
                  <%= form.check_box :force_sync, class: "rounded border-stone-300 text-stone-600 focus:ring-stone-500" %>
                  <span class="ml-2 text-sm text-stone-700">Force sync (ignore recent sync)</span>
                </label>
              </div>
              <%= form.submit "Trigger Full Sync", class: "w-full bg-stone-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-stone-700 focus:outline-none focus:ring-2 focus:ring-stone-500" %>
            <% end %>
          </div>

          <!-- Health Check -->
          <div>
            <%= form_with url: trigger_super_admin_subscription_plan_sync_index_path, method: :post, local: true do |form| %>
              <%= form.hidden_field :sync_type, value: 'health_check' %>
              <%= form.submit "Run Health Check", class: "w-full bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500" %>
            <% end %>
          </div>

          <!-- Cleanup -->
          <div>
            <%= form_with url: cleanup_super_admin_subscription_plan_sync_index_path, method: :delete, local: true do |form| %>
              <%= form.submit "Cleanup Old Data", class: "w-full bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500" %>
            <% end %>
          </div>
        </div>

        <!-- Bulk Sync Form -->
        <div class="mt-6 pt-6 border-t border-stone-200">
          <h3 class="text-sm font-medium text-stone-900 mb-3">Bulk Sync Specific Plans</h3>
          <%= form_with url: trigger_super_admin_subscription_plan_sync_index_path, method: :post, local: true, class: "space-y-3" do |form| %>
            <%= form.hidden_field :sync_type, value: 'bulk' %>
            <div>
              <%= form.text_area :price_ids, placeholder: "Enter Stripe price IDs (comma-separated)\ne.g., price_1ABC123, price_2DEF456", rows: 3, class: "w-full border border-stone-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-stone-500 focus:border-stone-500" %>
            </div>
            <div class="flex items-center justify-between">
              <label class="flex items-center">
                <%= form.check_box :force_sync, class: "rounded border-stone-300 text-stone-600 focus:ring-stone-500" %>
                <span class="ml-2 text-sm text-stone-700">Force sync</span>
              </label>
              <%= form.submit "Trigger Bulk Sync", class: "bg-stone-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-stone-700 focus:outline-none focus:ring-2 focus:ring-stone-500" %>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Recent Sync History -->
    <div>
      <h2 class="text-lg font-semibold text-stone-900 mb-4">Recent Sync History</h2>
      <div class="bg-stone-50 border border-stone-200 rounded-lg overflow-hidden">
        <% if @sync_status[:sync_history].any? %>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-stone-200">
              <thead class="bg-stone-100">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Type</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Status</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Duration</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Results</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Time</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-stone-200">
                <% @sync_status[:sync_history].first(10).each do |sync| %>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-900">
                      <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-stone-100 text-stone-800">
                        <%= sync[:sync_type].humanize %>
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-900">
                      <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-<%= sync[:success] ? 'green' : 'red' %>-100 text-<%= sync[:success] ? 'green' : 'red' %>-800">
                        <%= sync[:success] ? 'Success' : 'Failed' %>
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-500">
                      <%= sync[:duration_seconds] %>s
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-500">
                      <% if sync[:success] %>
                        C:<%= sync[:created_count] || 0 %> U:<%= sync[:updated_count] || 0 %>
                        <% if sync[:skipped_count] && sync[:skipped_count] > 0 %>
                          S:<%= sync[:skipped_count] %>
                        <% end %>
                      <% else %>
                        <%= sync[:error] || 'Unknown error' %>
                      <% end %>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-500">
                      <%= time_ago_in_words(sync[:timestamp]) %> ago
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
        <% else %>
          <div class="px-6 py-4 text-center text-sm text-stone-500">
            No sync history available
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>
