<% content_for :title, "Discover Stripe Products" %>

<div class="bg-white rounded-lg shadow"
     data-controller="price-sync"
     data-price-sync-sync-url-value="<%= sync_price_super_admin_subscription_plans_path %>"
     data-price-sync-csrf-token-value="<%= form_authenticity_token %>">
  <div class="px-4 py-5 sm:p-6">
    <!-- Header Section -->
    <div class="sm:flex sm:items-center">
      <div class="sm:flex-auto">
        <h1 class="text-xl font-semibold text-stone-900">Discover Stripe Products</h1>
        <p class="mt-2 text-sm text-stone-700">Import and assign Stripe products to app sections</p>
      </div>
      <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
        <%= link_to super_admin_subscription_plans_path,
            class: "inline-flex items-center justify-center rounded-md border border-stone-300 px-4 py-2 text-sm font-medium text-stone-800 shadow-sm hover:bg-stone-100 focus:outline-none focus:ring-2 focus:ring-stone-500 focus:ring-offset-2" do %>
          <svg class="w-4 h-4 mr-2 -ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
          Back to Plans
        <% end %>
      </div>
    </div>

    <!-- Info Section -->
    <div class="p-4 mt-6 border border-blue-200 rounded-lg bg-blue-50">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-blue-800">Enhanced Sync Functionality</h3>
          <div class="mt-2 text-sm text-blue-700">
            <p>You can now sync individual prices to create local subscription plans. Use the <strong>"Sync to Local Plan"</strong> button for unsynced prices, or <strong>"Re-sync"</strong> to update existing plans with latest Stripe data. Once synced, you can assign plans to Scout or Talent sections.</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Statistics Section -->
    <div class="grid w-full grid-cols-3 gap-5 mt-6">

      <div class="flex-1 overflow-hidden border rounded-lg shadow bg-stone-50 border-stone-400">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="w-6 h-6 text-stone-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
              </svg>
            </div>
            <div class="flex-1 w-0 ml-5">
              <dl>
                <dt class="text-sm font-medium truncate text-stone-500">Total Prices</dt>
                <dd class="text-lg font-medium text-stone-900"><%= @total_prices %></dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="flex-1 overflow-hidden border border-green-100 rounded-lg shadow bg-green-50">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="w-6 h-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="flex-1 w-0 ml-5">
              <dl>
                <dt class="text-sm font-medium text-green-500 truncate">Synced Prices</dt>
                <dd class="text-lg font-medium text-green-900"><%= @synced_prices %></dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="flex-1 overflow-hidden border border-yellow-100 rounded-lg shadow bg-yellow-50">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="w-6 h-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
              </svg>
            </div>
            <div class="flex-1 w-0 ml-5">
              <dl>
                <dt class="text-sm font-medium text-yellow-500 truncate">Unsynced Prices</dt>
                <dd class="text-lg font-medium text-yellow-900"><%= @unsynced_prices %></dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Products Section -->
    <div class="pt-6 mt-8 border-t border-stone-200">
      <% if @products.any? %>
        <div class="space-y-6">
          <% @products.each do |product| %>
            <div class="p-6 rounded-lg bg-stone-50">
              <!-- Product Header -->
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <h3 class="text-lg font-medium text-stone-900"><%= product[:name] %></h3>
                  <p class="mt-1 text-sm text-stone-600"><%= product[:description] %></p>
                  <div class="flex items-center mt-2 space-x-4">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <%= product[:active] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' %>">
                      <%= product[:active] ? 'Active' : 'Inactive' %>
                    </span>
                    <span class="text-xs text-stone-500">ID: <%= product[:id] %></span>
                    <span class="text-xs text-stone-500">Created: <%= Time.at(product[:created]).strftime('%b %d, %Y') %></span>
                  </div>
                </div>
              </div>

              <!-- Product Prices -->
              <% if product[:prices].any? %>
                <div class="mt-6">
                  <h4 class="mb-4 text-sm font-medium text-stone-900">Pricing Options</h4>
                  <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                    <% product[:prices].each do |price| %>
                      <div class="border border-stone-200 rounded-lg p-4 <%= price[:synced] ? 'bg-white' : 'bg-yellow-50' %>">
                        <!-- Price Header -->
                        <div class="flex items-start justify-between mb-3">
                          <div>
                            <div class="text-lg font-semibold text-stone-900">
                              <%= number_to_currency(price[:unit_amount] / 100.0, unit: price[:currency].upcase + ' ') %>
                            </div>
                            <div class="text-sm text-stone-600">
                              <% if price[:recurring] %>
                                per <%= price[:recurring]['interval'] %>
                                <% if price[:recurring]['interval_count'] > 1 %>
                                  (every <%= price[:recurring]['interval_count'] %> <%= price[:recurring]['interval'].pluralize %>)
                                <% end %>
                              <% else %>
                                one-time payment
                              <% end %>
                            </div>
                          </div>
                          <div class="flex flex-col space-y-1">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <%= price[:active] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' %>">
                              <%= price[:active] ? 'Active' : 'Inactive' %>
                            </span>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <%= price[:recurring] ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800' %>">
                              <%= price[:recurring] ? 'Subscription' : 'One-time' %>
                            </span>
                          </div>
                        </div>

                        <!-- Price ID -->
                        <div class="mb-3 font-mono text-xs text-stone-500"><%= price[:id] %></div>

                        <!-- Sync Status and Controls -->
                        <div class="mb-4" data-price-id="<%= price[:id] %>">
                          <div class="sync-status-container">
                            <% if price[:synced] %>
                              <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center text-sm text-green-600">
                                  <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                  </svg>
                                  Synced to local plan
                                </div>
                                <button type="button"
                                        class="px-2 py-1 text-xs transition-colors border rounded sync-price-btn bg-stone-100 hover:bg-stone-200 text-stone-700"
                                        data-price-id="<%= price[:id] %>"
                                        data-action="re-sync"
                                        title="Update local plan with latest data from Stripe">
                                  Re-sync
                                </button>
                              </div>
                              <!-- Show current section assignments -->
                              <div class="mt-2">
                                <% if price[:available_for_scout] || price[:available_for_talent] %>
                                  <div class="mb-1 text-xs text-stone-600">Available for:</div>
                                  <div class="flex flex-wrap gap-1">
                                    <% if price[:available_for_scout] %>
                                      <span class="inline-flex px-2 py-1 text-xs font-semibold text-blue-800 bg-blue-100 rounded-full">Scout</span>
                                    <% end %>
                                    <% if price[:available_for_talent] %>
                                      <span class="inline-flex px-2 py-1 text-xs font-semibold text-purple-800 bg-purple-100 rounded-full">Talent</span>
                                    <% end %>
                                  </div>
                                <% else %>
                                  <div class="text-xs text-stone-500">Not assigned to any section</div>
                                <% end %>
                              </div>
                            <% else %>
                              <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center text-sm text-yellow-600">
                                  <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                  </svg>
                                  Not synced
                                </div>
                                <button type="button"
                                        class="px-3 py-1 text-xs text-white transition-colors bg-blue-600 rounded sync-price-btn hover:bg-blue-700"
                                        data-price-id="<%= price[:id] %>"
                                        data-action="sync"
                                        title="Create a local subscription plan from this Stripe price">
                                  Sync to Local Plan
                                </button>
                              </div>
                            <% end %>
                          </div>

                          <!-- Loading and message states -->
                          <div class="hidden sync-loading">
                            <div class="flex items-center text-sm text-blue-600">
                              <svg class="w-4 h-4 mr-1 animate-spin" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              Syncing...
                            </div>
                          </div>

                          <div class="hidden mt-2 sync-message"></div>
                        </div>

                        <!-- Assignment Form -->
                        <%= form_with url: assign_price_to_sections_super_admin_subscription_plans_path, method: :post, local: true, class: "space-y-3" do |form| %>
                          <%= form.hidden_field :stripe_price_id, value: price[:id] %>
                          <%= form.hidden_field :from, value: 'discovery' %>
                          
                          <div>
                            <label class="text-xs font-medium text-stone-700">Assign to sections:</label>
                            <div class="mt-1 space-y-1">
                              <label class="flex items-center">
                                <%= form.check_box :sections, { multiple: true, checked: price[:available_for_scout] }, 'scout', '' %>
                                <span class="ml-2 text-sm text-stone-600">Scout subscriptions</span>
                              </label>
                              <label class="flex items-center">
                                <%= form.check_box :sections, { multiple: true, checked: price[:available_for_talent] }, 'talent', '' %>
                                <span class="ml-2 text-sm text-stone-600">Talent subscriptions</span>
                              </label>
                            </div>
                          </div>
                          
                          <button type="submit"
                                  class="w-full inline-flex justify-center items-center px-3 py-2 border border-transparent text-xs font-medium rounded-md text-white transition-colors
                                         <%= price[:synced] ? 'bg-stone-800 hover:bg-stone-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500' : 'bg-stone-400 cursor-not-allowed' %>"
                                  <%= 'disabled' unless price[:synced] %>>
                            <% if price[:synced] %>
                              Update Assignment
                            <% else %>
                              <span class="flex items-center">
                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                  <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                                </svg>
                                Sync Required
                              </span>
                            <% end %>
                          </button>
                        <% end %>
                      </div>
                    <% end %>
                  </div>
                </div>
              <% else %>
                <div class="mt-4 text-sm text-stone-500">No pricing options found for this product.</div>
              <% end %>
            </div>
          <% end %>
        </div>
      <% else %>
        <!-- Empty State -->
        <div class="py-12 text-center">
          <svg class="w-12 h-12 mx-auto text-stone-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-stone-900">No products found</h3>
          <p class="mt-1 text-sm text-stone-500">No products were discovered from your Stripe account.</p>
        </div>
      <% end %>
    </div>
  </div>
</div>
