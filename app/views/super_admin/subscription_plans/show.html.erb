<% content_for :title, @subscription_plan.name %>

<div class="bg-white rounded-lg shadow">
  <div class="px-4 py-5 sm:p-6">
    <!-- Header Section -->
    <div class="sm:flex sm:items-center sm:justify-between">
      <div class="sm:flex-auto">
        <h1 class="text-xl font-semibold text-stone-900"><%= @subscription_plan.name %></h1>
        <p class="mt-2 text-sm text-stone-700"><%= @subscription_plan.description %></p>
      </div>
      <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none space-x-3">
        <% if @subscription_plan.stripe_price_id.present? %>
          <%= link_to sync_plan_super_admin_subscription_plan_path(@subscription_plan),
              class: "inline-flex items-center justify-center rounded-md border border-stone-300 px-4 py-2 text-sm font-medium text-stone-800 shadow-sm hover:bg-stone-100 focus:outline-none focus:ring-2 focus:ring-stone-500 focus:ring-offset-2",
              data: { turbo_method: :post, confirm: "Sync this plan with <PERSON><PERSON>?" } do %>
            <svg class="w-4 h-4 mr-2 -ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            Sync with Stripe
          <% end %>
        <% else %>
          <div class="inline-flex items-center justify-center rounded-md border border-stone-200 px-4 py-2 text-sm font-medium text-stone-400 bg-stone-50 cursor-not-allowed">
            <svg class="w-4 h-4 mr-2 -ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            No Stripe Integration
          </div>
        <% end %>
        <%= link_to edit_super_admin_subscription_plan_path(@subscription_plan),
            class: "inline-flex items-center justify-center rounded-md border border-transparent px-4 py-2 text-sm font-medium text-white bg-stone-800 hover:bg-stone-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500" do %>
          <svg class="w-4 h-4 mr-2 -ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
          </svg>
          Edit Plan
        <% end %>
      </div>
    </div>

    <!-- Back Button -->
    <div class="mt-6 pt-6 border-t border-stone-200">
      <div class="flex justify-center">
        <%= link_to super_admin_subscription_plans_path, class: "text-stone-600 hover:text-stone-900 text-sm" do %>
          ← Back to Subscription Plans
        <% end %>
      </div>
    </div>

    <!-- Plan Details Section -->
    <div class="mt-8 pt-6 border-t border-stone-200">
      <h2 class="text-lg font-medium text-stone-900 mb-6">Plan Details</h2>
      
      <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        <!-- Basic Information -->
        <div class="bg-stone-50 rounded-lg p-4">
          <h3 class="text-sm font-medium text-stone-900 mb-3">Basic Information</h3>
          <dl class="space-y-2">
            <div>
              <dt class="text-xs text-stone-500">Name</dt>
              <dd class="text-sm text-stone-900"><%= @subscription_plan.name %></dd>
            </div>
            <div>
              <dt class="text-xs text-stone-500">Amount</dt>
              <dd class="text-sm text-stone-900"><%= @subscription_plan.formatted_amount %></dd>
            </div>
            <div>
              <dt class="text-xs text-stone-500">Billing Cycle</dt>
              <dd class="text-sm text-stone-900"><%= @subscription_plan.billing_cycle_description %></dd>
            </div>
          </dl>
        </div>

        <!-- Stripe Information -->
        <div class="bg-stone-50 rounded-lg p-4">
          <h3 class="text-sm font-medium text-stone-900 mb-3">Stripe Integration</h3>
          <dl class="space-y-2">
            <div>
              <dt class="text-xs text-stone-500">Price ID</dt>
              <dd class="text-sm text-stone-900 font-mono"><%= @subscription_plan.stripe_price_id %></dd>
            </div>
            <% if @subscription_plan.stripe_product_id.present? %>
              <div>
                <dt class="text-xs text-stone-500">Product ID</dt>
                <dd class="text-sm text-stone-900 font-mono"><%= @subscription_plan.stripe_product_id %></dd>
              </div>
            <% end %>
            <div>
              <dt class="text-xs text-stone-500">Last Synced</dt>
              <dd class="text-sm text-stone-900">
                <% if @subscription_plan.last_synced_at %>
                  <%= time_ago_in_words(@subscription_plan.last_synced_at) %> ago
                <% else %>
                  Never
                <% end %>
              </dd>
            </div>
          </dl>
        </div>

        <!-- Status Information -->
        <div class="bg-stone-50 rounded-lg p-4">
          <h3 class="text-sm font-medium text-stone-900 mb-3">Status</h3>
          <dl class="space-y-2">
            <div>
              <dt class="text-xs text-stone-500">Active</dt>
              <dd class="text-sm">
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <%= @subscription_plan.active? ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' %>">
                  <%= @subscription_plan.active? ? 'Active' : 'Inactive' %>
                </span>
              </dd>
            </div>
            <div>
              <dt class="text-xs text-stone-500">Legacy</dt>
              <dd class="text-sm">
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <%= @subscription_plan.legacy? ? 'bg-yellow-100 text-yellow-800' : 'bg-stone-100 text-stone-800' %>">
                  <%= @subscription_plan.legacy? ? 'Legacy' : 'Current' %>
                </span>
              </dd>
            </div>
            <div>
              <dt class="text-xs text-stone-500">Sync Status</dt>
              <dd class="text-sm">
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <%= @subscription_plan.sync_status == 'recently_synced' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' %>">
                  <%= @subscription_plan.sync_status.humanize %>
                </span>
              </dd>
            </div>
          </dl>
        </div>
      </div>
    </div>

    <!-- Features Section -->
    <% if @subscription_plan.features.present? %>
      <div class="mt-8 pt-6 border-t border-stone-200">
        <h2 class="text-lg font-medium text-stone-900 mb-4">Features</h2>
        <div class="bg-stone-50 rounded-lg p-4">
          <ul class="space-y-2">
            <% @subscription_plan.features.each do |feature| %>
              <li class="flex items-center text-sm text-stone-700">
                <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <%= feature %>
              </li>
            <% end %>
          </ul>
        </div>
      </div>
    <% end %>

    <!-- Subscription Statistics -->
    <div class="mt-8 pt-6 border-t border-stone-200">
      <h2 class="text-lg font-medium text-stone-900 mb-6">Subscription Statistics</h2>
      
      <div class="grid grid-cols-1 gap-6 sm:grid-cols-3">
        <div class="bg-stone-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-stone-900"><%= @subscription_count %></div>
          <div class="text-sm text-stone-500">Total Subscriptions</div>
        </div>
        <div class="bg-stone-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-green-600"><%= @active_subscriptions %></div>
          <div class="text-sm text-stone-500">Active Subscriptions</div>
        </div>
        <div class="bg-stone-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-stone-900"><%= @subscription_plan.total_revenue %></div>
          <div class="text-sm text-stone-500">Total Revenue</div>
        </div>
      </div>
    </div>

    <!-- Recent Subscriptions -->
    <% if @recent_subscriptions.any? %>
      <div class="mt-8 pt-6 border-t border-stone-200">
        <h2 class="text-lg font-medium text-stone-900 mb-4">Recent Subscriptions</h2>
        <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
          <table class="min-w-full divide-y divide-stone-300">
            <thead class="bg-stone-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">User</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Status</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Created</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-stone-200">
              <% @recent_subscriptions.each do |subscription| %>
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-stone-900"><%= subscription.customer.owner&.full_name || 'Unknown User' %></div>
                    <div class="text-sm text-stone-500"><%= subscription.customer.owner&.email || 'No email' %></div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <%= subscription.active? ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' %>">
                      <%= subscription.status.humanize %>
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-500">
                    <%= time_ago_in_words(subscription.created_at) %> ago
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>
    <% end %>

    <!-- Metadata Section -->
    <% if @subscription_plan.metadata.present? %>
      <div class="mt-8 pt-6 border-t border-stone-200">
        <h2 class="text-lg font-medium text-stone-900 mb-4">Metadata</h2>
        <div class="bg-stone-50 rounded-lg p-4">
          <pre class="text-sm text-stone-700 whitespace-pre-wrap"><%= JSON.pretty_generate(@subscription_plan.metadata) %></pre>
        </div>
      </div>
    <% end %>

    <!-- Danger Zone -->
    <% if @subscription_count == 0 && !@subscription_plan.available_for_any_section? %>
      <div class="mt-8 pt-6 border-t border-red-200">
        <h2 class="text-lg font-medium text-red-900 mb-4">Danger Zone</h2>
        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-sm font-medium text-red-900">Delete this subscription plan</h3>
              <p class="text-sm text-red-700">This action cannot be undone. This will permanently delete the subscription plan.</p>
            </div>
            <div>
              <%= link_to super_admin_subscription_plan_path(@subscription_plan),
                  class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",
                  data: { turbo_method: :delete, confirm: "Are you sure you want to delete this subscription plan? This action cannot be undone." } do %>
                Delete Plan
              <% end %>
            </div>
          </div>
        </div>
      </div>
    <% else %>
      <div class="mt-8 pt-6 border-t border-stone-200">
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div class="flex">
            <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-yellow-800">Cannot delete plan</h3>
              <p class="text-sm text-yellow-700">
                <% if @subscription_count > 0 %>
                  This plan has <%= @subscription_count %> subscription(s) and cannot be deleted. Deactivate the plan instead.
                <% elsif @subscription_plan.available_for_any_section? %>
                  This plan is assigned to <%= @subscription_plan.assigned_sections.join(' and ') %> section(s) and cannot be deleted. Remove section assignments first.
                <% end %>
              </p>
            </div>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</div>
