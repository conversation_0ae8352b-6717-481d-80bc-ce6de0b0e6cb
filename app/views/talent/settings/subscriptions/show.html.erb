<%# Container div with styling matching general and password settings %>
<div class="h-full px-16 py-8 bg-white border rounded shadow-sm border-stone-200"
     data-controller="subscription"
     data-subscription-loaded-value="<%= @subscription.present? || !@loading %>">
  <%= render "talent/settings/shell" %> <%# Render shell inside container %>

  <div class="max-w-2xl pt-6"> <%# Added pt-6 for spacing, matching other settings pages %>

    <%# Loading State %>
    <div data-subscription-target="loadingState" class="pb-12 border-b border-stone-900/10 <%= 'hidden' unless @loading %>">
      <div class="animate-pulse">
        <div class="w-1/3 h-6 mb-2 rounded bg-stone-200"></div>
        <div class="w-2/3 h-4 mb-8 rounded bg-stone-200"></div>
        <div class="p-6 rounded-lg bg-stone-100">
          <div class="w-1/4 h-5 mb-4 rounded bg-stone-200"></div>
          <div class="w-3/4 h-4 mb-6 rounded bg-stone-200"></div>
          <div class="grid grid-cols-2 gap-6">
            <div class="space-y-3">
              <div class="w-1/2 h-4 rounded bg-stone-200"></div>
              <div class="w-full h-3 rounded bg-stone-200"></div>
              <div class="w-full h-3 rounded bg-stone-200"></div>
              <div class="w-full h-3 rounded bg-stone-200"></div>
            </div>
            <div class="space-y-3">
              <div class="w-1/2 h-4 rounded bg-stone-200"></div>
              <div class="w-full h-3 rounded bg-stone-200"></div>
              <div class="w-full h-3 rounded bg-stone-200"></div>
              <div class="w-full h-3 rounded bg-stone-200"></div>
            </div>
          </div>
          <div class="pt-6 mt-6 border-t border-stone-200">
            <div class="w-1/4 h-4 mb-3 rounded bg-stone-200"></div>
            <div class="w-1/3 h-8 rounded bg-stone-200"></div>
          </div>
        </div>
      </div>
    </div>

    <%# Current Subscription Section - Following General/Password Page Structure %>
    <div data-subscription-target="content" class="<%= 'hidden' if @loading %>">
      <% if @subscription %>
        <div class="pb-12 border-b border-stone-900/10">
        <h2 class="text-base font-semibold leading-7 text-stone-900">Current Subscription</h2>
        <p class="mt-1 text-sm leading-6 text-stone-600">View and manage your active subscription details.</p>

        <%# Main Subscription Card %>
        <div class="mt-8 <%= subscription_card_classes(@subscription.processor_plan) %>">
          <%# Header with Plan Name and Status %>
          <div class="flex items-center justify-between mb-6">
            <div class="flex items-center space-x-3">
              <h3 class="text-lg font-semibold text-stone-900">
                <%= plan_display_name(@subscription.processor_plan) %>
              </h3>
            <span class="<%= subscription_status_badge_classes(@subscription.status) %>">
              <%= subscription_status_display(@subscription.status) %>
            </span>
            </div>
          </div>

          <%# Plan Description %>
          <p class="mb-6 text-sm text-stone-600">
            <%= plan_description(@subscription.processor_plan) %>
          </p>

          <%# Subscription Details Grid %>
          <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <%# Billing Information %>
            <div class="space-y-4">
              <h4 class="text-sm font-medium text-stone-900">Billing Information</h4>

              <div class="space-y-3">
                <div class="flex justify-between">
                  <span class="text-sm text-stone-600">Billing Cycle</span>
                  <span class="text-sm font-medium text-stone-900">
                    <%= plan_billing_cycle(@subscription.processor_plan) %>
                  </span>
                </div>

                <div class="flex justify-between">
                  <span class="text-sm text-stone-600">Amount</span>
                  <span class="text-sm font-medium text-stone-900">
                    <%= @billing_amount || 'See billing portal' %>
                  </span>
                </div>

                <div class="flex justify-between">
                  <span class="text-sm text-stone-600">Started</span>
                  <span class="text-sm font-medium text-stone-900">
                    <%= format_billing_date(@subscription.created_at) %>
                  </span>
                </div>

                <% if @subscription.trial_ends_at %>
                  <div class="flex justify-between">
                    <span class="text-sm text-stone-600">Trial Ends</span>
                    <span class="text-sm font-medium text-stone-900">
                      <%= format_billing_date(@subscription.trial_ends_at) %>
                    </span>
                  </div>
                <% end %>

                <% if @subscription.ends_at %>
                  <div class="flex justify-between">
                    <span class="text-sm text-stone-600">Subscription Ends</span>
                    <span class="text-sm font-medium text-red-600">
                      <%= format_billing_date(@subscription.ends_at) %>
                    </span>
                  </div>
                <% else %>
                  <div class="flex justify-between">
                    <span class="text-sm text-stone-600">Next Billing Date</span>
                    <span class="text-sm font-medium text-stone-900">
                      <%= format_billing_date(@next_billing_date) %>
                    </span>
                  </div>
                <% end %>

                <%# Additional status information %>
                <% if @subscription.status == 'past_due' %>
                  <div class="p-3 mt-4 border border-red-200 rounded-md bg-red-50">
                    <div class="flex">
                      <div class="flex-shrink-0">
                        <%= phosphor_icon "warning-circle", class: "h-4 w-4 text-red-400" %>
                      </div>
                      <div class="ml-2">
                        <p class="text-xs text-red-700">
                          Your payment is past due. Please update your payment method to continue your subscription.
                        </p>
                      </div>
                    </div>
                  </div>
                <% elsif @subscription.status == 'trialing' %>
                  <div class="p-3 mt-4 border border-blue-200 rounded-md bg-blue-50">
                    <div class="flex">
                      <div class="flex-shrink-0">
                        <%= phosphor_icon "info", class: "h-4 w-4 text-blue-400" %>
                      </div>
                      <div class="ml-2">
                        <p class="text-xs text-blue-700">
                          You're currently in your trial period. Billing will begin after the trial ends.
                        </p>
                      </div>
                    </div>
                  </div>
                <% end %>
              </div>
            </div>

            <%# Plan Features %>
            <div class="space-y-4">
              <h4 class="text-sm font-medium text-stone-900">Plan Features</h4>
              <ul class="space-y-2">
                <% plan_features(@subscription.processor_plan).each do |feature| %>
                  <li class="flex items-center text-sm text-stone-600">
                    <%= phosphor_icon "check-circle", class: "h-4 w-4 text-green-500 mr-2 flex-shrink-0" %>
                    <%= feature %>
                  </li>
                <% end %>
              </ul>
            </div>
          </div>
        </div>

        <%# Action Buttons Section %>
        <div class="pt-6 mt-8 border-t border-stone-200">
          <% if @billing_portal_url %>
            <div class="flex flex-col gap-4 sm:flex-row sm:justify-between">
              <div class="flex-1">
                <h4 class="text-sm font-medium text-stone-900">Subscription Management</h4>
                <p class="mt-1 text-sm text-stone-600">
                  Manage your plan, payment methods, and view invoices via the Stripe Billing Portal.
                </p>
              </div>

              <div class="flex justify-end gap-3 mt-3">
                <%# Manage Subscription button with black background and white text %>
                <%= link_to @billing_portal_url,
                            class: "inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-black border border-black rounded-md shadow-sm hover:bg-stone-800 focus:outline-none focus:ring-2 focus:ring-stone-500 focus:ring-offset-2",
                            data: { action: "click->subscription#handleBillingPortal" } do %> <%= phosphor_icon "gear", class: "h-4 w-4 mr-2" %>
                  Manage Subscription
                <% end %>
              </div>
            </div>
          <% else %>
            <%# Enhanced Error Display %>
            <div class="p-4 border border-yellow-200 rounded-md bg-yellow-50">
              <div class="flex">
                <div class="flex-shrink-0">
                  <%= phosphor_icon "warning-circle", class: "h-5 w-5 text-yellow-400", "aria-hidden": true %>
                </div>
                <div class="flex-1 ml-3">
                  <h3 class="text-sm font-medium text-yellow-800">Cannot Access Billing Portal</h3>
                  <div class="mt-2 text-sm text-yellow-700">
                    <p>We encountered an issue accessing the subscription management portal. This may be temporary.</p>
                  </div>
                  <div class="mt-4">
                    <div class="flex space-x-3">
                      <button type="button"
                              data-subscription-target="retryButton"
                              data-action="click->subscription#retry"
                              class="inline-flex items-center px-3 py-2 text-xs font-medium text-yellow-800 bg-yellow-100 border border-yellow-300 rounded-md hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2">
                        <%= phosphor_icon "arrow-clockwise", class: "h-3 w-3 mr-1" %>
                        Try Again
                      </button>
                      <a href="mailto:<EMAIL>"
                         class="inline-flex items-center px-3 py-2 text-xs font-medium text-yellow-800 bg-yellow-100 border border-yellow-300 rounded-md hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2">
                        <%= phosphor_icon "envelope", class: "h-3 w-3 mr-1" %>
                        Contact Support
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      </div>

    <%# No Subscription Section - Following General/Password Page Structure %>
    <% else %>
      <div class="pb-12 border-b border-stone-900/10">
        <h2 class="text-base font-semibold leading-7 text-stone-900">Subscription Plans</h2>
        <p class="mt-1 text-sm leading-6 text-stone-600">You do not have an active subscription. Choose a plan to get started.</p>

        <%# Plan Selection Cards - Dynamic from Database %>
        <div class="mt-8">
          <% available_plans = available_plans_for_new_subscriptions %>
          <% if available_plans.any? %>
            <div class="grid gap-6 <%= available_plans.size == 1 ? 'max-w-md mx-auto' : 'md:grid-cols-2 lg:grid-cols-3' %>">
              <% available_plans.each do |plan_id, plan_data| %>
                <div class="relative p-6 transition-shadow bg-white border rounded-lg shadow-sm border-stone-200 hover:shadow-md">
                  <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-stone-900"><%= plan_data[:name] %></h3>
                    <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-md bg-stone-50 text-stone-700 ring-1 ring-inset ring-stone-700/10">
                      <%= plan_data[:billing_cycle] %>
                    </span>
                  </div>

                  <p class="mb-4 text-sm text-stone-600"><%= plan_data[:description] %></p>

                  <%# Features List %>
                  <% if plan_data[:features].present? %>
                    <ul class="mb-6 space-y-2">
                      <% plan_data[:features].each do |feature| %>
                        <li class="flex items-center text-sm text-stone-600">
                          <%= phosphor_icon "check-circle", class: "h-4 w-4 text-green-500 mr-2 flex-shrink-0" %>
                          <%= feature %>
                        </li>
                      <% end %>
                    </ul>
                  <% end %>

                  <%# Pricing Display %>
                  <% if plan_data[:amount] && plan_data[:currency] %>
                    <div class="mb-4 text-center">
                      <span class="text-2xl font-bold text-stone-900">
                        <%= case plan_data[:currency].downcase
                            when 'usd' then "$#{plan_data[:amount] / 100.0}"
                            when 'eur' then "€#{plan_data[:amount] / 100.0}"
                            when 'gbp' then "£#{plan_data[:amount] / 100.0}"
                            else "#{plan_data[:amount] / 100.0} #{plan_data[:currency].upcase}"
                            end %>
                      </span>
                      <span class="text-sm text-stone-600">/ <%= plan_data[:billing_cycle].downcase %></span>
                    </div>
                  <% end %>

                  <%= button_to talent_subscription_path,
                                method: :post,
                                params: { plan: plan_id },
                                form: {
                                  data: {
                                    turbo: false
                                  }
                                },
                                class: "w-full inline-flex justify-center items-center px-4 py-2 text-sm font-medium text-white bg-stone-900 border border-transparent rounded-md shadow-sm hover:bg-stone-700 focus:outline-none focus:ring-2 focus:ring-stone-500 focus:ring-offset-2" do %>
                    Get Started with <%= plan_data[:name] %>
                  <% end %>
                </div>
              <% end %>
            </div>
          <% else %>
            <%# Fallback if no plans available %>
            <div class="text-center py-8">
              <p class="text-stone-600">No subscription plans are currently available. Please check back later.</p>
            </div>
          <% end %>
        </div>
        </div>
      </div>
    <% end %>
    </div> <%# Close content target %>

  </div> <%# Close max-w-2xl container %>
</div> <%# Close the main container div %>
