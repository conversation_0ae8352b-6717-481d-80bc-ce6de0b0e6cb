<div>
  <!-- Main Content -->
  <main class="flex flex-col h-full bg-white border rounded-md border-stone-200">
    <div class="flex-1 w-full px-16 py-8 mx-auto max-w-7xl">
      <!-- Main Container -->
      <div class="w-full">
        <!-- Header Section -->
        <div class="pb-5 mb-4 sm:pb-0">

    <div class="mb-2 text-2xl font-semibold text-stone-900">
      Jobs 
    </div>

    <div class="pb-2 mb-4 text-sm text-stone-600 ">
      List of current jobs on Ghostwrote.
    </div>
          <!-- Segmented Control for Jobs -->
          <div class="pb-4 mt-4">
            <div class="inline-flex rounded-md shadow-sm" role="group">
              <%= link_to talent_jobs_path(tab: 'all'), class: "px-4 py-2 text-sm font-medium #{@tab == 'all' ? 'text-white bg-stone-900' : 'text-stone-700 bg-white hover:bg-stone-50'} border border-stone-200 rounded-l-lg focus:z-10 focus:ring-2 focus:ring-[#6100FF] focus:text-[#6100FF]" do %>
                All Jobs<%= @all_jobs_count ? " (#{@all_jobs_count})" : "" %>
              <% end %>
              
              <%= link_to talent_jobs_path(tab: 'saved'), class: "px-4 py-2 text-sm font-medium #{@tab == 'saved' ? 'text-white bg-stone-900' : 'text-stone-700 bg-white hover:bg-stone-50'} border-t border-b border-r border-stone-200 rounded-r-lg focus:z-10 focus:ring-2 focus:ring-[#6100FF] focus:text-[#6100FF]" do %>
                Saved Jobs<%= @saved_jobs_count ? " (#{@saved_jobs_count})" : "" %>
              <% end %>
            </div>
          </div>
        </div>
        
        <!-- Job Search & Filters -->
        <div data-controller="job-search">
          <%= form_with url: talent_jobs_path, method: :get, data: { turbo_frame: "jobs-list", job_search_target: "form" } do |f| %>
            <!-- Render the filter panel partial -->
            <%= render "filter_panel", form: f %>
          <% end %>
        </div>

        <!-- Job List Results -->
        <%= turbo_frame_tag "jobs-list" do %>
          <%= render "jobs_list", jobs: @jobs %>
        <% end %>
      </div>
    </div>
  </main>
</div>
