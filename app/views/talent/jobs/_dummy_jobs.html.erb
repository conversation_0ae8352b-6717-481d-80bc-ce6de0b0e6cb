<div class="space-y-8">
  <!-- Dummy Job 1: High-paying Newsletter Role -->
  <div
    class="mx-auto mb-6 overflow-hidden bg-white border rounded-lg shadow-lg border-stone-200"
  >
    <div class="p-6">
      <!-- Header Section -->
      <div class="pb-4 mb-5 border-b border-stone-200">
        <div class="flex items-start justify-between">
          <div>
            <div class="flex items-center gap-2 mb-1">
              <span
                class="px-2 py-0.5 text-xs font-medium rounded-full bg-green-100 text-green-700"
                >Active</span
              >
              <span class="text-sm text-stone-500">TechStart Inc</span>
            </div>
            <h3 class="text-xl font-bold text-stone-900">
              Senior Email Marketing Strategist
            </h3>
          </div>
          <div class="flex items-center gap-2">
            <div class="flex items-center gap-1 text-sm text-stone-500">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="w-4 h-4"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12 6 12 12 16 14"></polyline>
              </svg>
              <span>Posted 2 hours ago</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Application Deadline -->
      <div class="mb-6 bg-blue-50 border border-blue-100 rounded-md p-4">
        <div class="flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6 mr-3 text-blue-500"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
            <line x1="16" y1="2" x2="16" y2="6"></line>
            <line x1="8" y1="2" x2="8" y2="6"></line>
            <line x1="3" y1="10" x2="21" y2="10"></line>
          </svg>
          <div>
            <div class="text-sm font-semibold text-blue-800">
              APPLICATION DEADLINE
            </div>
            <div class="text-lg font-medium text-stone-900">
              Dec 20, 2024 at 11:59 PM
            </div>
          </div>
          <div class="ml-auto">
            <span
              class="px-3 py-1 text-sm font-medium rounded-md bg-blue-200 text-blue-800"
              >3 applications</span
            >
          </div>
        </div>
      </div>

      <!-- Info Cards Grid -->
      <div class="grid grid-cols-3 gap-4 mb-6">
        <div class="p-3 border rounded-lg border-stone-200 bg-stone-50">
          <div class="mb-1 text-xs text-stone-500">Category</div>
          <div class="flex items-center text-sm font-medium text-stone-800">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-1.5 text-blue-600"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path
                d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"
              ></path>
              <polyline points="14 2 14 8 20 8"></polyline>
            </svg>
            <span>Newsletter</span>
          </div>
        </div>

        <div class="p-3 border rounded-lg border-stone-200 bg-stone-50">
          <div class="mb-1 text-xs text-stone-500">Platform</div>
          <div class="flex items-center text-sm font-medium text-stone-800">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-1.5 text-blue-500"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path
                d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"
              ></path>
            </svg>
            <span>Email</span>
          </div>
        </div>

        <div class="p-3 border rounded-lg border-stone-200 bg-stone-50">
          <div class="mb-1 text-xs text-stone-500">Duration</div>
          <div class="flex items-center text-sm font-medium text-stone-800">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-1.5 text-amber-500"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <circle cx="12" cy="12" r="10"></circle>
              <polyline points="12 6 12 12 16 14"></polyline>
            </svg>
            <span>Long Term Contract</span>
          </div>
        </div>

        <div class="p-3 border rounded-lg border-stone-200 bg-stone-50">
          <div class="mb-1 text-xs text-stone-500">Budget</div>
          <div class="flex items-center text-sm font-medium text-stone-800">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-1.5 text-green-600"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <line x1="12" y1="1" x2="12" y2="23"></line>
              <path
                d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"
              ></path>
            </svg>
            <span>$8,000 - $12,000 USD</span>
          </div>
        </div>

        <div class="p-3 border rounded-lg border-stone-200 bg-stone-50">
          <div class="mb-1 text-xs text-stone-500">Involvement</div>
          <div class="flex items-center text-sm font-medium text-stone-800">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-1.5 text-purple-600"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
            </svg>
            <span>High Involvement</span>
          </div>
        </div>

        <div class="p-3 border rounded-lg border-stone-200 bg-stone-50">
          <div class="mb-1 text-xs text-stone-500">Outcome</div>
          <div class="flex items-center text-sm font-medium text-stone-800">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-1.5 text-green-600"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <circle cx="12" cy="12" r="10"></circle>
              <circle cx="12" cy="12" r="6"></circle>
              <circle cx="12" cy="12" r="2"></circle>
            </svg>
            <span>Lead Generation</span>
          </div>
        </div>
      </div>

      <!-- Job Details Section -->
      <div class="mb-6">
        <h4 class="mb-3 text-sm font-semibold uppercase text-stone-700">
          JOB DETAILS
        </h4>
        <div class="mb-4">
          <h5 class="mb-1 text-sm text-stone-600">Description</h5>
          <p class="text-stone-800">
            We're looking for an experienced email marketing strategist to lead
            our weekly newsletter campaigns. You'll work directly with our
            marketing team to create compelling content that drives conversions
            and builds our subscriber base of 50k+ engaged readers.
          </p>
        </div>

        <div class="mb-4">
          <h5 class="mb-1 text-sm text-stone-600">Target Audience</h5>
          <p class="text-stone-800">
            Tech entrepreneurs and startup founders aged 25-45 looking for
            growth strategies and industry insights.
          </p>
        </div>

        <div class="mb-4">
          <h5 class="mb-1 text-sm text-stone-600">Topics</h5>
          <div class="flex flex-wrap gap-2">
            <span
              class="px-3 py-1 text-xs rounded-full text-stone-700 bg-stone-100"
              >SaaS Growth</span
            >
            <span
              class="px-3 py-1 text-xs rounded-full text-stone-700 bg-stone-100"
              >Email Marketing</span
            >
            <span
              class="px-3 py-1 text-xs rounded-full text-stone-700 bg-stone-100"
              >Conversion Optimization</span
            >
          </div>
        </div>
      </div>

      <!-- Footer with action button -->
      <div class="flex justify-end">
        <button
          class="px-6 py-2.5 rounded-md text-sm font-medium bg-indigo-600 text-white hover:bg-indigo-700"
        >
          Apply Now
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="inline-block w-4 h-4 ml-2"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <line x1="5" y1="12" x2="19" y2="12"></line>
            <polyline points="12 5 19 12 12 19"></polyline>
          </svg>
        </button>
      </div>
    </div>
  </div>

  <!-- Dummy Job 2: Social Media Role -->
  <div
    class="mx-auto mb-6 overflow-hidden bg-white border rounded-lg shadow-lg border-stone-200"
  >
    <div class="p-6">
      <!-- Header Section -->
      <div class="pb-4 mb-5 border-b border-stone-200">
        <div class="flex items-start justify-between">
          <div>
            <div class="flex items-center gap-2 mb-1">
              <span
                class="px-2 py-0.5 text-xs font-medium rounded-full bg-green-100 text-green-700"
                >Active</span
              >
              <span class="text-sm text-stone-500">InnovateCo</span>
              <span
                class="bg-gradient-to-r from-amber-500 to-yellow-400 text-white px-2 py-0.5 text-xs font-bold rounded-md flex items-center"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="w-3 h-3 mr-1"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <circle cx="12" cy="8" r="6"></circle>
                  <path d="M15.477 12.89 17 22l-5-3-5 3 1.523-9.11"></path>
                </svg>
                PREMIUM
              </span>
            </div>
            <h3 class="text-xl font-bold text-stone-900">
              LinkedIn Content Creator & Growth Specialist
            </h3>
          </div>
          <div class="flex items-center gap-2">
            <div class="flex items-center gap-1 text-sm text-stone-500">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="w-4 h-4"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12 6 12 12 16 14"></polyline>
              </svg>
              <span>Posted 5 hours ago</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Application Deadline -->
      <div class="mb-6 bg-blue-50 border border-blue-100 rounded-md p-4">
        <div class="flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6 mr-3 text-blue-500"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
            <line x1="16" y1="2" x2="16" y2="6"></line>
            <line x1="8" y1="2" x2="8" y2="6"></line>
            <line x1="3" y1="10" x2="21" y2="10"></line>
          </svg>
          <div>
            <div class="text-sm font-semibold text-blue-800">
              APPLICATION DEADLINE
            </div>
            <div class="text-lg font-medium text-stone-900">
              Dec 25, 2024 at 11:59 PM
            </div>
          </div>
          <div class="ml-auto">
            <span
              class="px-3 py-1 text-sm font-medium rounded-md bg-blue-200 text-blue-800"
              >7 applications</span
            >
          </div>
        </div>
      </div>

      <!-- Info Cards Grid -->
      <div class="grid grid-cols-3 gap-4 mb-6">
        <div class="p-3 border rounded-lg border-stone-200 bg-stone-50">
          <div class="mb-1 text-xs text-stone-500">Budget</div>
          <div class="flex items-center text-sm font-medium text-stone-800">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-1.5 text-green-600"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <line x1="12" y1="1" x2="12" y2="23"></line>
              <path
                d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"
              ></path>
            </svg>
            <span>$5,000 - $8,000 USD</span>
          </div>
        </div>

        <div class="p-3 border rounded-lg border-stone-200 bg-stone-50">
          <div class="mb-1 text-xs text-stone-500">Platform</div>
          <div class="flex items-center text-sm font-medium text-stone-800">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-1.5 text-blue-500"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path
                d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"
              ></path>
            </svg>
            <span>LinkedIn</span>
          </div>
        </div>

        <div class="p-3 border rounded-lg border-stone-200 bg-stone-50">
          <div class="mb-1 text-xs text-stone-500">Outcome</div>
          <div class="flex items-center text-sm font-medium text-stone-800">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-1.5 text-green-600"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <circle cx="12" cy="12" r="10"></circle>
              <circle cx="12" cy="12" r="6"></circle>
              <circle cx="12" cy="12" r="2"></circle>
            </svg>
            <span>Brand Awareness</span>
          </div>
        </div>
      </div>

      <!-- Job Details Section -->
      <div class="mb-6">
        <h4 class="mb-3 text-sm font-semibold uppercase text-stone-700">
          JOB DETAILS
        </h4>
        <div class="mb-4">
          <h5 class="mb-1 text-sm text-stone-600">Description</h5>
          <p class="text-stone-800">
            Join our team as a LinkedIn content creator! We need someone who can
            create engaging posts, articles, and campaigns that resonate with
            our B2B audience. This is a premium opportunity with excellent
            growth potential.
          </p>
        </div>

        <div class="mb-4">
          <h5 class="mb-1 text-sm text-stone-600">Topics</h5>
          <div class="flex flex-wrap gap-2">
            <span
              class="px-3 py-1 text-xs rounded-full text-stone-700 bg-stone-100"
              >B2B Marketing</span
            >
            <span
              class="px-3 py-1 text-xs rounded-full text-stone-700 bg-stone-100"
              >LinkedIn Strategy</span
            >
            <span
              class="px-3 py-1 text-xs rounded-full text-stone-700 bg-stone-100"
              >Content Marketing</span
            >
          </div>
        </div>
      </div>

      <!-- Footer with action button -->
      <div class="flex justify-end">
        <button
          class="px-6 py-2.5 rounded-md text-sm font-medium bg-indigo-600 text-white hover:bg-indigo-700"
        >
          Apply Now
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="inline-block w-4 h-4 ml-2"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <line x1="5" y1="12" x2="19" y2="12"></line>
            <polyline points="12 5 19 12 12 19"></polyline>
          </svg>
        </button>
      </div>
    </div>
  </div>
</div>
