<div class="flex flex-col h-full bg-white border rounded-md border-stone-200">
  <div class="flex-1 w-full px-4 py-8 mx-auto max-w-7xl sm:px-6 lg:px-8">

    <div class="px-16 mb-8">
      <%= link_to talent_jobs_path, class: "inline-flex items-center text-sm font-medium text-stone-600 hover:text-stone-900" do %>
        <%= phosphor_icon "arrow-left", class: "h-4 w-4 mr-1" %>
        Back to Jobs
      <% end %>
    </div>

    <div class="flex items-center justify-between px-16 mb-6">
      <div>
        <h1 class="text-2xl font-bold text-stone-900"><%= @job.title %></h1>
        <p class="max-w-2xl mt-1 text-sm leading-6 text-stone-500"><%= @job.job_category.present? ? @job.job_category.to_s.humanize : 'Not specified' %></p>
      </div>
      <div>
        <% if Current.user.job_applications.exists?(job: @job) %>
          <span class="inline-flex items-center px-4 py-2 text-sm font-medium text-green-700 bg-green-100 rounded-md">
            Applied
          </span>
          <% job_application = Current.user.job_applications.find_by(job: @job) %>
          <%= link_to talent_job_application_path(job_application), class: "ml-2 inline-flex items-center px-4 py-2 text-sm font-medium text-stone-700 bg-white border border-stone-300 rounded-md shadow-sm hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500" do %>
            View Application
          <% end %>
        <% else %>
          <%= link_to new_talent_job_job_application_path(@job), class: "inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-black rounded-md shadow-sm hover:bg-stone-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500" do %>
            Apply Now
            <%= phosphor_icon "arrow-right", class: "h-4 w-4 ml-1" %>
          <% end %>
        <% end %>
      </div>
    </div>

    <!-- Job Overview Section -->
    <div class="px-16">
      <div class="sm:px-0">
        <h3 class="text-base font-semibold leading-7 text-stone-900">Job Overview</h3>
        <p class="max-w-2xl mt-1 text-sm leading-6 text-stone-500">Basic job information</p>
      </div>

      <div class="mt-6 border-t border-stone-100">
        <dl class="divide-y divide-stone-100">
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Status</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <span class="px-2 py-1 text-xs font-medium rounded-full <%= @job.status == 'open' ? 'text-green-800 bg-green-100' : 'text-red-800 bg-red-100' %>">
                <%= @job.status&.capitalize || "Unknown" %>
              </span>
            </dd>
          </div>
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Job Category</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <%= @job.job_category.present? ? @job.job_category.to_s.humanize : 'Not specified' %>
            </dd>
          </div>
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Budget Range</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <%= @job.salary_range %>
            </dd>
          </div>
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Payment Frequency</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <%= @job.payment_frequency.present? ? @job.payment_frequency.to_s.humanize : 'Not specified' %>
            </dd>
          </div>
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Application Deadline</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <%= @job.application_deadline %>
            </dd>
          </div>
        </dl>
      </div>
    </div>

    <!-- Job Details Section -->
    <div class="px-16 pt-8">
      <div class="sm:px-0">
        <h3 class="text-base font-semibold leading-7 text-stone-900">Job Details</h3>
        <p class="max-w-2xl mt-1 text-sm leading-6 text-stone-500">Detailed Job Specifications</p>
      </div>

      <div class="mt-6 border-t border-stone-100">
        <dl class="divide-y divide-stone-100">
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Description</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <%= simple_format @job.description %>
            </dd>
          </div>
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Platform</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <%= @job.platform.present? ? @job.platform.to_s.humanize : 'Not Specified' %>
            </dd>
          </div>
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Desired Outcome</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <%= @job.outcome.present? ? @job.outcome.to_s.humanize : 'Not Specified' %>
            </dd>
          </div>
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Content Topics</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <% if @job.topics.present? && @job.topics.any? %>
                <div class="flex flex-wrap gap-2">
                  <% @job.topics.each do |topic| %>
                    <span class="px-3 py-1 text-sm font-medium text-purple-800 bg-purple-100 rounded-full"><%= topic %></span>
                  <% end %>
                </div>
              <% else %>
                <span class="text-stone-500">Not Specified</span>
              <% end %>
            </dd>
          </div>
        </dl>
      </div>
    </div>

    <% if @job.client_count.present? || @job.charge_per_client.present? || @job.business_challenge.present? || @job.offer_summary.present? || @job.useful_info.present? %>
    <!-- Client Information Section -->
    <div class="px-16 pt-8">
      <div class="sm:px-0">
        <h3 class="text-base font-semibold leading-7 text-stone-900">Client Information</h3>
        <p class="max-w-2xl mt-1 text-sm leading-6 text-stone-500">Background information about the client</p>
      </div>

      <div class="mt-6 border-t border-stone-100">
        <dl class="divide-y divide-stone-100">
          <% if @job.client_count.present? %>
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Current Client Count</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <%= @job.client_count %>
            </dd>
          </div>
          <% end %>
          
          <% if @job.charge_per_client.present? %>
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Average Charge Per Client</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <%= @job.charge_per_client %>
            </dd>
          </div>
          <% end %>
          
          <% if @job.business_challenge.present? %>
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Current Business Challenge</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <%= @job.business_challenge %>
            </dd>
          </div>
          <% end %>
          
          <% if @job.offer_summary.present? %>
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Current Offer & Target Audience</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <%= @job.offer_summary %>
            </dd>
          </div>
          <% end %>
          
          <% if @job.useful_info.present? %>
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Additional Information</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <%= @job.useful_info %>
            </dd>
          </div>
          <% end %>
        </dl>
      </div>
    </div>
    <% end %>

  </div>
</div>
