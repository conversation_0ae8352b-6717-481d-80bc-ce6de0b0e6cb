<% if defined?(mask_subscription_data?) && mask_subscription_data? %>
  <!-- Show compelling dummy data to create FOMO with limited preview -->
  <div class="fixed-height-preview" style="height: 600px; overflow: hidden;">
    <%= render "dummy_jobs" %>
  </div>
<% else %>
  <div class="space-y-8">
    <% if jobs.present? %>
      <% jobs.each do |job| %>
        <%# Use the actual boolean field 'is_premium' instead of the removed 'premium?' method %>
        <% if job.is_premium %>
          <%= render "premium_job_card", job: job %>
        <% else %>
          <%= render "regular_job_card", job: job %>
        <% end %>
      <% end %>
    <% else %>
      <p class="text-center text-stone-500">No jobs found matching your criteria.</p>
    <% end %>
  </div>
<% end %>
