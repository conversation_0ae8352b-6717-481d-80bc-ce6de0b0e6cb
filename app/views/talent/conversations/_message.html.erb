<%
  # Determine if this message is from the current user
  is_current_user = message.user_id == Current.user&.id

  # Set colors based on sender
  if is_current_user
    # Sent messages: dark background, light text (stone-900 bg, stone-100 text)
    bg_color = "#1c1917"
    text_color = "#f5f5f4"
  else
    # Received messages: light background, dark text (stone-100 bg, stone-900 text)
    bg_color = "#f5f5f4"
    text_color = "#1c1917"
  end
%>
<div class="message mb-4"
     data-controller="message"
     data-message-user-id-value="<%= message.user_id %>">
  <div class="message-bubble inline-block rounded-lg px-4 py-2 max-w-sm"
       style="background-color: <%= bg_color %> !important; color: <%= text_color %> !important;">
    <p class="text-sm leading-relaxed text-left"><%= message.body %></p>
  </div>
  <div class="text-xs text-stone-500 mt-1">
    <%= message.user.name %>, <%= time_ago_in_words(message.created_at) %> ago
  </div>
</div>
