<!-- Dummy Conversation 1: Exciting opportunity -->
<div
  class="relative p-4 border rounded-md transition duration-150 ease-in-out hover:bg-stone-50"
>
  <!-- Bookmark But<PERSON> (Positioned top-right) -->
  <div class="absolute z-10 top-2 right-2">
    <button class="p-1 text-stone-400 hover:text-yellow-500" title="Bookmark">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke-width="1.5"
        stroke="currentColor"
        class="w-5 h-5"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z"
        />
      </svg>
    </button>
  </div>

  <!-- Main Card Content -->
  <div class="block">
    <div class="flex items-center justify-between gap-4">
      <div class="flex items-center flex-shrink-0 gap-4">
        <div
          class="flex items-center justify-center w-12 h-12 rounded-full bg-blue-100"
        >
          <span class="text-xl text-blue-600">SM</span>
        </div>
      </div>

      <div class="flex-grow min-w-0">
        <p class="font-semibold truncate text-stone-900">Sarah Martinez</p>
        <p class="text-sm font-medium truncate text-stone-600">
          Senior Email Marketing Strategist
        </p>
        <p class="mt-1 text-sm truncate text-stone-500">
          Hi! I love your portfolio and think you'd be perfect for our email
          marketing role. The budget is $8k-12k. Are you available for a quick
          call this week?
        </p>
      </div>

      <div class="flex-shrink-0 text-right">
        <div class="text-sm text-stone-500 whitespace-nowrap">2 hours ago</div>
      </div>
    </div>
  </div>
</div>

<!-- Dummy Conversation 2: Follow-up message -->
<div
  class="relative p-4 border rounded-md transition duration-150 ease-in-out hover:bg-stone-50"
>
  <!-- Bookmark Button (Positioned top-right) -->
  <div class="absolute z-10 top-2 right-2">
    <button
      class="p-1 text-yellow-500 hover:text-yellow-600"
      title="Unbookmark"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="currentColor"
        class="w-5 h-5"
      >
        <path
          fill-rule="evenodd"
          d="M6.32 2.577a49.255 49.255 0 0 1 11.36 0c1.497.174 2.57 1.46 2.57 2.93V21a.75.75 0 0 1-1.085.67L12 18.089l-7.165 3.583A.75.75 0 0 1 3.75 21V5.507c0-1.47 1.073-2.756 2.57-2.93Z"
          clip-rule="evenodd"
        />
      </svg>
    </button>
  </div>

  <!-- Main Card Content -->
  <div class="block">
    <div class="flex items-center justify-between gap-4">
      <div class="flex items-center flex-shrink-0 gap-4">
        <div
          class="flex items-center justify-center w-12 h-12 rounded-full bg-green-100"
        >
          <span class="text-xl text-green-600">MJ</span>
        </div>
      </div>

      <div class="flex-grow min-w-0">
        <p class="font-semibold truncate text-stone-900">Michael Johnson</p>
        <p class="text-sm font-medium truncate text-stone-600">
          LinkedIn Content Creator & Growth Specialist
        </p>
        <p class="mt-1 text-sm truncate text-stone-500">
          Thanks for your interest! I've reviewed your application and I'm
          impressed. When can we schedule a call to discuss the project details
          and timeline?
        </p>
      </div>

      <div class="flex-shrink-0 text-right">
        <div class="text-sm text-stone-500 whitespace-nowrap">5 hours ago</div>
      </div>
    </div>
  </div>
</div>

<!-- Dummy Conversation 3: Urgent project -->
<div
  class="relative p-4 border rounded-md transition duration-150 ease-in-out hover:bg-stone-50"
>
  <!-- Bookmark Button (Positioned top-right) -->
  <div class="absolute z-10 top-2 right-2">
    <button class="p-1 text-stone-400 hover:text-yellow-500" title="Bookmark">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke-width="1.5"
        stroke="currentColor"
        class="w-5 h-5"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z"
        />
      </svg>
    </button>
  </div>

  <!-- Main Card Content -->
  <div class="block">
    <div class="flex items-center justify-between gap-4">
      <div class="flex items-center flex-shrink-0 gap-4">
        <div
          class="flex items-center justify-center w-12 h-12 rounded-full bg-purple-100"
        >
          <span class="text-xl text-purple-600">AC</span>
        </div>
      </div>

      <div class="flex-grow min-w-0">
        <p class="font-semibold truncate text-stone-900">Alex Chen</p>
        <p class="text-sm font-medium truncate text-stone-600">
          Lead Magnet Copywriter - Urgent
        </p>
        <p class="mt-1 text-sm truncate text-stone-500">
          URGENT: We need to move fast on this project. $4k budget, 1-week
          deadline. Your writing samples are exactly what we're looking for.
          Interested?
        </p>
      </div>

      <div class="flex-shrink-0 text-right">
        <div class="text-sm text-stone-500 whitespace-nowrap">1 day ago</div>
      </div>
    </div>
  </div>
</div>
