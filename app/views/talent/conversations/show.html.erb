<div>
  <!-- Main Content -->
  <main class="overflow-y-auto scrollbar-gutter-stable">
    <%# Removed the conversation list section %>
    <%# Adjusted the main container div to take full width %>
    <div class="h-full p-8 px-4 mx-auto bg-white border rounded border-stone-200 sm:px-6 lg:px-8">
      <!-- Chat Window -->
      <div class="w-full"> <%# Changed width to full %>
        <div class="flex flex-col h-full " data-controller="chat">
          <div class="px-4 py-3 border-b border-stone-200">
            <%# Added Back link %>
            <div class="mb-6">
              <%= link_to "← Back to Conversations", talent_conversations_path, class: "border border-stone-200 text-sm rounded-lg px-4 py-2 text-stone-600 hover:text-stone-800" %>
            </div>
            <div class="flex items-center justify-between"> <%# Wrap title and button %>
              <div> <%# Title container %>
                <h1 class="text-xl font-semibold">
                  Chat With <%= @conversation.users.where.not(id: Current.user.id).pluck(:first_name).join(', ') %>
                </h1>
                <% if @conversation.job %>
                  <p class="text-sm text-stone-500">Re: <%= @conversation.job.title %></p>
                <% end %>
              </div>
              <div> <%# Button container %>
                <% unless @conversation.new_record? %>
                  <% participant = @conversation.conversation_participants.find_by(user: Current.user) %>
                  <% if participant&.archived? %>
                    <%= button_to "Unarchive", unarchive_talent_conversation_path(@conversation),
                                class: "px-4 py-2 text-sm text-stone-600 underline rounded hover:bg-stone-200" %>
                  <% else %>
                    <%= button_to "Archive", archive_talent_conversation_path(@conversation),
                                class: "px-4 py-2 text-sm text-stone-600  underline rounded hover:bg-stone-200" %>
                  <% end %>
                <% end %>
              </div>
              <%# Removed extra end tag here %>
            </div>
          </div>

          <% unless @conversation.new_record? %>
            <%= turbo_stream_from @conversation %>
          <% end %>

          <div id="messages"
              data-controller="chat scroll"
              data-chat-target="messages"
              data-scroll-target="container"
              data-action="turbo:stream:received->chat#messageReceived"
              class="flex-1 overflow-y-auto messages-container">
            <%= render partial: 'talent/conversations/message', collection: @messages %>
          </div>

          <div class="p-4 border-t border-stone-200">
            <%= render partial: "talent/conversations/form", locals: { conversation: @conversation, message: @message } %>
          </div>
        </div>
      </div>
    </div>
  </main>
</div>
