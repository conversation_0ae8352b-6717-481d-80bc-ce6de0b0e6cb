<div>
  <!-- Main Content -->
  <main class="overflow-y-auto scrollbar-gutter-stable">
    <div class="flex flex-col h-full p-8 px-24 mx-auto bg-white border rounded shadow-sm border-stone-200">
      <!-- Header Section -->
      <div class="pb-5 mb-4 border-b border-stone-200 sm:pb-0">
        <div class="flex items-center justify-between">
          <h3 class="text-xl font-semibold leading-6 text-stone-900">Your Messages</h3>

          <!-- Optional: Add a search bar -->
          <div class="relative">
            <input type="search"
                   placeholder="Search conversations..."
                   class="w-64 px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
          </div>
        </div>
        <div class="mt-6 sm:mt-4">
          <%= render "talent/conversations/tabbar" %>
        </div>
      </div>

      <!-- Conversations List -->
      <div class="flex-1 space-y-4">
        <% if @conversations.any? %>
          <% @conversations.each do |conversation| %>
            <%= link_to talent_conversation_path(conversation),
                class: "block p-4 border rounded-lg hover:bg-stone-50 transition duration-150 ease-in-out" do %>
              <div class="flex items-center justify-between gap-4">
                <div class="flex items-center gap-4">
                  <!-- Add user avatar -->
                  <div class="flex items-center justify-center w-12 h-12 rounded-full bg-stone-200">
                    <% if conversation.users.where.not(id: Current.user).first&.avatar&.attached? %>
                      <%= image_tag conversation.users.where.not(id: Current.user).first.avatar,
                          class: "w-12 h-12 rounded-full object-cover" %>
                    <% else %>
                      <span class="text-xl text-stone-500">
                        <%= conversation.users.where.not(id: Current.user).first&.name.initials %>

                      </span>
                    <% end %>
                  </div>

                  <div>
                    <p class="font-semibold text-stone-900">
                      Chat with <%= conversation.users.where.not(id: Current.user.id).pluck(:first_name).join(', ') %>
                    </p>
                    <% if conversation.job %>
                      <p class="text-sm text-stone-500">Re: <%= conversation.job.title %></p>
                    <% end %>
                    <!-- Add preview of last message -->
                    <p class="max-w-md mt-1 text-sm truncate text-stone-500">
                      <%= conversation.messages.last&.body || "No messages yet" %>
                    </p>
                  </div>
                </div>

                <div class="text-right">
                  <div class="text-sm text-stone-500">
                    <%= time_ago_in_words(conversation.messages.last&.created_at || conversation.created_at) %> ago
                  </div>
                  <!-- Add unread messages indicator if any -->
                </div>
              </div>
            <% end %>
          <% end %>
        <% else %>
          <!-- Empty state -->
          <div class="py-12 text-center">
            <div class="text-stone-400">
              <svg class="w-12 h-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
              </svg>
            </div>
            <h3 class="mt-2 text-sm font-medium text-stone-900">No Conversations</h3>
            <p class="mt-1 text-sm text-stone-500">Get started by searching for jobs.</p>
          </div>
        <% end %>
      </div>
    </div>
  </main>
</div>
