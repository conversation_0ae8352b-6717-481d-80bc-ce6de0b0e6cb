<% if @conversation.persisted? %>
  <%= form_with(model: [@conversation, message],
                url: talent_conversation_messages_path(@conversation),
                class: "message-form",
                data: {
                  controller: "form-reset",
                  action: "turbo:submit-end->form-reset#reset"
                },
                id: "new_message_form") do |f| %>
    <div class="flex items-center gap-4">
      <div class="flex-1">
        <%= f.text_field :body,
            class: "w-full h-[42px] rounded-lg border border-stone-300 px-4 text-base font-sans placeholder:text-stone-400 focus:border-stone-500 focus:outline-none",
            placeholder: "Type a message...",
            data: { form_reset_target: "input" } %>
      </div>
      <button type="submit" class="flex items-center justify-center w-10 h-10 border-0 rounded-lg cursor-pointer bg-stone-900 hover:bg-stone-800 transition-colors">
        <%= phosphor_icon "paper-plane-right", class: "p-0.5 text-stone-50" %>
      </button>
    </div>
  <% end %>
<% else %>
  <%= form_with(model: message,
                url: "#",
                class: "message-form opacity-50 pointer-events-none",
                data: {
                  controller: "form-reset",
                  action: "turbo:submit-end->form-reset#reset"
                },
                id: "new_message_form") do |f| %>
    <div class="flex items-center gap-4">
      <div class="flex-1">
        <%= f.text_field :body,
            class: "w-full h-[42px] rounded-lg border border-stone-300 px-4 text-base font-sans placeholder:text-stone-400 focus:border-stone-500 focus:outline-none",
            placeholder: "Type a message...",
            data: { form_reset_target: "input" },
            disabled: true %>
      </div>
      <button type="submit" class="flex items-center justify-center w-10 h-10 border-0 rounded-lg cursor-pointer bg-stone-900 hover:bg-stone-800 transition-colors" disabled>
        <%= phosphor_icon "paper-plane-right", class: "p-0.5 text-stone-50" %>
      </button>
    </div>
  <% end %>
<% end %>
