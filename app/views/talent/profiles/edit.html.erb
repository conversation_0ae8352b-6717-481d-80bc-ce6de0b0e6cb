<div class="flex flex-col h-full bg-white border rounded-md border-stone-200">
  <div class="flex-1 w-full px-4 py-8 mx-auto max-w-7xl sm:px-6 lg:px-8">
    <div class="flex flex-col px-16 mb-8">
      <h1 class="text-2xl font-bold text-stone-900">
        Edit your Ghostwriting Profile
      </h1>
      <p class="mt-1 text-sm leading-6 text-stone-600">
        Edit information for your profile.
      </p>
    </div>

    <%= form_with(model: @profile, url: talent_profile_path, method: @profile.new_record? ? :post : :patch, class: "px-16", data: { controller: "profile-form" }) do |f| %>
      <% if @profile.errors.any? %>
        <div class="p-4 mb-6 rounded-md bg-red-50">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg
                class="w-5 h-5 text-red-400"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
              >
                <path
                  fill-rule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z"
                  clip-rule="evenodd"
                />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">
                Please fix the following issues:
              </h3>
              <div class="mt-2 text-sm text-red-700">
                <ul class="pl-5 space-y-1 list-disc">
                  <% @profile.errors.messages.each do |field, messages| %>
                    <li>
                      <strong><%= field.to_s.humanize %>:</strong>
                      <%= messages.join(', ') %>
                    </li>
                  <% end %>
                </ul>
              </div>
            </div>
          </div>
        </div>
      <% end %>

      <div class="space-y-12">
        <div class="grid grid-cols-2 py-12 border-y gap-x-8 gap-y-10 border-stone-900/10 md:grid-cols-3">
          <div>
            <h2 class="text-base font-semibold leading-7 text-stone-900">Profile</h2>
            <p class="mt-1 text-sm leading-6 text-stone-600">These details will be publicly displayed to prospects on the platform.</p>
          </div>

          <div class="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6 md:col-span-2">
            <div class="sm:col-span-4">
              <%= f.label :profile_headline, class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= f.text_field :profile_headline, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6" %>
              </div>
            </div>

            <div class="col-span-5">
              <%= f.label :about_section, class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= f.text_area :about_section, rows: 3, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6" %>
              </div>
              <p class="mt-3 text-sm leading-6 text-stone-600">Write a few sentences about yourself.</p>
            </div>

            <div class="col-span-5">
              <%= f.label "Professional Bio", class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= f.text_area "Professional Bio", rows: 3, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6" %>
              </div>
              <p class="mt-3 text-sm leading-6 text-stone-600">Your professional bio.</p>
            </div>

            <div class="col-span-5">
              <%= f.label "Looking For", class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= f.text_area "Looking For", rows: 2, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6" %>
              </div>
              <p class="mt-3 text-sm leading-6 text-stone-600">Describe what type of work you're looking for.</p>
            </div>
          </div>
        </div>

        <div class="grid grid-cols-2 pb-12 border-b gap-x-8 gap-y-10 border-stone-900/10 md:grid-cols-3">
          <div>
            <h2 class="text-base font-semibold leading-7 text-stone-900">Location Information</h2>
            <p class="mt-1 text-sm leading-6 text-stone-600">Let clients know where you're located.</p>
          </div>

          <div class="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6 md:col-span-2">

            <div class="sm:col-span-6">
              <h3 class="text-sm font-medium leading-6 text-stone-900">Where do you primarily work from?</h3>
              <div class="mt-4 space-y-4">
                <% [
                  ["north_america", "North America"],
                  ["united_kingdom", "United Kingdom"],
                  ["europe", "Europe"],
                  ["africa", "Africa"],
                  ["asia", "Asia"],
                  ["south_america", "South America"],
                  ["india", "India"]
                ].each do |(location_value, location_name)| %>
                  <div class="flex items-center">
                    <%= f.radio_button :location, location_value, id: "location_#{location_value}", class: "h-4 w-4 border-stone-300 text-stone-600 focus:ring-stone-600" %>
                    <label for="location_<%= location_value %>" class="block ml-3 text-sm font-medium leading-6 text-stone-900">
                      <%= "#{location_name}" %>
                    </label>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        </div>

        <div class="grid grid-cols-2 pb-12 border-b gap-x-8 gap-y-10 border-stone-900/10 md:grid-cols-3">
          <div>
            <h2 class="text-base font-semibold leading-7 text-stone-900">Ghostwriting Details</h2>
            <p class="mt-1 text-sm leading-6 text-stone-600">Information about your ghostwriting services.</p>
          </div>

          <div class="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6 md:col-span-2">
            <div class="sm:col-span-4">
              <fieldset>
                <legend class="text-sm font-medium leading-6 text-stone-900">Agency or Independent?</legend>
                <div class="grid grid-cols-2 mt-6 gap-y-6 sm:grid-cols-2 sm:gap-x-4">
                  <label 
                    class="relative flex p-4 bg-white border rounded-lg shadow-sm cursor-pointer focus:outline-none <%= @profile.is_agency? ? 'border-stone-600 ring-2 ring-stone-600' : 'border-stone-300' %>"
                    data-action="click->profile-form#selectAgencyOption"
                    data-profile-form-target="agencyOption"
                  >
                    <%= f.radio_button :is_agency, true, class: "sr-only", data: { profile_form_target: "agencyRadio" } %>
                    <span class="flex flex-1">
                      <span class="flex flex-col">
                        <span class="block text-sm font-medium text-stone-900">I run an Agency</span>
                      </span>
                    </span>
                    <svg 
                      class="<%= @profile.is_agency? ? '' : 'hidden' %> w-5 h-5 text-stone-700" 
                      viewBox="0 0 20 20" 
                      fill="currentColor" 
                      aria-hidden="true"
                      data-profile-form-target="agencyIcon"
                    >
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
                    </svg>
                    <span class="absolute border-2 rounded-lg pointer-events-none -inset-px" aria-hidden="true"></span>
                  </label>

                  <label 
                    class="relative flex p-4 bg-white border rounded-lg shadow-sm cursor-pointer focus:outline-none <%= @profile.is_agency? ? 'border-stone-300' : 'border-stone-600 ring-2 ring-stone-600' %>"
                    data-action="click->profile-form#selectIndependentOption"
                    data-profile-form-target="independentOption"
                  >
                    <%= f.radio_button :is_agency, false, class: "sr-only", data: { profile_form_target: "independentRadio" } %>
                    <span class="flex flex-1">
                      <span class="flex flex-col">
                        <span class="block text-sm font-medium text-stone-900">I work Independently</span>
                      </span>
                    </span>
                    <svg 
                      class="<%= @profile.is_agency? ? 'hidden' : '' %> w-5 h-5 text-stone-700" 
                      viewBox="0 0 20 20" 
                      fill="currentColor" 
                      aria-hidden="true"
                      data-profile-form-target="independentIcon"
                    >
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
                    </svg>
                    <span class="absolute border-2 rounded-lg pointer-events-none -inset-px" aria-hidden="true"></span>
                  </label>
                </div>
              </fieldset>
            </div>

            <div class="sm:col-span-6">
              <h3 class="text-sm font-medium leading-6 text-stone-900">Ghostwriter Type</h3>
              <div class="mt-4 space-y-4">
                <%= hidden_field_tag 'talent_profile[ghostwriter_type][]', nil %>
                <% ["Social Media", "Lead Magnets", "Newsletters", "Books", "Articles", "Speeches", "Scripts"].each do |type| %>
                  <div class="flex items-center">
                    <%= check_box_tag 'talent_profile[ghostwriter_type][]', type, @profile.ghostwriter_type&.include?(type), id: "type_#{type.parameterize}", class: "h-4 w-4 rounded border-stone-300 text-stone-600 focus:ring-stone-600" %>
                    <label for="type_<%= type.parameterize %>" class="block ml-3 text-sm font-medium leading-6 text-stone-900">
                      <%= type %>
                    </label>
                  </div>
                <% end %>
              </div>
            </div>

            <div class="sm:col-span-6">
              <h3 class="text-sm font-medium leading-6 text-stone-900">Achievement Badges</h3>
              <p class="mt-1 text-sm leading-6 text-stone-500">Enter badges separated by commas</p>
              <div class="mt-2">
                <%= f.text_field :achievement_badges, value: @profile.achievement_badges.is_a?(Array) ? @profile.achievement_badges.join(", ") : @profile.achievement_badges, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6" %>
              </div>
            </div>

            <div class="sm:col-span-6">
              <h3 class="text-sm font-medium leading-6 text-stone-900">Preferred Content Topics</h3>
              <p class="mt-1 text-sm leading-6 text-stone-500">Enter topics separated by commas</p>
              <div class="mt-2">
                <%= f.text_field :niches, value: @profile.niches.is_a?(Array) ? @profile.niches.join(", ") : @profile.niches, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6" %>
              </div>
            </div>

            <div class="sm:col-span-6">
              <h3 class="text-sm font-medium leading-6 text-stone-900">Social Media Specialties</h3>
              <div class="mt-4 space-y-4">
                <%= hidden_field_tag 'talent_profile[social_media_specialty][]', nil %>
                <% ["Twitter (X)", "LinkedIn", "Instagram", "Threads"].each do |platform| %>
                  <div class="flex items-center">
                    <%= check_box_tag 'talent_profile[social_media_specialty][]', platform, @profile.social_media_specialty&.include?(platform), id: "platform_#{platform.parameterize}", class: "h-4 w-4 rounded border-stone-300 text-stone-600 focus:ring-stone-600" %>
                    <label for="platform_<%= platform.parameterize %>" class="block ml-3 text-sm font-medium leading-6 text-stone-900">
                      <%= platform %>
                    </label>
                  </div>
                <% end %>
              </div>
            </div>

            <div class="sm:col-span-6">
              <h3 class="text-sm font-medium leading-6 text-stone-900">Which outcome(s) is your offer focused on?</h3>
              <div class="mt-4 space-y-4">
                <%= hidden_field_tag 'talent_profile[outcomes][]', nil %>
                <% ["Followers", "Leads", "Product Sales", "Newsletter Subscribers"].each do |outcome| %>
                  <div class="flex items-center">
                    <%= check_box_tag 'talent_profile[outcomes][]', outcome, @profile.outcomes&.include?(outcome), id: "outcome_#{outcome.parameterize}", class: "h-4 w-4 rounded border-stone-300 text-stone-600 focus:ring-stone-600" %>
                    <label for="outcome_<%= outcome.parameterize %>" class="block ml-3 text-sm font-medium leading-6 text-stone-900">
                      <%= outcome %>
                    </label>
                  </div>
                <% end %>
              </div>
            </div>

            <div class="sm:col-span-6">
              <h3 class="text-sm font-medium leading-6 text-stone-900">Skills</h3>
              <p class="mt-1 text-sm leading-6 text-stone-500">Enter skills separated by commas</p>
              <div class="mt-2">
                <%= f.text_field :skills, value: @profile.skills.is_a?(Array) ? @profile.skills.join(", ") : @profile.skills, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6" %>
              </div>
            </div>

            <div class="sm:col-span-6">
              <h3 class="text-sm font-medium leading-6 text-stone-900">Niches</h3>
              <p class="mt-1 text-sm leading-6 text-stone-500">Enter niches separated by commas</p>
              <div class="mt-2">
                <%= f.text_field :niches, value: @profile.niches.is_a?(Array) ? @profile.niches.join(", ") : @profile.niches, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6" %>
              </div>
            </div>
          </div>
        </div>

        <div class="grid grid-cols-2 pb-12 border-b gap-x-8 gap-y-10 border-stone-900/10 md:grid-cols-3">
          <div>
            <h2 class="text-base font-semibold leading-7 text-stone-900">Pricing & Availability</h2>
            <p class="mt-1 text-sm leading-6 text-stone-600">Information about your pricing and availability.</p>
          </div>

          <div class="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6 md:col-span-2">
            <div class="sm:col-span-3">
              <%= f.label :pricing_model, class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= f.select :pricing_model, [
                  ["Hourly", "hourly"],
                  ["Fixed Price", "fixed_price"],
                  ["Retainer", "retainer"],
                  ["Project Based", "project_based"]
                ], {}, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6" %>
              </div>
            </div>

            <div class="sm:col-span-3">
              <%= f.label :availability_status, class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= f.select :availability_status, [
                  ["Available", "available"],
                  ["Limited", "limited"],
                  ["Unavailable", "unavailable"]
                ], {}, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6" %>
              </div>
            </div>

            <div class="sm:col-span-3">
              <%= f.label :price_range_min, "Minimum Price (USD)", class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= f.number_field :price_range_min, min: 0, step: 100, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6" %>
              </div>
            </div>

            <div class="sm:col-span-3">
              <%= f.label :price_range_max, "Maximum Price (USD)", class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= f.number_field :price_range_max, min: 0, step: 100, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6" %>
              </div>
            </div>
          </div>
        </div>

        <div class="grid grid-cols-2 pb-12 border-b gap-x-8 gap-y-10 border-stone-900/10 md:grid-cols-3">
          <div>
            <h2 class="text-base font-semibold leading-7 text-stone-900">Links & Portfolio</h2>
            <p class="mt-1 text-sm leading-6 text-stone-600">Your online presence and portfolio information.</p>
          </div>

          <div class="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6 md:col-span-2">
            <div class="sm:col-span-4">
              <%= f.label :website_url, "Personal Website Link", class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= f.url_field :website_url, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6" %>
              </div>
            </div>

            <div class="sm:col-span-4">
              <%= f.label :portfolio_link, "Portfolio Link", class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= f.url_field :portfolio_link, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6" %>
              </div>
            </div>

            <div class="sm:col-span-4">
              <%= f.label :vsl_link, "Video Sales Letter Link", class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= f.url_field :vsl_link, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6" %>
              </div>
            </div>

            <div class="sm:col-span-4">
              <%= f.label :linkedin_url, "LinkedIn URL", class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= f.url_field :linkedin_url, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6" %>
              </div>
            </div>

            <div class="sm:col-span-4">
              <%= f.label :x_url, "X (Twitter) URL", class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= f.url_field :x_url, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6" %>
              </div>
            </div>

            <div class="sm:col-span-4">
              <%= f.label :instagram_url, "Instagram URL", class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= f.url_field :instagram_url, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6" %>
              </div>
            </div>

            <div class="sm:col-span-4">
              <%= f.label :threads_url, "Threads URL", class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= f.url_field :threads_url, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6" %>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="flex items-center justify-end mt-6 gap-x-6">
        <%= link_to "Cancel", @profile.new_record? ? root_path : talent_profile_path, class: "text-sm font-semibold leading-6 text-stone-900" %>
        <%= f.submit @profile.new_record? ? "Create Profile" : "Save Changes", class: "px-3 py-2 text-sm font-semibold text-white bg-stone-600 rounded-md shadow-sm hover:bg-stone-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-stone-600" %>
      </div>
    <% end %>
  </div>
</div>
