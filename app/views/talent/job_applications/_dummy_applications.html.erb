<!-- Dummy Application 1: Offered Status -->
<div class="p-6 border-b last:border-none border-stone-200">
  <div class="flex items-start justify-between">
    <div class="flex-1">
      <!-- Job Title and Status Header -->
      <div class="flex items-center justify-between mb-2">
        <div class="flex items-center">
          <div class="mr-4 text-lg font-medium text-stone-900 hover:underline">
            Senior Email Marketing Strategist
          </div>
          <p
            class="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-0.5 rounded-md"
          >
            Offered
          </p>
        </div>
        <!-- Add application date -->
        <p class="text-xs text-stone-500">Applied 5 days ago</p>
      </div>

      <!-- Company name -->
      <p class="mt-1 text-sm text-stone-600">TechStart Inc</p>

      <!-- Application Progress Timeline -->
      <div class="mt-4 mb-4">
        <div class="relative">
          <div class="absolute inset-0 flex items-center" aria-hidden="true">
            <div class="w-full border-t border-stone-200"></div>
          </div>
          <div class="relative flex justify-between">
            <div class="flex flex-col items-center">
              <span class="relative flex w-3 h-3">
                <span class="bg-stone-900 h-3 w-3 rounded-full"></span>
              </span>
              <span class="mt-1 text-xs font-medium text-stone-900"
                >Applied</span
              >
            </div>
            <div class="flex flex-col items-center">
              <span class="relative flex w-3 h-3">
                <span class="bg-stone-900 h-3 w-3 rounded-full"></span>
              </span>
              <span class="mt-1 text-xs font-medium text-stone-900"
                >Reviewed</span
              >
            </div>
            <div class="flex flex-col items-center">
              <span class="relative flex w-3 h-3">
                <span class="bg-stone-900 h-3 w-3 rounded-full"></span>
              </span>
              <span class="mt-1 text-xs font-medium text-stone-900"
                >Qualified</span
              >
            </div>
            <div class="flex flex-col items-center">
              <span class="relative flex w-3 h-3">
                <span class="bg-stone-900 h-3 w-3 rounded-full"></span>
                <span
                  class="absolute inline-flex w-full h-full rounded-full opacity-75 animate-ping bg-stone-400"
                ></span>
              </span>
              <span class="mt-1 text-xs font-medium text-stone-900"
                >Offered</span
              >
            </div>
            <div class="flex flex-col items-center">
              <span class="relative flex w-3 h-3">
                <span class="bg-stone-200 h-3 w-3 rounded-full"></span>
              </span>
              <span class="mt-1 text-xs text-stone-400">Accepted</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Next Steps Information -->
      <div class="mt-3 mb-4 text-sm">
        <div class="p-3 border border-blue-200 rounded-md bg-blue-50">
          <p class="text-blue-700">
            <span class="font-medium">Action required:</span> Review and respond
            to your job offer.
          </p>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex items-center justify-between gap-3 mt-4">
        <div>
          <p class="text-xs text-stone-500">Status updated: 2 hours ago</p>
        </div>

        <div class="flex">
          <button
            class="mr-2 inline-flex items-center px-4 py-2 text-sm font-medium rounded-md text-stone-700 bg-white hover:text-stone-900 underline focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500"
          >
            View Job
          </button>
          <button
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-stone-900 hover:bg-stone-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500"
          >
            Edit my Application
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Dummy Application 2: Qualified Status -->
<div class="p-6 border-b last:border-none border-stone-200">
  <div class="flex items-start justify-between">
    <div class="flex-1">
      <!-- Job Title and Status Header -->
      <div class="flex items-center justify-between mb-2">
        <div class="flex items-center">
          <div class="mr-4 text-lg font-medium text-stone-900 hover:underline">
            LinkedIn Content Creator & Growth Specialist
          </div>
          <p
            class="bg-green-100 text-green-800 text-xs font-medium px-2 py-0.5 rounded-md"
          >
            Qualified
          </p>
        </div>
        <!-- Add application date -->
        <p class="text-xs text-stone-500">Applied 1 week ago</p>
      </div>

      <!-- Company name -->
      <p class="mt-1 text-sm text-stone-600">InnovateCo</p>

      <!-- Application Progress Timeline -->
      <div class="mt-4 mb-4">
        <div class="relative">
          <div class="absolute inset-0 flex items-center" aria-hidden="true">
            <div class="w-full border-t border-stone-200"></div>
          </div>
          <div class="relative flex justify-between">
            <div class="flex flex-col items-center">
              <span class="relative flex w-3 h-3">
                <span class="bg-stone-900 h-3 w-3 rounded-full"></span>
              </span>
              <span class="mt-1 text-xs font-medium text-stone-900"
                >Applied</span
              >
            </div>
            <div class="flex flex-col items-center">
              <span class="relative flex w-3 h-3">
                <span class="bg-stone-900 h-3 w-3 rounded-full"></span>
              </span>
              <span class="mt-1 text-xs font-medium text-stone-900"
                >Reviewed</span
              >
            </div>
            <div class="flex flex-col items-center">
              <span class="relative flex w-3 h-3">
                <span class="bg-stone-900 h-3 w-3 rounded-full"></span>
                <span
                  class="absolute inline-flex w-full h-full rounded-full opacity-75 animate-ping bg-stone-400"
                ></span>
              </span>
              <span class="mt-1 text-xs font-medium text-stone-900"
                >Qualified</span
              >
            </div>
            <div class="flex flex-col items-center">
              <span class="relative flex w-3 h-3">
                <span class="bg-stone-200 h-3 w-3 rounded-full"></span>
              </span>
              <span class="mt-1 text-xs text-stone-400">Offered</span>
            </div>
            <div class="flex flex-col items-center">
              <span class="relative flex w-3 h-3">
                <span class="bg-stone-200 h-3 w-3 rounded-full"></span>
              </span>
              <span class="mt-1 text-xs text-stone-400">Accepted</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Next Steps Information -->
      <div class="mt-3 mb-4 text-sm">
        <div class="p-3 border rounded-md bg-stone-50 border-stone-200">
          <p class="text-stone-700">
            <span class="font-medium">Next:</span> You may receive an offer if
            selected as the final candidate.
          </p>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex items-center justify-between gap-3 mt-4">
        <div>
          <p class="text-xs text-stone-500">Status updated: 1 day ago</p>
        </div>

        <div class="flex">
          <button
            class="mr-2 inline-flex items-center px-4 py-2 text-sm font-medium rounded-md text-stone-700 bg-white hover:text-stone-900 underline focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500"
          >
            View Job
          </button>
          <button
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-stone-900 hover:bg-stone-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500"
          >
            Edit my Application
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
