<div class="max-w-5xl min-h-screen px-4 py-8 mx-auto bg-stone-50 sm:px-6 lg:px-8">
  <!-- Header Section -->
  <div class="mb-8">
    <%= link_to talent_job_path(@job), class: "inline-flex items-center text-sm font-medium text-stone-600 hover:text-stone-800 transition-colors duration-200" do %>
      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
      </svg>
      Back to Job
    <% end %>
  </div>
  <!-- Application Form Container -->
  <div class="overflow-hidden bg-white border rounded-lg shadow-lg border-stone-200">
    <!-- Header -->
    <div class="px-6 py-8 border-b bg-stone-50 text-stone-900">
      <div class="flex items-center">
        <div>
          <h1 class="text-2xl font-bold">Apply for <%= @job.title %></h1>
          <p class="mt-1 text-stone-600">Show us why you're the perfect fit for this position</p>
        </div>
      </div>
    </div>
    <!-- Error Messages -->
    <% if @errors.present? %>
      <div class="p-4 mx-6 mt-6 border border-red-200 rounded-lg bg-red-50">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">
              <%= pluralize(@errors.count, "error") %> prohibited this application from being saved:
            </h3>
            <ul class="mt-2 text-sm text-red-700 list-disc list-inside">
              <% @errors.each do |error| %>
                <li><%= error %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    <% end %>
    <!-- Form Content -->
    <%= form_with(model: @job_application, 
                url: talent_job_job_applications_path(@job),
                method: :post,
                multipart: true, 
                data: { controller: 'application-wizard' }) do |f| %>
      <%= hidden_field_tag :final_submission, true %>
      <div class="px-6 py-8 space-y-10">
        <!-- Basic Information Section -->
        <div class="group">
          <div class="flex items-center mb-6">
            <div class="flex items-center justify-center w-8 h-8 mr-3 rounded-full bg-stone-100">
              <svg class="w-4 h-4 text-stone-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-semibold text-stone-900">Basic Information</h3>
              <p class="text-sm text-stone-600">Tell us about your interest in this position</p>
            </div>
          </div>
          <div class="p-6 bg-stone-50 rounded-xl">
            <div>
              <%= f.label :application_letter, "Cover Letter", class: "block text-sm font-semibold text-stone-700 mb-3" %>
              <div class="relative">
                <%= f.text_area :application_letter, rows: 6, required: true,
                    class: "shadow-sm focus:ring-2 focus:ring-stone-500 focus:border-stone-500 block w-full text-sm border-stone-300 rounded-lg transition-all duration-200",
                    data: { action: "input->application-wizard#handleInput" },
                    placeholder: "Share your enthusiasm and explain why you're the perfect fit for this role..." %>
                <div class="absolute inset-y-0 right-0 flex items-start pt-3 pr-3">
                  <svg class="w-5 h-5 text-stone-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
                  </svg>
                </div>
              </div>
              <p class="mt-2 text-xs text-stone-500">Required • Minimum 50 characters</p>
            </div>
          </div>
        </div>
        <!-- Social Proof and Experience Section -->
        <div class="group">
          <div class="flex items-center mb-6">
            <div class="flex items-center justify-center w-8 h-8 mr-3 rounded-full bg-stone-100">
              <svg class="w-4 h-4 text-stone-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-semibold text-stone-900">Social Proof and Experience</h3>
              <p class="text-sm text-stone-600">Upload your documents and work samples</p>
            </div>
          </div>
          <div class="p-6 bg-stone-50 rounded-xl">
            <div>
              <%= f.label :documents, "Documents", class: "block text-sm font-semibold text-stone-700 mb-3" %>
              <div class="relative">
                <div class="p-6 text-center transition-colors duration-200 border-2 border-dashed rounded-lg border-stone-300 hover:border-stone-500">
                  <%= f.file_field :documents, multiple: true, accept: ".pdf,.doc,.docx",
                      class: "absolute inset-0 w-full h-full opacity-0 cursor-pointer",
                      data: { action: "change->application-wizard#handleInput" } %>
                  <div class="space-y-2">
                    <svg class="w-8 h-8 mx-auto text-stone-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
                    </svg>
                    <div class="text-sm text-stone-600">
                      <span class="font-medium text-stone-700">Click to upload</span> multiple documents
                    </div>
                    <p class="text-xs text-stone-500">PDF, DOC, or DOCX (max 10MB each) • Multiple files supported</p>
                  </div>
                </div>
              </div>
              <p class="mt-2 text-xs text-stone-500">Upload your resume, portfolio samples, writing samples, certifications, or other relevant documents</p>
            </div>
          </div>
        </div>
      </div>
      <!-- Action Buttons -->
      <div class="flex items-center justify-end px-6 py-6 border-t bg-stone-50 border-stone-200">
        <div class="flex items-center space-x-3">
          <%= link_to "Cancel", talent_job_path(@job), class: "inline-flex items-center px-4 py-2 border border-stone-300 shadow-sm text-sm font-medium rounded-lg text-stone-700 bg-white hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500 transition-all duration-200" %>
          <%= f.submit "Submit Application", class: "inline-flex items-center px-6 py-2 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-stone-800 hover:bg-stone-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500 transition-all duration-200",
              data: { application_wizard_target: "submitButton" } %>
        </div>
      </div>
    <% end %>
  </div>
</div>
