<div id="subscription-gate"
     class="fixed top-0 left-0 right-0 bottom-0 z-50 flex items-center justify-center"
     style="margin-left: var(--sidebar-width, 280px);">
  <!-- Backdrop with blur effect only over main content -->
  <div class="absolute inset-0 bg-stone-900/60 backdrop-blur"></div>
  <!-- Modal content positioned above backdrop -->
  <div class="relative w-full max-w-lg p-6 bg-white shadow-xl rounded-2xl ring-1 ring-stone-300 z-10">
    <h2 class="text-lg font-semibold text-stone-900">
      Upgrade to unlock messaging, jobs, and applications
    </h2>
    <p class="mt-2 text-sm text-stone-600">
      Your profile stays visible and can receive chat requests, but access is restricted until you subscribe.
    </p>

    <!-- Benefits List -->
    <div class="mt-4">
      <ul class="space-y-2 text-sm text-stone-700">
        <li class="flex items-start">
          <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
          <span>See all jobs and full compensation details</span>
        </li>
        <li class="flex items-start">
          <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
          <span>Apply to any role without limits</span>
        </li>
        <li class="flex items-start">
          <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
          <span>Reply to chat requests and messages instantly</span>
        </li>
        <li class="flex items-start">
          <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
          <span>Appear higher in recruiter searches (Premium badge)</span>
        </li>
        <li class="flex items-start">
          <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
          <span>Track application status in real time</span>
        </li>
      </ul>
    </div>

    <!-- CTA Button -->
    <% plans = @available_plans || available_plans_for_new_subscriptions %>
    <% if plans.present? %>
      <div class="mt-6">
        <% plans.each do |plan_id, data| %>
          <%= button_to talent_subscription_path,
                        method: :post,
                        params: { plan: plan_id },
                        form: {
                          data: {
                            turbo: false
                          }
                        },
                        class: "w-full inline-flex justify-center items-center rounded-lg px-4 py-3 text-sm font-medium bg-stone-900 text-white hover:bg-stone-800" do %>
            I want more ghostwriting opportunities
          <% end %>
        <% end %>
      </div>
    <% end %>
  </div>
</div>

<style>
  /* Ensure sidebar width is available as CSS variable */
  :root {
    --sidebar-width: 280px;
  }

  /* Responsive sidebar width */
  @media (max-width: 1024px) {
    :root {
      --sidebar-width: 0px;
    }

    #subscription-gate {
      margin-left: 0 !important;
    }
  }

  /* Blur effect for main content area when subscription gate is active */
  .subscription-gate-blur {
    filter: blur(4px);
    transition: filter 0.3s ease-in-out;
  }

  /* Remove backdrop blur from overlay since we're blurring the content directly */
  #subscription-gate .backdrop-blur {
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
  }
</style>
