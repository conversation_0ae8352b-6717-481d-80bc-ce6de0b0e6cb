<%# Social Media Category Steps %>
<div data-section-name="social_media" class="hidden">

  <!-- Social Media Platform Step -->
  <div data-step-name="social_media_platform" data-step-id="platform" data-form-wizard-target="step" class="hidden">
    <div class="mb-6">
      <h3 class="text-2xl font-semibold text-stone-900 mb-2">Social Media Platform</h3>
      <p class="text-stone-600">Choose the platform where you need content created.</p>
    </div>
    <div class="mb-6">
      <%= form.label :platform, "Which platform do you need content for?",
          class: "block text-sm font-medium text-stone-700 mb-4" %>
      <div class="mt-3 space-y-3">
        <% Job.platforms.each do |key, value| %>
          <%
            # Map platform keys to appropriate Phosphor icons
            platform_icon = case key
            when "x_twitter"
              phosphor_icon("twitter-logo", class: "h-5 w-5 text-stone-600")
            when "linkedin"
              phosphor_icon("linkedin-logo", class: "h-5 w-5 text-stone-600")
            when "instagram"
              phosphor_icon("instagram-logo", class: "h-5 w-5 text-stone-600")
            when "youtube"
              phosphor_icon("youtube-logo", class: "h-5 w-5 text-stone-600")
            when "substack"
              phosphor_icon("newspaper", class: "h-5 w-5 text-stone-600")
            when "threads"
              phosphor_icon("threads-logo", class: "h-5 w-5 text-stone-600")
            when "not_sure_yet"
              phosphor_icon("question", class: "h-5 w-5 text-stone-600")
            else
              phosphor_icon("globe", class: "h-5 w-5 text-stone-600")
            end

            # Map platform keys to display names
            platform_name = case key
            when "x_twitter"
              "X (Twitter)"
            when "not_sure_yet"
              "Not sure yet"
            else
              key.humanize
            end
          %>
          <label class="inline-flex items-center p-3 border border-stone-200 rounded-md hover:bg-stone-50 cursor-pointer transition-colors duration-200">
            <%= form.radio_button :platform, key,
                class: "focus:ring-stone-500 h-4 w-4 text-stone-600 border-stone-300" %>
            <span class="ml-3 flex items-center">
              <%= platform_icon %>
              <span class="ml-2 text-sm text-stone-700"><%= platform_name %></span>
            </span>
          </label>
        <% end %>
      </div>
      <span class="error-message hidden text-red-500 text-sm mt-2">Please select a platform</span>
    </div>
    <div class="step-error-message hidden mt-6 p-4 text-red-700 bg-red-50 border border-red-200 rounded-md text-sm">
      Please select a platform before continuing.
    </div>
  </div>

  <!-- Social Media Goal Step -->
  <div data-step-name="social_media_goal" data-step-id="goal" data-form-wizard-target="step" class="hidden">
    <div class="mb-6">
      <h3 class="text-2xl font-semibold text-stone-900 mb-2">Social Media Goal</h3>
      <p class="text-stone-600">Define the primary objective for your social media content strategy.</p>
    </div>
    <div class="mb-6">
      <%= form.label :outcome, "What's your main goal for social media content?",
          class: "block text-sm font-medium text-stone-700 mb-4" %>

      <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
        <!-- Gain Followers Card -->
        <label class="relative flex cursor-pointer rounded-lg border border-stone-300 bg-white p-4 shadow-sm focus:outline-none hover:border-stone-400 transition-colors duration-200">
          <%= form.radio_button :outcome, "followers",
              class: "sr-only",
              data: { action: "change->form-wizard#handleGoalChange" } %>
          <span class="flex flex-1">
            <span class="flex flex-col">
              <span class="block text-sm font-medium text-stone-900">Followers</span>
              <span class="mt-1 flex items-center text-sm text-stone-500">
                Grow your social media audience and increase your reach organically
              </span>
            </span>
          </span>
          <!-- Selected state indicator -->
          <svg class="h-5 w-5 text-[#6100FF] opacity-0 transition-opacity duration-200" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.236 4.53L7.53 10.53a.75.75 0 00-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
          </svg>
          <!-- Focus ring -->
          <span class="pointer-events-none absolute -inset-px rounded-lg border-2 border-transparent" aria-hidden="true"></span>
        </label>

        <!-- Generate Leads Card -->
        <label class="relative flex cursor-pointer rounded-lg border border-stone-300 bg-white p-4 shadow-sm focus:outline-none hover:border-stone-400 transition-colors duration-200">
          <%= form.radio_button :outcome, "leads",
              class: "sr-only",
              data: { action: "change->form-wizard#handleGoalChange" } %>
          <span class="flex flex-1">
            <span class="flex flex-col">
              <span class="block text-sm font-medium text-stone-900">Leads</span>
              <span class="mt-1 flex items-center text-sm text-stone-500">
                Attract potential customers and capture their contact information
              </span>
            </span>
          </span>
          <!-- Selected state indicator -->
          <svg class="h-5 w-5 text-[#6100FF] opacity-0 transition-opacity duration-200" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.236 4.53L7.53 10.53a.75.75 0 00-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
          </svg>
          <!-- Focus ring -->
          <span class="pointer-events-none absolute -inset-px rounded-lg border-2 border-transparent" aria-hidden="true"></span>
        </label>

        <!-- Booked Calls Card -->
        <label class="relative flex cursor-pointer rounded-lg border border-stone-300 bg-white p-4 shadow-sm focus:outline-none hover:border-stone-400 transition-colors duration-200">
          <%= form.radio_button :outcome, "booked_calls",
              class: "sr-only",
              data: { action: "change->form-wizard#handleGoalChange" } %>
          <span class="flex flex-1">
            <span class="flex flex-col">
              <span class="block text-sm font-medium text-stone-900">Booked Calls</span>
              <span class="mt-1 flex items-center text-sm text-stone-500">
                Drive traffic to your calendar to schedule sales calls or consultations
              </span>
            </span>
          </span>
          <!-- Selected state indicator -->
          <svg class="h-5 w-5 text-[#6100FF] opacity-0 transition-opacity duration-200" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.236 4.53L7.53 10.53a.75.75 0 00-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
          </svg>
          <!-- Focus ring -->
          <span class="pointer-events-none absolute -inset-px rounded-lg border-2 border-transparent" aria-hidden="true"></span>
        </label>

        <!-- Email Subscribers Card -->
        <label class="relative flex cursor-pointer rounded-lg border border-stone-300 bg-white p-4 shadow-sm focus:outline-none hover:border-stone-400 transition-colors duration-200">
          <%= form.radio_button :outcome, "email_subscribers",
              class: "sr-only",
              data: { action: "change->form-wizard#handleGoalChange" } %>
          <span class="flex flex-1">
            <span class="flex flex-col">
              <span class="block text-sm font-medium text-stone-900">Email Subscribers</span>
              <span class="mt-1 flex items-center text-sm text-stone-500">
                Build your email list by converting social media followers into subscribers
              </span>
            </span>
          </span>
          <!-- Selected state indicator -->
          <svg class="h-5 w-5 text-[#6100FF] opacity-0 transition-opacity duration-200" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.236 4.53L7.53 10.53a.75.75 0 00-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
          </svg>
          <!-- Focus ring -->
          <span class="pointer-events-none absolute -inset-px rounded-lg border-2 border-transparent" aria-hidden="true"></span>
        </label>

        <!-- Authority/Brand Awareness Card -->
        <label class="relative flex cursor-pointer rounded-lg border border-stone-300 bg-white p-4 shadow-sm focus:outline-none hover:border-stone-400 transition-colors duration-200">
          <%= form.radio_button :outcome, "authority_brand_awareness",
              class: "sr-only",
              data: { action: "change->form-wizard#handleGoalChange" } %>
          <span class="flex flex-1">
            <span class="flex flex-col">
              <span class="block text-sm font-medium text-stone-900">Authority/Brand Awareness</span>
              <span class="mt-1 flex items-center text-sm text-stone-500">
                Establish thought leadership and increase brand recognition in your industry
              </span>
            </span>
          </span>
          <!-- Selected state indicator -->
          <svg class="h-5 w-5 text-[#6100FF] opacity-0 transition-opacity duration-200" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.236 4.53L7.53 10.53a.75.75 0 00-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
          </svg>
          <!-- Focus ring -->
          <span class="pointer-events-none absolute -inset-px rounded-lg border-2 border-transparent" aria-hidden="true"></span>
        </label>

        <!-- Media/Speaking Engagements Card -->
        <label class="relative flex cursor-pointer rounded-lg border border-stone-300 bg-white p-4 shadow-sm focus:outline-none hover:border-stone-400 transition-colors duration-200">
          <%= form.radio_button :outcome, "media_speaking_engagements",
              class: "sr-only",
              data: { action: "change->form-wizard#handleGoalChange" } %>
          <span class="flex flex-1">
            <span class="flex flex-col">
              <span class="block text-sm font-medium text-stone-900">Media/Speaking Engagements</span>
              <span class="mt-1 flex items-center text-sm text-stone-500">
                Position yourself for podcast interviews, speaking opportunities, and media features
              </span>
            </span>
          </span>
          <!-- Selected state indicator -->
          <svg class="h-5 w-5 text-[#6100FF] opacity-0 transition-opacity duration-200" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.236 4.53L7.53 10.53a.75.75 0 00-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
          </svg>
          <!-- Focus ring -->
          <span class="pointer-events-none absolute -inset-px rounded-lg border-2 border-transparent" aria-hidden="true"></span>
        </label>
      </div>

      <span class="error-message hidden text-red-500 text-sm mt-2">Please select a goal</span>
    </div>

    <!-- Conditional fields for leads/booked calls -->
    <div id="social-media-goal-details" class="mb-6 hidden">
      <div class="p-4 mb-6 border border-amber-200 rounded-md bg-amber-50">
        <p class="mb-2 text-sm font-medium text-amber-800">Important:</p>
        <p class="text-sm text-amber-700">
          To generate leads from social media, it's important to set realistic expectations:
        </p>
        <p class="text-sm text-amber-700 mt-2">
          Ghostwriting helps build your authority and get attention from your target audience.
        </p>
        <p class="text-sm text-amber-700 mt-2">
          But ghostwriting is one piece of lead generation.
        </p>
        <p class="text-sm text-amber-700 mt-2">
          It's also crucial to have:
        </p>
        <ul class="text-sm text-amber-700 mt-2 ml-4 list-disc">
          <li>Social proof and case studies.</li>
          <li>A validated offer with product-market fit.</li>
          <li>A system to convert online traffic into calls/sales (like an appointment setter or funnel).</li>
        </ul>
        <p class="text-sm text-amber-700 mt-2">
          If you don't have one or more of the points above, that's okay.
        </p>
        <p class="text-sm text-amber-700 mt-2">
          Just know it will likely take longer to start generating leads.
        </p>
      </div>

      <!-- Hidden field to automatically set social_media_goal_type based on outcome selection -->
      <%= form.hidden_field :social_media_goal_type,
          data: { goal_dependent_required: true } %>

      <div class="mt-6">
        <label class="inline-flex items-start p-3 border border-stone-200 rounded-md hover:bg-stone-50 cursor-pointer">
          <%= form.check_box :social_media_understands_risk_acknowledged,
              class: "focus:ring-stone-500 h-4 w-4 text-stone-600 border-stone-300 mt-0.5",
              data: { goal_dependent_required: true } %>
          <span class="ml-3 text-sm text-stone-700">
            I understand the role of the ghostwriter.
          </span>
        </label>
        <span class="error-message hidden text-red-500 text-sm mt-2">Please acknowledge understanding the ghostwriter's role</span>
      </div>
    </div>

    <div class="step-error-message hidden mt-6 p-4 text-red-700 bg-red-50 border border-red-200 rounded-md text-sm">
      Please complete all required fields before continuing.
    </div>
  </div>

</div>


