<div data-controller="scout-sidebar" class="flex flex-col min-h-screen lg:flex-row">
  <!-- Sidebar -->
  <div class="flex flex-col w-1/5 lg:min-h-full">
    <div class="flex px-5 py-4">
      <span class="text-3xl font-semibold tracking-[-0.4px]">Manage Jobs</span>
      <span class="text-3xl font-semibold text-[#6100FF] tracking-[-0.4px]">.</span>
    </div>
    <div class="px-5 py-2">
      <ul class="text-xs list-disc list-inside text-stone-600">
        <li class="py-1">Create new job postings.</li>
        <li class="py-1">View the status of your jobs.</li>
        <li class="py-1">Manage your existing job postings.</li>
        <li class="py-1">View the number of applicants per job.</li>
      </ul>
    </div>
  </div>

  <!-- Main Content - Removed overflow-hidden to allow natural scrolling -->
    <div class="flex w-4/5 p-4 m-2 bg-white border rounded-md">
    <main data-sidebar-target="main" class="w-full p-4">
 
      <!-- Segmented Control for Job Status -->
      <div class="flex items-center justify-between mb-6">
        <div class="inline-flex rounded-md shadow-sm" role="group">
          <%= link_to scout_jobs_path, class: "px-4 py-2 text-sm font-medium #{params[:status].blank? ? 'text-white bg-stone-900' : 'text-stone-700 bg-white hover:bg-stone-50'} border border-stone-200 rounded-l-lg focus:z-10 focus:ring-2 focus:ring-[#6100FF] focus:text-[#6100FF]" do %>
            All (<%= @total_jobs_count || 0 %>)
          <% end %>
          <%= link_to scout_jobs_path(status: "draft"), class: "px-4 py-2 text-sm font-medium #{params[:status] == 'draft' ? 'text-white bg-stone-900' : 'text-stone-700 bg-white hover:bg-stone-50'} border-t border-b border-r border-stone-200 focus:z-10 focus:ring-2 focus:ring-[#6100FF] focus:text-[#6100FF]" do %>
            Draft (<%= @draft_jobs_count || 0 %>)
          <% end %>

          <%= link_to scout_jobs_path(status: "published"), class: "px-4 py-2 text-sm font-medium #{params[:status] == 'published' ? 'text-white bg-stone-900' : 'text-stone-700 bg-white hover:bg-stone-50'} border-t border-b border-r border-stone-200 focus:z-10 focus:ring-2 focus:ring-[#6100FF] focus:text-[#6100FF]" do %>
            Published (<%= @published_jobs_count || 0 %>)
          <% end %>
          <%= link_to scout_jobs_path(status: "expired"), class: "px-4 py-2 text-sm font-medium #{params[:status] == 'expired' ? 'text-white bg-stone-900' : 'text-stone-700 bg-white hover:bg-stone-50'} border-t border-b border-r border-stone-200 rounded-r-lg focus:z-10 focus:ring-2 focus:ring-[#6100FF] focus:text-[#6100FF]" do %>
            Expired (<%= @expired_jobs_count || 0 %>)
          <% end %>
        </div>
        
        <div>
          <%= link_to "Post New Job", new_scout_job_path, class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-stone-900 hover:bg-stone-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500" %>
        </div>
      </div>
      

            <div class="">

              <div class="mt-4">
                <% if @jobs.present? %>
                  <div class="shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                    <table class="min-w-full divide-y divide-stone-300">
                      <thead class="bg-stone-50">
                        <tr>
                          <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-stone-900 sm:pl-6">Job Title</th>
                          <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-stone-900">Status</th>
                          <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-stone-900">Applicants</th>
                          <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-stone-900">Posted Date</th>
                          <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                            <span class="sr-only">Actions</span>
                          </th>
                        </tr>
                      </thead>
                      <tbody class="bg-white divide-y divide-stone-200">
                        <% @jobs.each do |job| %>
                          <tr>
                            <td class="py-4 pl-4 pr-3 text-sm font-medium text-stone-900 whitespace-nowrap sm:pl-6">
                              <%= link_to job.title, scout_job_path(job), class: "underline text-stone-600 hover:text-purple-800" %>
                            </td>
                            <td class="px-3 py-4 text-sm whitespace-nowrap text-stone-500">
                              <span class="inline-flex border items-center px-2.5 py-0.5 rounded-md text-sm font-medium <%= job.status == 'published' ? 'bg-green-100 text-green-800 border-green-800' : (job.status == 'draft' ? 'bg-yellow-100 text-yellow-800 border-yellow-800' : 'bg-red-100 text-red-800 border-red-800') %>">
                                <%= job.status.capitalize %>
                              </span>
                            </td>
                            <td class="px-3 py-4 text-sm text-stone-500 whitespace-nowrap">

                                <% if job.applicants_count.to_i > 0 %>
                                  <%= link_to scout_job_applicants_path(job), class: ' shadow inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium border border-stone-900 text-stone-900' do %>
                                  <%= job.applicants_count %> Applicant<%= job.applicants_count != 1 ? 's' : '' %>
                                  <% end %>
                                <% else %>
                                <span class="text-xs text-stone-600">
                                  No Applicants
                                </span>
                                <% end %>
                            </td>
                            <td class="px-3 py-4 text-xs whitespace-nowrap text-stone-600">
                              <%= time_ago_in_words(job.created_at) %> ago
                            </td>
                            </td>
                          </tr>
                        <% end %>
                      </tbody>
                    </table>
                  </div>
                <% else %>
                  <div class="py-12 text-center">
                    <svg class="w-12 h-12 mx-auto text-stone-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-stone-900">No jobs</h3>
                    <p class="mt-1 text-sm text-stone-500">Get started by creating a new job posting.</p>
                    <div class="mt-6">
                      <%= link_to "Post New Job", new_scout_job_path, class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-stone-900 hover:bg-stone-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500" %>
                    </div>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
            </main>
          </div>
        </div>
      </div>
