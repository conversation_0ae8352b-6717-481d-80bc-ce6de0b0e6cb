<div class="sm:hidden">
  <label for="current-tab" class="sr-only">Select a tab</label>
  <select id="current-tab" name="current-tab" class="block w-full py-2 pl-3 pr-10 text-base rounded-md border-stone-300 focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm">
    <option <%= 'selected' if current_page?(scout_settings_path) %>>General</option>
    <option <%= 'selected' if current_page?(scout_settings_account_path) %>>Account</option>
    <option <%= 'selected' if current_page?(scout_settings_subscription_path) %>>Billing</option>
    <option <%= 'selected' if current_page?(scout_settings_organization_path) %>>Organization</option>
  </select>
</div>
<!-- Tabs at small breakpoint and up -->
<div class="hidden sm:block">
  <nav class="flex mt-6 -mb-px space-x-8">
    <!-- Current: "border-stone-900 text-stone-900", Default: "border-transparent text-stone-500 hover:border-stone-300 hover:text-stone-700" -->

    <%= link_to "General", scout_settings_path,
                class: " #{current_page?(scout_settings_path) ? 'px-1 pb-4 text-sm font-medium text-stone-900 border-b-2 border-stone-900 whitespace-nowrap' : 'px-1 pb-4 text-sm font-medium text-stone-500 border-b-2 border-transparent whitespace-nowrap hover:border-stone-300 hover:text-stone-700'}" %>
    <%= link_to "Account", scout_settings_account_path,
                class: " #{current_page?(scout_settings_account_path) ? 'px-1 pb-4 text-sm font-medium text-stone-900 border-b-2 border-stone-900 whitespace-nowrap' : 'px-1 pb-4 text-sm font-medium text-stone-500 border-b-2 border-transparent whitespace-nowrap hover:border-stone-300 hover:text-stone-700'}" %>
    <%= link_to "Billing", scout_settings_subscription_path,
                class: " #{current_page?(scout_settings_subscription_path) ? 'px-1 pb-4 text-sm font-medium text-stone-900 border-b-2 border-stone-900 whitespace-nowrap' : 'px-1 pb-4 text-sm font-medium text-stone-500 border-b-2 border-transparent whitespace-nowrap hover:border-stone-300 hover:text-stone-700'}" %>
    <%= link_to "Organization", scout_settings_organization_path,
                class: " #{current_page?(scout_settings_organization_path) ? 'px-1 pb-4 text-sm font-medium text-stone-900 border-b-2 border-stone-900 whitespace-nowrap' : 'px-1 pb-4 text-sm font-medium text-stone-500 border-b-2 border-transparent whitespace-nowrap hover:border-stone-300 hover:text-stone-700'}" %>

  </nav>
</div>
