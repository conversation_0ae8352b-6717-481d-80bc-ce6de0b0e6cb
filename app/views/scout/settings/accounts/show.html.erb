<%# Container div with styling following talent settings pattern %>
<div class="h-full px-16 py-8 bg-white border rounded shadow-sm border-stone-200">
  <%= render "scout/settings/shell" %>

  <%# Account Management Section %>
  <div class="max-w-2xl pt-6">
    <div class="pb-12 border-b border-stone-900/10">
      <h2 class="text-base font-semibold leading-7 text-stone-900">Account Management</h2>
      <p class="mt-1 text-sm leading-6 text-stone-600">Manage your account security and access settings.</p>

      <div class="grid grid-cols-1 mt-10 gap-x-6 gap-y-8 sm:grid-cols-6">
        <%# Password Change %>
        <div class="sm:col-span-6">
          <div class="flex items-center justify-between p-4 border border-stone-200 rounded-lg">
            <div>
              <h3 class="text-sm font-medium text-stone-900">Password</h3>
              <p class="text-sm text-stone-600">Change your account password</p>
            </div>
            <%= link_to "Change Password", edit_password_path, 
                class: "rounded-md bg-white px-3 py-2 text-sm font-semibold text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 hover:bg-stone-50" %>
          </div>
        </div>

        <%# Email Change %>
        <div class="sm:col-span-6">
          <div class="flex items-center justify-between p-4 border border-stone-200 rounded-lg">
            <div>
              <h3 class="text-sm font-medium text-stone-900">Email Address</h3>
              <p class="text-sm text-stone-600">Current: <%= @user.email %></p>
            </div>
            <%= link_to "Change Email", edit_identity_email_path, 
                class: "rounded-md bg-white px-3 py-2 text-sm font-semibold text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 hover:bg-stone-50" %>
          </div>
        </div>

        <%# Session Management %>
        <div class="sm:col-span-6">
          <div class="flex items-center justify-between p-4 border border-stone-200 rounded-lg">
            <div>
              <h3 class="text-sm font-medium text-stone-900">Active Sessions</h3>
              <p class="text-sm text-stone-600">Manage your devices and active sessions</p>
            </div>
            <%= link_to "Manage Sessions", sessions_path, 
                class: "rounded-md bg-white px-3 py-2 text-sm font-semibold text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 hover:bg-stone-50" %>
          </div>
        </div>

        <%# Logout All Sessions %>
        <div class="sm:col-span-6">
          <div class="flex items-center justify-between p-4 border border-red-200 rounded-lg bg-red-50">
            <div>
              <h3 class="text-sm font-medium text-red-900">Security Action</h3>
              <p class="text-sm text-red-700">Sign out from all devices and sessions</p>
            </div>
            <%= button_to "Logout All Sessions", logout_all_scout_settings_path, method: :delete,
                class: "rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500",
                data: { confirm: "Are you sure? This will sign you out from all devices." } %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
