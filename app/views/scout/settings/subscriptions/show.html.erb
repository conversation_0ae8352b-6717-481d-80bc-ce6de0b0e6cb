<%# Container div with styling matching general settings %>
<div class="h-full px-16 py-8 bg-white border rounded shadow-sm border-stone-200"
     data-controller="subscription"
     data-subscription-loaded-value="<%= @subscription.present? || !@loading %>">
  
  <%# Page Header %>
  <div class="pb-6 border-b border-stone-900/10">
    <h1 class="text-2xl font-bold leading-7 text-stone-900">Billing & Subscription</h1>
    <p class="mt-1 text-sm leading-6 text-stone-600">
      Manage your organization's subscription and billing information.
    </p>
  </div>

  <%# Loading State %>
  <div data-subscription-target="loading" class="<%= 'hidden' unless @loading %>">
    <div class="flex items-center justify-center py-12">
      <div class="flex items-center space-x-3">
        <div class="w-5 h-5 border-2 border-stone-300 border-t-stone-600 rounded-full animate-spin"></div>
        <span class="text-sm text-stone-600">Loading subscription details...</span>
      </div>
    </div>
  </div>

  <div class="max-w-2xl pt-6">
    <%# Current Subscription Section %>
    <div data-subscription-target="content" class="<%= 'hidden' if @loading %>">
      <% if @subscription %>
        <div class="pb-12 border-b border-stone-900/10">
          <h2 class="text-base font-semibold leading-7 text-stone-900">Current Subscription</h2>
          <p class="mt-1 text-sm leading-6 text-stone-600">View and manage your active subscription details.</p>

          <%# Subscription Details Card %>
          <div class="p-6 mt-6 bg-white border rounded-lg shadow-sm border-stone-200">
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <%# Plan Name %>
              <div>
                <dt class="text-sm font-medium text-stone-600">Plan</dt>
                <dd class="mt-1 text-sm text-stone-900">
                  <%= @subscription_plan&.name || get_plan_data(@subscription.processor_plan)[:name] || 'Unknown Plan' %>
                </dd>
              </div>

              <%# Status %>
              <div>
                <dt class="text-sm font-medium text-stone-600">Status</dt>
                <dd class="mt-1">
                  <span class="<%= subscription_status_badge_classes(@subscription.status) %>">
                    <%= @subscription.status.humanize %>
                  </span>
                </dd>
              </div>

              <%# Billing Amount %>
              <div>
                <dt class="text-sm font-medium text-stone-600">Amount</dt>
                <dd class="mt-1 text-sm text-stone-900">
                  <%= plan_amount(@subscription.processor_plan) %>
                </dd>
              </div>

              <%# Billing Cycle %>
              <div>
                <dt class="text-sm font-medium text-stone-600">Billing Cycle</dt>
                <dd class="mt-1 text-sm text-stone-900">
                  <%= @subscription_plan&.billing_cycle_description || get_plan_data(@subscription.processor_plan)[:billing_cycle] || 'Monthly' %>
                </dd>
              </div>

              <%# Next Billing Date %>
              <div>
                <dt class="text-sm font-medium text-stone-600">Next Billing Date</dt>
                <dd class="mt-1 text-sm text-stone-900">
                  <%= @next_billing_date&.strftime('%B %d, %Y') || 'Not available' %>
                </dd>
              </div>

              <%# Subscription Start Date %>
              <div>
                <dt class="text-sm font-medium text-stone-600">Started</dt>
                <dd class="mt-1 text-sm text-stone-900">
                  <%= @subscription.created_at.strftime('%B %d, %Y') %>
                </dd>
              </div>
            </div>

            <%# Features List %>
            <% features = @subscription_plan&.features || get_plan_data(@subscription.processor_plan)[:features] %>
            <% if features.present? %>
              <div class="mt-6">
                <dt class="text-sm font-medium text-stone-600">Included Features</dt>
                <dd class="mt-2">
                  <ul class="space-y-1 text-sm text-stone-900">
                    <% features.each do |feature| %>
                      <li class="flex items-center">
                        <%= phosphor_icon "check", class: "h-4 w-4 text-stone-500 mr-2" %>
                        <%= feature %>
                      </li>
                    <% end %>
                  </ul>
                </dd>
              </div>
            <% end %>
          </div>

          <%# Action Buttons Section %>
          <div class="pt-6 mt-8 border-t border-stone-200">
            <% if @billing_portal_url %>
              <div class="flex flex-col gap-4 sm:flex-row sm:justify-between">
                <div class="flex-1">
                  <h4 class="text-sm font-medium text-stone-900">Subscription Management</h4>
                  <p class="mt-1 text-sm text-stone-600">
                    Manage your plan, payment methods, and view invoices via the Stripe Billing Portal.
                  </p>
                </div>

                <div class="flex justify-end gap-3 mt-3">
                  <%# Manage Subscription button %>
                  <%= link_to @billing_portal_url,
                              class: "inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-stone-900 border border-stone-900 rounded-md shadow-sm hover:bg-stone-700 focus:outline-none focus:ring-2 focus:ring-stone-500 focus:ring-offset-2",
                              data: { action: "click->subscription#handleBillingPortal" } do %>
                    <%= phosphor_icon "gear", class: "h-4 w-4 mr-2" %>
                    Manage Subscription
                  <% end %>
                </div>
              </div>
            <% else %>
              <%# Enhanced Error Display %>
              <div class="p-4 border border-yellow-200 rounded-md bg-yellow-50">
                <div class="flex">
                  <div class="flex-shrink-0">
                    <%= phosphor_icon "warning", class: "h-5 w-5 text-yellow-400" %>
                  </div>
                  <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-800">
                      Billing Portal Unavailable
                    </h3>
                    <div class="mt-2 text-sm text-yellow-700">
                      <p>
                        <%= flash[:alert] || "We're unable to load your billing portal at this time. Please try again later or contact support if the issue persists." %>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        </div>

      <% else %>
        <%# No Subscription Section %>
        <div class="text-center">
          <div class="mx-auto mb-4">
            <%= phosphor_icon "credit-card", class: "h-12 w-12 text-stone-400 mx-auto" %>
          </div>
          <h3 class="mt-2 text-sm font-semibold text-stone-900">No Active Subscription</h3>
          <p class="mt-1 text-sm text-stone-500">
            Get started with a subscription plan to unlock premium features for your organization.
          </p>

          <%# Plan Selection Cards %>
          <div class="mt-8">
            <% available_plans = available_scout_plans_for_new_subscriptions %>
            <% if available_plans.any? %>
              <div class="grid gap-6 <%= available_plans.size == 1 ? 'max-w-md mx-auto' : 'md:grid-cols-2 lg:grid-cols-3' %>">
                <% available_plans.each do |plan_id, plan_data| %>
                  <div class="relative p-6 transition-shadow bg-white border rounded-lg shadow-sm border-stone-200 hover:shadow-md">
                    <div class="flex items-center justify-between mb-4">
                      <h3 class="text-lg font-semibold text-stone-900"><%= plan_data[:name] %></h3>
                      <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-md bg-stone-50 text-stone-700 ring-1 ring-inset ring-stone-700/10">
                        <%= plan_data[:billing_cycle] %>
                      </span>
                    </div>

                    <div class="mb-4">
                      <div class="flex items-baseline">
                        <span class="text-2xl font-bold text-stone-900">
                          <%= plan_amount(plan_id) %>
                        </span>
                        <span class="ml-1 text-sm text-stone-500">
                          / <%= plan_data[:billing_cycle]&.downcase || 'month' %>
                        </span>
                      </div>
                    </div>

                    <p class="mb-6 text-sm text-stone-600">
                      <%= plan_data[:description] %>
                    </p>

                    <% if plan_data[:features].present? %>
                      <ul class="mb-6 space-y-2 text-sm text-stone-600">
                        <% plan_data[:features].each do |feature| %>
                          <li class="flex items-center">
                            <%= phosphor_icon "check", class: "h-4 w-4 text-stone-500 mr-2" %>
                            <%= feature %>
                          </li>
                        <% end %>
                      </ul>
                    <% end %>

                    <%= button_to scout_subscription_path,
                                  method: :post,
                                  params: { plan: plan_id },
                                  form: {
                                    data: {
                                      turbo: false
                                    }
                                  },
                                  class: "w-full inline-flex justify-center items-center px-4 py-2 text-sm font-medium text-white bg-stone-900 border border-transparent rounded-md shadow-sm hover:bg-stone-700 focus:outline-none focus:ring-2 focus:ring-stone-500 focus:ring-offset-2" do %>
                      Get Started with <%= plan_data[:name] %>
                    <% end %>
                  </div>
                <% end %>
              </div>
            <% else %>
              <%# Fallback if no plans available %>
              <div class="py-8 text-center">
                <p class="text-stone-600">No subscription plans are currently available for organizations. Please check back later.</p>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>
