<%# Flash Messages %>
<% if notice %>
  <div class="p-4 mb-6 border border-green-300 rounded-md bg-green-50">
    <div class="flex">
      <div class="ml-3">
        <p class="text-sm font-medium text-green-800"><%= notice %></p>
      </div>
    </div>
  </div>
<% end %>

<% if alert %>
  <div class="p-4 mb-6 border border-red-300 rounded-md bg-red-50">
    <div class="flex">
      <div class="ml-3">
        <p class="text-sm font-medium text-red-800"><%= alert %></p>
      </div>
    </div>
  </div>
<% end %>

<%# Shell provides the header/tabs, content styling is moved to individual views %>
<div class="max-w-2xl pb-5 mb-6 border-b border-stone-200 sm:pb-0">
  <div class="mb-2 text-2xl font-semibold text-stone-900">
    Settings
  </div>
  <div class="pb-2 mb-4 text-sm text-stone-600">
    Manage your organization settings and information.
  </div>
  <div class="mt-6 sm:mt-4">
    <%= render "scout/settings/tabbar" %>
  </div>
</div>
<%= yield %> <%# Content will be yielded directly into the main layout's <main> tag %>
