<%# Container div with styling following talent settings pattern %>
<div class="h-full px-16 py-8 bg-white border rounded shadow-sm border-stone-200">
  <%= render "scout/settings/shell" %>

  <%# Display user validation errors if any %>
  <% if @user&.errors&.any? %>
    <div class="p-4 mt-6 border border-red-300 rounded-md bg-red-50">
      <div class="flex">
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">
            <%= pluralize(@user.errors.count, "error") %> prohibited your profile from being saved:
          </h3>
          <div class="mt-2 text-sm text-red-700">
            <ul role="list" class="pl-5 space-y-1 list-disc">
              <% @user.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <%# Personal Profile Section %>
  <%= form_with model: @user, url: scout_settings_path, method: :patch, data: { turbo: false, controller: "settings-form" }, class: "max-w-2xl pt-6" do |user_form| %>
    <div class="pb-12 border-b border-stone-900/10">
      <h2 class="text-base font-semibold leading-7 text-stone-900">Personal Profile</h2>
      <p class="mt-1 text-sm leading-6 text-stone-600">Update your personal information and avatar.</p>

      <div class="grid grid-cols-1 mt-10 gap-x-6 gap-y-8 sm:grid-cols-6">
        <%# Avatar %>
        <div class="sm:col-span-4">
          <label for="avatar" class="block text-sm font-medium leading-6 text-stone-900">Avatar</label>
          <div class="flex items-center mt-2 gap-x-3">
            <% if @user.avatar.attached? %>
              <%= image_tag @user.avatar, class: "h-12 w-12 rounded-full object-cover", id: "avatar-preview" %>
            <% else %>
              <svg class="w-12 h-12 text-stone-300" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true" id="avatar-preview">
                <path fill-rule="evenodd" d="M18.685 19.097A9.723 9.723 0 0021.75 12c0-5.385-4.365-9.75-9.75-9.75S2.25 6.615 2.25 12a9.723 9.723 0 003.065 7.097A9.716 9.716 0 0012 21.75a9.716 9.716 0 006.685-2.653zm-12.54-1.285A7.486 7.486 0 0112 15a7.486 7.486 0 015.855 2.812A8.224 8.224 0 0112 20.25a8.224 8.224 0 01-5.855-2.438zM15.75 9a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0z" clip-rule="evenodd" />
              </svg>
            <% end %>
            <%= user_form.file_field :avatar, class: "sr-only", id: "avatar-upload", accept: "image/*" %>
            <label for="avatar-upload" class="rounded-md bg-white px-2.5 py-1.5 text-sm font-semibold text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 hover:bg-stone-50 cursor-pointer">
              Choose File
            </label>
          </div>
          <p class="mt-2 text-xs leading-5 text-stone-600">JPG, GIF or PNG. 1MB max.</p>
        </div>

        <%# First Name %>
        <div class="sm:col-span-3">
          <%= user_form.label :first_name, class: "block text-sm font-medium leading-6 text-stone-900" %>
          <div class="mt-2">
            <%= user_form.text_field :first_name,
                required: true,
                maxlength: 50,
                class: "block w-full rounded-md py-1.5 text-stone-900 shadow-sm ring-1 ring-inset #{@user.errors[:first_name].any? ? 'ring-red-300 border-red-300' : 'ring-stone-300'} placeholder:text-stone-400 sm:text-sm sm:leading-6" %>
            <% if @user.errors[:first_name].any? %>
              <p class="mt-1 text-sm text-red-600"><%= @user.errors[:first_name].first %></p>
            <% end %>
          </div>
        </div>

        <%# Last Name %>
        <div class="sm:col-span-3">
          <%= user_form.label :last_name, class: "block text-sm font-medium leading-6 text-stone-900" %>
          <div class="mt-2">
            <%= user_form.text_field :last_name,
                required: true,
                maxlength: 50,
                class: "block w-full rounded-md py-1.5 text-stone-900 shadow-sm ring-1 ring-inset #{@user.errors[:last_name].any? ? 'ring-red-300 border-red-300' : 'ring-stone-300'} placeholder:text-stone-400 sm:text-sm sm:leading-6" %>
            <% if @user.errors[:last_name].any? %>
              <p class="mt-1 text-sm text-red-600"><%= @user.errors[:last_name].first %></p>
            <% end %>
          </div>
        </div>
      </div>
    </div>

    <div class="flex items-center justify-end mt-6 gap-x-6">
      <%= user_form.submit "Update Profile",
          data: { "settings-form-target": "submit" },
          class: "rounded-md bg-stone-900 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-stone-700 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-stone-900" %>
    </div>
  <% end %>
</div>

<script>
  // Image preview functionality
  document.getElementById('avatar-upload').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = function(e) {
        const preview = document.getElementById('avatar-preview');
        preview.innerHTML = `<img src="${e.target.result}" class="h-12 w-12 rounded-full object-cover" />`;
      };
      reader.readAsDataURL(file);
    }
  });
</script>

<%# Custom CSS to override @tailwindcss/forms focus style with higher specificity %>
<style>
  .max-w-2xl input[type=text]:focus,
  .max-w-2xl input[type=email]:focus,
  .max-w-2xl input[type=file]:focus,
  .max-w-2xl select:focus {
    border-color: #1c1917 !important; /* stone-900 */
    box-shadow: 0 0 0 1px #1c1917 !important; /* stone-900 */
    outline: none !important;
  }
</style>
