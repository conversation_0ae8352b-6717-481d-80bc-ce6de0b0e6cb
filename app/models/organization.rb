# == Schema Information
#
# Table name: organizations
#
#  id                    :bigint           not null, primary key
#  address_line_1        :string
#  address_line_2        :string
#  bio                   :text
#  city                  :string
#  country               :string
#  email                 :string
#  jobs_count            :integer          default(0), not null
#  name                  :string           not null
#  operating_timezone    :string
#  postal_code           :string
#  show_email_on_profile :boolean          default(FALSE)
#  size                  :string
#  state_province        :string
#  created_at            :datetime         not null
#  updated_at            :datetime         not null
#
# Indexes
#
#  index_organizations_on_created_at  (created_at)
#  index_organizations_on_name        (name)
#
class Organization < ApplicationRecord
  has_many :jobs, dependent: :destroy
  has_many :organization_memberships, dependent: :destroy
  has_many :users, through: :organization_memberships
  has_many :talent_notes, dependent: :destroy
  has_one_attached :logo

  # Validations for new settings fields
  validates :name, presence: true, length: { maximum: 255 }
  validates :email,
            format: {
              with: URI::MailTo::EMAIL_REGEXP,
            },
            allow_blank: true
  validates :bio, length: { maximum: 1000 }, allow_blank: true
  validates :address_line_1, length: { maximum: 255 }, allow_blank: true
  validates :address_line_2, length: { maximum: 255 }, allow_blank: true
  validates :city, length: { maximum: 100 }, allow_blank: true
  validates :state_province, length: { maximum: 100 }, allow_blank: true
  validates :postal_code, length: { maximum: 20 }, allow_blank: true
  validates :country, length: { maximum: 100 }, allow_blank: true

  # Helper methods for settings functionality
  def full_address
    address_parts =
      [
        address_line_1,
        address_line_2,
        city,
        state_province,
        postal_code,
        country,
      ].compact.reject(&:blank?)
    address_parts.join(', ')
  end

  def has_complete_address?
    address_line_1.present? && city.present? && country.present?
  end

  def display_email
    show_email_on_profile? ? email : nil
  end
end
