# frozen_string_literal: true

module Pay
  module Webhooks
    module Stripe
      # Handles the product.updated event from Stripe.
      # This webhook is triggered when a product is updated in Stripe dashboard.
      # Enhanced to sync changes to SubscriptionPlan model using Stripe-first architecture.
      class ProductUpdated
        def call(event)
          product_object = event.data.object
          product_id = product_object.id

          Rails.logger.info "Pay::Webhooks::Stripe::ProductUpdated: Handling event for Product #{product_id}"

          # Check if this is a product we care about (our subscription products)
          if subscription_product?(product_id)
            Rails.logger.info "Pay::Webhooks::Stripe::ProductUpdated: Processing product update for subscription product #{product_id}"

            # Sync changes to SubscriptionPlan model using enhanced service
            sync_result = SubscriptionPlanSyncService.sync_from_webhook({
              'type' => 'product.updated',
              'data' => { 'object' => product_object.to_hash }
            })

            if sync_result[:success]
              Rails.logger.info "Pay::Webhooks::Stripe::ProductUpdated: Successfully synced #{sync_result[:updated_plans_count]} plans for product #{product_id}"
            else
              Rails.logger.error "Pay::Webhooks::Stripe::ProductUpdated: Failed to sync plans for product #{product_id}: #{sync_result[:errors]&.join(', ')}"
            end

            # Also invalidate cache for all related prices (backward compatibility)
            invalidate_subscription_price_cache

            Rails.logger.info "Pay::Webhooks::Stripe::ProductUpdated: Successfully processed product update for #{product_id}"
          else
            Rails.logger.debug "Pay::Webhooks::Stripe::ProductUpdated: Ignoring product update for non-subscription product #{product_id}"
          end

        rescue StandardError => e
          Rails.logger.error "Pay::Webhooks::Stripe::ProductUpdated Error: #{e.message}\n#{e.backtrace.join("\n")}"
          # Don't re-raise to avoid webhook retry loops
        end

        private

        # Check if the product ID belongs to our subscription products
        # Enhanced to use dynamic detection from SubscriptionPlan model
        def subscription_product?(product_id)
          # First check if we have any plans using this product in our SubscriptionPlan database
          return true if SubscriptionPlan.exists?(stripe_product_id: product_id)

          # Fallback: For now, we'll be conservative and process all product updates
          # since product updates are less frequent and we want to catch any new products
          # that might be added to our subscription system
          true
        end

        # Invalidate cache for all subscription plan prices
        # Enhanced to use dynamic detection from SubscriptionPlan model
        def invalidate_subscription_price_cache
          # Get all subscription plan price IDs from database
          subscription_price_ids = SubscriptionPlan.where.not(stripe_price_id: nil).pluck(:stripe_price_id)

          # Fallback to hardcoded list if database is empty
          if subscription_price_ids.empty?
            subscription_price_ids = [
              'price_1R9Q55DYYVPVcCCrWQOwsKmT', # Standard plan
              'price_1R9Q66DYYVPVcCCrnqiXNafF', # Premium plan (legacy)
              'price_1RhubBDYYVPVcCCrqSs7wmUF'  # Premium plan (legacy alternative)
            ]
          end

          subscription_price_ids.each do |price_id|
            StripePricingService.invalidate_price_cache(price_id)
          end

          Rails.logger.info "Pay::Webhooks::Stripe::ProductUpdated: Invalidated cache for #{subscription_price_ids.count} subscription prices"
        end
      end
    end
  end
end
