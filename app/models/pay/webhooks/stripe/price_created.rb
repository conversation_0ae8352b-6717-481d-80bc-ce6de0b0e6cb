# frozen_string_literal: true

module Pay
  module Webhooks
    module Stripe
      # Handles the price.created event from Stripe.
      # This webhook is triggered when a new price is created in Stripe dashboard.
      # Automatically syncs new subscription plans to local database.
      class PriceCreated
        def call(event)
          price_object = event.data.object
          price_id = price_object.id

          Rails.logger.info "Pay::Webhooks::Stripe::PriceCreated: Handling event for new Price #{price_id}"

          # Only process recurring prices (subscription plans)
          unless price_object.type == 'recurring'
            Rails.logger.debug "Pay::Webhooks::Stripe::PriceCreated: Ignoring non-recurring price #{price_id}"
            return
          end

          # Check if this price already exists in our database
          if SubscriptionPlan.exists?(stripe_price_id: price_id)
            Rails.logger.info "Pay::Webhooks::Stripe::PriceCreated: Price #{price_id} already exists in database, skipping"
            return
          end

          Rails.logger.info "Pay::Webhooks::Stripe::PriceCreated: Creating new subscription plan for price #{price_id}"
          
          # Sync the new price to SubscriptionPlan model
          sync_result = SubscriptionPlanSyncService.sync_from_webhook({
            'type' => 'price.created',
            'data' => { 'object' => price_object.to_hash }
          })
          
          if sync_result[:success]
            Rails.logger.info "Pay::Webhooks::Stripe::PriceCreated: Successfully created plan #{price_id} in database (action: #{sync_result[:action]})"
          else
            Rails.logger.error "Pay::Webhooks::Stripe::PriceCreated: Failed to create plan #{price_id}: #{sync_result[:errors]&.join(', ')}"
          end

        rescue StandardError => e
          Rails.logger.error "Pay::Webhooks::Stripe::PriceCreated Error: #{e.message}\n#{e.backtrace.join("\n")}"
          # Don't re-raise to avoid webhook retry loops
        end
      end
    end
  end
end
