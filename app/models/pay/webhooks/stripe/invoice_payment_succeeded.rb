# frozen_string_literal: true

module Pay
  module Webhooks
    module Stripe
      # Handles the invoice.payment_succeeded event from Stripe.
      class InvoicePaymentSucceeded
        def call(event)
          # The Pay gem might have already processed the charge/subscription update.
          # This handler is for custom application logic *after* that.

          pay_charge = Pay::Charge.find_by(processor: :stripe, processor_id: event.data.object.charge)
          pay_subscription = pay_charge&.subscription
          user = pay_charge&.customer&.owner # Assuming owner is the User model

          Rails.logger.info "Pay::Webhooks::Stripe::InvoicePaymentSucceeded: Handling event for Charge #{pay_charge&.id}, Subscription #{pay_subscription&.id}, User #{user&.id}"

          # Grant premium access if the subscription paid for is the premium one
          if user && pay_subscription && pay_subscription.processor_plan.present?
            # Use database-driven approach to check if plan is premium
            is_premium_plan = false
            plan = SubscriptionPlan.find_by(stripe_price_id: pay_subscription.processor_plan)
            if plan
              # Use database plan information
              is_premium_plan = plan.premium?
            else
              # Fallback to hardcoded check for backward compatibility
              is_premium_plan = (pay_subscription.processor_plan == 'price_1R9Q66DYYVPVcCCrnqiXNafF') # Talent Premium Subscription Price ID (legacy)
            end

            if is_premium_plan
              Rails.logger.info "Pay::Webhooks::Stripe::InvoicePaymentSucceeded: Granting premium access to User ##{user.id} for premium plan #{pay_subscription.processor_plan}"
              user.talent_profile&.update(is_premium: true)
            else
              Rails.logger.info "Pay::Webhooks::Stripe::InvoicePaymentSucceeded: Invoice paid for non-premium plan #{pay_subscription.processor_plan} by User ##{user.id}"
              # Optional: Ensure premium is false if they somehow paid for a non-premium plan while premium
              # This could happen if they downgraded from premium to standard
              if user.talent_profile&.is_premium?
                Rails.logger.info "Pay::Webhooks::Stripe::InvoicePaymentSucceeded: Removing premium access for User ##{user.id} due to non-premium plan payment"
                user.talent_profile&.update(is_premium: false)
              end
            end
          else
            Rails.logger.warn "Pay::Webhooks::Stripe::InvoicePaymentSucceeded: Could not find User or Pay::Subscription for event."
          end

        rescue StandardError => e
          Rails.logger.error "Pay::Webhooks::Stripe::InvoicePaymentSucceeded Error: #{e.message}\n#{e.backtrace.join("\n")}"
          # Optionally notify an error service
        end
      end
    end
  end
end
