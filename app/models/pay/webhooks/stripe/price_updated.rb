# frozen_string_literal: true

module Pay
  module Webhooks
    module Stripe
      # Handles the price.updated event from Stripe.
      # This webhook is triggered when a price is updated in Stripe dashboard.
      # Enhanced to sync changes to SubscriptionPlan model using Stripe-first architecture.
      class PriceUpdated
        def call(event)
          price_object = event.data.object
          price_id = price_object.id

          Rails.logger.info "Pay::Webhooks::Stripe::PriceUpdated: Handling event for Price #{price_id}"

          # Check if this is a price we care about (our subscription plans)
          if subscription_plan_price?(price_id)
            Rails.logger.info "Pay::Webhooks::Stripe::PriceUpdated: Processing subscription plan price update for #{price_id}"

            # Sync changes to SubscriptionPlan model using enhanced service
            sync_result = SubscriptionPlanSyncService.sync_from_webhook({
              'type' => 'price.updated',
              'data' => { 'object' => price_object.to_hash }
            })

            if sync_result[:success]
              Rails.logger.info "Pay::Webhooks::Stripe::PriceUpdated: Successfully synced plan #{price_id} to database (action: #{sync_result[:action]})"
            else
              Rails.logger.error "Pay::Webhooks::Stripe::PriceUpdated: Failed to sync plan #{price_id}: #{sync_result[:errors]&.join(', ')}"
            end

            # Also refresh the cache with updated pricing data (backward compatibility)
            StripePricingService.refresh_price_cache(price_id)

            Rails.logger.info "Pay::Webhooks::Stripe::PriceUpdated: Successfully processed price update for #{price_id}"
          else
            Rails.logger.debug "Pay::Webhooks::Stripe::PriceUpdated: Ignoring price update for non-subscription price #{price_id}"
          end

        rescue StandardError => e
          Rails.logger.error "Pay::Webhooks::Stripe::PriceUpdated Error: #{e.message}\n#{e.backtrace.join("\n")}"
          # Don't re-raise to avoid webhook retry loops
        end

        private

        # Check if the price ID belongs to our subscription plans
        # Enhanced to use dynamic detection from SubscriptionPlan model
        def subscription_plan_price?(price_id)
          # First check if we have this price in our SubscriptionPlan database
          return true if SubscriptionPlan.exists?(stripe_price_id: price_id)

          # Fallback to hardcoded list for backward compatibility
          # These are the known subscription plan price IDs
          known_subscription_prices = [
            'price_1R9Q55DYYVPVcCCrWQOwsKmT', # Standard plan
            'price_1R9Q66DYYVPVcCCrnqiXNafF', # Premium plan (legacy)
            'price_1RhubBDYYVPVcCCrqSs7wmUF'  # Premium plan (legacy alternative)
          ]

          known_subscription_prices.include?(price_id)
        end
      end
    end
  end
end
