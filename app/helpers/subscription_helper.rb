# frozen_string_literal: true

module SubscriptionHelper
  # Cache expiration for plan data
  PLAN_CACHE_EXPIRATION = 1.hour

  # Legacy fallback mappings for backward compatibility
  # TODO: Remove after full migration to SubscriptionPlan model is complete
  LEGACY_PLAN_MAPPINGS = {
    'price_1R9Q55DYYVPVcCCrWQOwsKmT' => {
      name: 'Standard',
      description: 'Standard subscription plan with all essential features',
      billing_cycle: 'Annually',
      features: [
        'Access to all job listings',
        'Direct messaging with scouts',
        'Profile visibility',
        'Standard support'
      ]
    },
    # Legacy premium plan mapping - preserved for existing subscribers only
    'price_1R9Q66DYYVPVcCCrnqiXNafF' => {
      name: 'Premium',
      description: 'Premium subscription plan with advanced features',
      billing_cycle: 'Monthly',
      features: ['All Standard features', 'Premium support', 'Advanced analytics', 'Priority listing']
    }
  }.freeze

  # Get plan data from database with caching and fallback
  def get_plan_data(plan_id)
    return {} unless plan_id.present?

    # Try cache first
    cache_key = "subscription_plan_data_#{plan_id}"
    cached_data = Rails.cache.read(cache_key)
    return cached_data if cached_data

    # Try database lookup
    plan = SubscriptionPlan.find_by(stripe_price_id: plan_id)
    if plan
      plan_data = {
        name: plan.name,
        description: plan.description,
        billing_cycle: plan.billing_cycle_description,
        features: plan.features,
        amount: plan.amount,
        currency: plan.currency,
        active: plan.active?,
        legacy: plan.legacy?
      }

      # Cache the result
      Rails.cache.write(cache_key, plan_data, expires_in: PLAN_CACHE_EXPIRATION)
      return plan_data
    end

    # Fallback to legacy mappings
    legacy_data = LEGACY_PLAN_MAPPINGS[plan_id]
    if legacy_data
      # Cache legacy data too (but with shorter expiration)
      Rails.cache.write(cache_key, legacy_data, expires_in: 5.minutes)
      return legacy_data
    end

    # Return empty hash if nothing found
    {}
  end

  # Map Stripe plan ID to user-friendly name
  def plan_display_name(plan_id)
    get_plan_data(plan_id)[:name] || 'Unknown Plan'
  end

  # Get plan description
  def plan_description(plan_id)
    get_plan_data(plan_id)[:description] || 'Subscription plan'
  end

  # Get billing cycle information
  def plan_billing_cycle(plan_id)
    get_plan_data(plan_id)[:billing_cycle] || 'Monthly'
  end

  # Get plan features
  def plan_features(plan_id)
    get_plan_data(plan_id)[:features] || []
  end

  # Check if plan is premium
  def premium_plan?(plan_id)
    # First try database lookup
    plan = SubscriptionPlan.find_by(stripe_price_id: plan_id)
    return plan.premium? if plan

    # Fallback to hardcoded check
    plan_id == 'price_1R9Q66DYYVPVcCCrnqiXNafF'
  end

  # Check if plan is standard
  def standard_plan?(plan_id)
    # First try database lookup
    plan = SubscriptionPlan.find_by(stripe_price_id: plan_id)
    return plan.standard? if plan

    # Fallback to hardcoded check
    plan_id == 'price_1R9Q55DYYVPVcCCrWQOwsKmT'
  end

  # Check if plan is available for new subscriptions
  def plan_available_for_new_subscriptions?(plan_id)
    # First try database lookup
    plan = SubscriptionPlan.find_by(stripe_price_id: plan_id)
    return plan.active? && !plan.legacy? if plan

    # Fallback to legacy logic (only standard plan available)
    plan_id == 'price_1R9Q55DYYVPVcCCrWQOwsKmT'
  end

  # Get available plans for new subscriptions (talent-specific)
  def available_plans_for_new_subscriptions
    # Try database first - filter for talent plans
    active_plans = SubscriptionPlan.available_for_signup.available_for_talent.includes(:subscriptions)

    if active_plans.any?
      # Convert to hash format for backward compatibility
      plans_hash = {}
      active_plans.each do |plan|
        plans_hash[plan.stripe_price_id] = {
          name: plan.name,
          description: plan.description,
          billing_cycle: plan.billing_cycle_description,
          features: plan.features,
          amount: plan.amount,
          currency: plan.currency
        }
      end
      return plans_hash
    end

    # Fallback to legacy logic
    {
      'price_1R9Q55DYYVPVcCCrWQOwsKmT' => LEGACY_PLAN_MAPPINGS['price_1R9Q55DYYVPVcCCrWQOwsKmT']
    }
  end

  # Get available plans for new scout subscriptions
  def available_scout_plans_for_new_subscriptions
    # Try database first - filter for scout plans
    active_plans = SubscriptionPlan.where(active: true, available_for_scout: true).includes(:subscriptions)

    if active_plans.any?
      # Convert to hash format for backward compatibility
      plans_hash = {}
      active_plans.each do |plan|
        plans_hash[plan.stripe_price_id] = {
          name: plan.name,
          description: plan.description,
          billing_cycle: plan.billing_cycle_description,
          features: plan.features,
          amount: plan.amount,
          currency: plan.currency
        }
      end
      return plans_hash
    end

    # Fallback to empty hash if no scout plans available
    {}
  end

  # Get subscription status badge classes
  def subscription_status_badge_classes(status)
    case status.to_s.downcase
    when "active"
      # Most prominent - darker stone background with white text for active status
      "inline-flex items-center rounded-md bg-stone-800 px-2 py-1 text-xs font-medium text-white ring-1 ring-inset ring-stone-700/20"
    when "trialing"
      # Secondary prominence - medium stone background for trial period
      "inline-flex items-center rounded-md bg-stone-200 px-2 py-1 text-xs font-medium text-stone-800 ring-1 ring-inset ring-stone-600/20"
    when "past_due"
      # Warning state - lighter stone with darker text for attention
      "inline-flex items-center rounded-md bg-stone-100 px-2 py-1 text-xs font-medium text-stone-700 ring-1 ring-inset ring-stone-600/20"
    when "canceled", "cancelled"
      # Muted state - very light stone for cancelled subscriptions
      "inline-flex items-center rounded-md bg-stone-50 px-2 py-1 text-xs font-medium text-stone-600 ring-1 ring-inset ring-stone-500/20"
    when "incomplete"
      # Attention needed - medium stone background
      "inline-flex items-center rounded-md bg-stone-100 px-2 py-1 text-xs font-medium text-stone-700 ring-1 ring-inset ring-stone-600/20"
    when "incomplete_expired"
      # Expired state - lightest stone background
      "inline-flex items-center rounded-md bg-stone-50 px-2 py-1 text-xs font-medium text-stone-600 ring-1 ring-inset ring-stone-500/20"
    when "unpaid"
      # Problem state - light stone with darker text for visibility
      "inline-flex items-center rounded-md bg-stone-100 px-2 py-1 text-xs font-medium text-stone-700 ring-1 ring-inset ring-stone-600/20"
    else
      # Default fallback - neutral stone styling
      "inline-flex items-center rounded-md bg-stone-50 px-2 py-1 text-xs font-medium text-stone-600 ring-1 ring-inset ring-stone-500/20"
    end
  end

  # Get subscription status display text
  def subscription_status_display(status)
    case status.to_s.downcase
    when "active"
      "Active"
    when "trialing"
      "Trial Period"
    when "past_due"
      "Past Due"
    when "canceled", "cancelled"
      "Cancelled"
    when "incomplete"
      "Incomplete"
    when "incomplete_expired"
      "Expired"
    when "unpaid"
      "Unpaid"
    else
      status.to_s.humanize
    end
  end

  # Format next billing date
  def format_billing_date(date)
    return 'Not available' unless date.present?
    
    if date.respond_to?(:strftime)
      date.strftime('%B %d, %Y')
    else
      date.to_s
    end
  end

  # Get subscription card classes for different plan types
  def subscription_card_classes(plan_id)
    base_classes = 'rounded-lg border p-6 shadow-sm'

    if premium_plan?(plan_id)
      "#{base_classes} border-purple-200 bg-gradient-to-br from-purple-50 to-indigo-50"
    elsif standard_plan?(plan_id)
      "#{base_classes} border-stone-200 bg-stone-50"
    else
      "#{base_classes} border-stone-200 bg-white"
    end
  end

  # Get dynamic pricing from database first, then Stripe (with fallback to static values)
  def plan_amount(plan_id)
    return 'Unknown Plan' unless plan_id.present?

    # First try database lookup
    plan_data = get_plan_data(plan_id)
    if plan_data[:amount] && plan_data[:currency]
      case plan_data[:currency].downcase
      when 'usd'
        return "$#{plan_data[:amount] / 100.0}"
      when 'eur'
        return "€#{plan_data[:amount] / 100.0}"
      when 'gbp'
        return "£#{plan_data[:amount] / 100.0}"
      else
        return "#{plan_data[:amount] / 100.0} #{plan_data[:currency].upcase}"
      end
    end

    # Fallback to StripePricingService for dynamic pricing
    begin
      StripePricingService.fetch_price_amount(plan_id)
    rescue StandardError => e
      Rails.logger.error "SubscriptionHelper: Error fetching price for #{plan_id}: #{e.message}"

      # Final fallback to static values
      case plan_id
      when 'price_1R9Q55DYYVPVcCCrWQOwsKmT'
        '$99.00' # Standard plan
      when 'price_1R9Q66DYYVPVcCCrnqiXNafF'
        '$99.00' # Premium plan (legacy)
      else
        'See billing portal'
      end
    end
  end

  # Get plan badge classes
  def plan_badge_classes(plan_id)
    if premium_plan?(plan_id)
      'inline-flex items-center rounded-md bg-purple-50 px-2 py-1 text-xs font-medium text-purple-700 ring-1 ring-inset ring-purple-700/10'
    elsif standard_plan?(plan_id)
      'inline-flex items-center rounded-md bg-stone-50 px-2 py-1 text-xs font-medium text-stone-700 ring-1 ring-inset ring-stone-700/10'
    else
      'inline-flex items-center rounded-md bg-stone-50 px-2 py-1 text-xs font-medium text-stone-700 ring-1 ring-inset ring-stone-700/10'
    end
  end

  # Cache invalidation method for plan data
  def invalidate_plan_cache(plan_id)
    Rails.cache.delete("subscription_plan_data_#{plan_id}")
  end

  # Invalidate all plan caches
  def invalidate_all_plan_caches
    # Get all known plan IDs from database and legacy mappings
    plan_ids = []

    # From database
    plan_ids += SubscriptionPlan.pluck(:stripe_price_id).compact

    # From legacy mappings
    plan_ids += LEGACY_PLAN_MAPPINGS.keys

    # Remove duplicates and invalidate
    plan_ids.uniq.each { |plan_id| invalidate_plan_cache(plan_id) }

    Rails.logger.info "SubscriptionHelper: Invalidated cache for #{plan_ids.count} plans"
  end

  # Get all plans (database + legacy) for admin purposes
  def all_known_plans
    plans = {}

    # Add database plans
    SubscriptionPlan.all.each do |plan|
      next unless plan.stripe_price_id.present?

      plans[plan.stripe_price_id] = {
        name: plan.name,
        description: plan.description,
        billing_cycle: plan.billing_cycle_description,
        features: plan.features,
        amount: plan.amount,
        currency: plan.currency,
        active: plan.active?,
        legacy: plan.legacy?,
        source: 'database'
      }
    end

    # Add legacy plans that aren't in database
    LEGACY_PLAN_MAPPINGS.each do |plan_id, plan_data|
      unless plans.key?(plan_id)
        plans[plan_id] = plan_data.merge(source: 'legacy')
      end
    end

    plans
  end
end
