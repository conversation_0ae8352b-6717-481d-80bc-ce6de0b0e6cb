class SeedSubscriptionPlans < ActiveRecord::Migration[8.0]
  def up
    # Seed existing hardcoded plans into the database
    # This migration uses the SubscriptionPlanMigrationService to ensure consistency

    puts "Seeding subscription plans from hardcoded data..."

    # Standard Plan (Currently active)
    standard_plan = SubscriptionPlan.find_or_create_by(stripe_price_id: 'price_1R9Q55DYYVPVcCCrWQOwsKmT') do |plan|
      plan.name = 'Standard'
      plan.description = 'Standard subscription plan with all essential features'
      plan.amount = 9900 # $99.00 in cents
      plan.currency = 'usd'
      plan.billing_interval = 'year'
      plan.billing_interval_count = 1
      plan.features = [
        'Access to all job listings',
        'Direct messaging with scouts',
        'Profile visibility',
        'Standard support'
      ]
      plan.metadata = {
        'plan_type' => 'standard',
        'migrated_from' => 'hardcoded_constants',
        'migration_date' => Time.current.iso8601
      }
      plan.active = true
      plan.legacy = false
      plan.last_synced_at = Time.current
    end

    puts "Created/Updated Standard plan: #{standard_plan.stripe_price_id}"

    # Premium Plan (Legacy - no longer available for new subscriptions)
    premium_plan = SubscriptionPlan.find_or_create_by(stripe_price_id: 'price_1R9Q66DYYVPVcCCrnqiXNafF') do |plan|
      plan.name = 'Premium'
      plan.description = 'Premium subscription plan with advanced features (Legacy)'
      plan.amount = 9900 # $99.00 in cents (same price as standard but monthly)
      plan.currency = 'usd'
      plan.billing_interval = 'month'
      plan.billing_interval_count = 1
      plan.features = [
        'All Standard features',
        'Premium support',
        'Advanced analytics',
        'Priority listing'
      ]
      plan.metadata = {
        'plan_type' => 'premium',
        'migrated_from' => 'hardcoded_constants',
        'migration_date' => Time.current.iso8601,
        'legacy_reason' => 'No longer available for new subscriptions'
      }
      plan.active = false # No longer available for new subscriptions
      plan.legacy = true
      plan.last_synced_at = Time.current
    end

    puts "Created/Updated Premium plan: #{premium_plan.stripe_price_id}"

    # Verify the seeding was successful
    total_plans = SubscriptionPlan.count
    active_plans = SubscriptionPlan.active.count
    legacy_plans = SubscriptionPlan.legacy.count

    puts "Seeding completed successfully!"
    puts "Total plans: #{total_plans}"
    puts "Active plans: #{active_plans}"
    puts "Legacy plans: #{legacy_plans}"

    # Optional: Sync with Stripe to ensure data consistency
    if Rails.env.production? || ENV['SYNC_WITH_STRIPE'] == 'true'
      puts "Syncing with Stripe API..."
      begin
        sync_service = SubscriptionPlanSyncService.new
        [standard_plan, premium_plan].each do |plan|
          sync_service.sync_plan_from_stripe(plan.stripe_price_id)
          puts "Synced plan #{plan.stripe_price_id} with Stripe"
        end
      rescue StandardError => e
        puts "Warning: Could not sync with Stripe: #{e.message}"
        puts "Plans have been seeded with hardcoded data. Manual sync may be required."
      end
    else
      puts "Skipping Stripe sync (not in production). Set SYNC_WITH_STRIPE=true to force sync."
    end
  end

  def down
    # Remove seeded plans
    puts "Removing seeded subscription plans..."

    plan_ids = [
      'price_1R9Q55DYYVPVcCCrWQOwsKmT', # Standard
      'price_1R9Q66DYYVPVcCCrnqiXNafF'  # Premium
    ]

    plan_ids.each do |price_id|
      plan = SubscriptionPlan.find_by(stripe_price_id: price_id)
      if plan
        plan.destroy!
        puts "Removed plan: #{price_id}"
      end
    end

    puts "Rollback completed."
  end
end
