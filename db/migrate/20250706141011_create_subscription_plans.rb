class CreateSubscriptionPlans < ActiveRecord::Migration[8.0]
  def change
    create_table :subscription_plans do |t|
      t.string :stripe_price_id, null: false
      t.string :stripe_product_id
      t.string :name, null: false
      t.text :description
      t.integer :amount, null: false # Amount in cents
      t.string :currency, null: false, default: 'usd'
      t.string :billing_interval, null: false # 'month', 'year', etc.
      t.integer :billing_interval_count, null: false, default: 1
      t.boolean :active, null: false, default: true
      t.boolean :legacy, null: false, default: false
      t.jsonb :features, null: false, default: []
      t.jsonb :metadata, null: false, default: {}
      t.datetime :stripe_created_at
      t.datetime :last_synced_at

      t.timestamps
    end

    # Indexes for performance and uniqueness
    add_index :subscription_plans, :stripe_price_id, unique: true
    add_index :subscription_plans, :active
    add_index :subscription_plans, :legacy
    add_index :subscription_plans, :billing_interval
    add_index :subscription_plans, %i[active legacy] # Composite index for common queries
  end
end
