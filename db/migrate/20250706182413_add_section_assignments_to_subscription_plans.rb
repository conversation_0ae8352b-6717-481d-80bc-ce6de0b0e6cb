class AddSectionAssignmentsToSubscriptionPlans < ActiveRecord::Migration[8.0]
  def change
    add_column :subscription_plans,
               :available_for_scout,
               :boolean,
               default: false,
               null: false
    add_column :subscription_plans,
               :available_for_talent,
               :boolean,
               default: false,
               null: false

    # Add indexes for filtering by section availability
    add_index :subscription_plans, :available_for_scout
    add_index :subscription_plans, :available_for_talent
    add_index :subscription_plans,
              %i[available_for_scout available_for_talent],
              name: 'index_subscription_plans_on_section_availability'
  end
end
