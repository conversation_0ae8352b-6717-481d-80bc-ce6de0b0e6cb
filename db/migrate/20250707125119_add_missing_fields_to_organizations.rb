class AddMissingFieldsToOrganizations < ActiveRecord::Migration[8.0]
  def change
    add_column :organizations, :bio, :text
    add_column :organizations, :email, :string
    add_column :organizations, :address_line_1, :string
    add_column :organizations, :address_line_2, :string
    add_column :organizations, :city, :string
    add_column :organizations, :state_province, :string
    add_column :organizations, :postal_code, :string
    add_column :organizations, :country, :string
    add_column :organizations, :show_email_on_profile, :boolean, default: false
  end
end
