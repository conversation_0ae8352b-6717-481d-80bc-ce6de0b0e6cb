class AllowNullPricingFieldsInSubscriptionPlans < ActiveRecord::Migration[8.0]
  def change
    # Allow NULL values for Stripe-managed pricing fields to support local-only plans
    change_column_null :subscription_plans, :stripe_price_id, true
    change_column_null :subscription_plans, :amount, true
    change_column_null :subscription_plans, :currency, true
    change_column_null :subscription_plans, :billing_interval, true
    change_column_null :subscription_plans, :billing_interval_count, true

    # Remove default values for pricing fields since they should come from Stripe
    change_column_default :subscription_plans, :currency, from: 'usd', to: nil
    change_column_default :subscription_plans,
                          :billing_interval_count,
                          from: 1,
                          to: nil

    # Remove the unique constraint on stripe_price_id and recreate it to allow multiple NULLs
    remove_index :subscription_plans, :stripe_price_id
    add_index :subscription_plans,
              :stripe_price_id,
              unique: true,
              where: 'stripe_price_id IS NOT NULL'
  end
end
