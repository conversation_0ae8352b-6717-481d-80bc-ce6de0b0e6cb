class UpdateScoutPlansAvailability < ActiveRecord::Migration[8.0]
  def up
    # Update Scout Job Listing plans to be available for scouts
    SubscriptionPlan.where(name: ['Scout Job Listing', 'Scout Premium Job Listing'])
                   .update_all(available_for_scout: true)
    
    puts "Updated Scout Job Listing plans to be available for scouts"
  end

  def down
    # Revert Scout Job Listing plans to not be available for scouts
    SubscriptionPlan.where(name: ['Scout Job Listing', 'Scout Premium Job Listing'])
                   .update_all(available_for_scout: false)
    
    puts "Reverted Scout Job Listing plans to not be available for scouts"
  end
end
