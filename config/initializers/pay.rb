# frozen_string_literal: true

Pay.setup do |config|
  # For use in the receipt/refund/renewal mailers
  config.business_name = 'Ghostwrote'
  config.business_address = 'Your Business Address' # Optional: Add your address
  config.application_name = 'Ghostwrote'
  config.support_email = '<EMAIL>'

  config.default_product_name = 'default'
  config.default_plan_name = 'default'

  config.automount_routes = true
  config.routes_path = '/pay' # Customizable path for Pay engine routes (webhooks, portal)

  # All processors are enabled by default. Comment out lines for processors you want to disable.
  config.enabled_processors = %i[stripe]

  # Set up mailers for receipts, refunds, renewals, etc.
  config.mailer = 'Pay::UserMailer'

  # Define the model that has the Pay::Billable concern by adding `pay_customer` to your model (e.g., User)

  # Customize the Pay::Customer table name if needed
  # config.customer_table = :pay_customers

  # Customize the Pay::Charge table name if needed
  # config.charge_table = :pay_charges

  # Customize the Pay::Subscription table name if needed
  # config.subscription_table = :pay_subscriptions

  # Configure Stripe
  # Keys (publishable_key, secret_key, signing_secret) are automatically read from:
  # Rails.application.credentials.stripe[:publishable_key]
  # Rails.application.credentials.stripe[:secret_key]
  # Rails.application.credentials.stripe[:signing_secret]
  # or ENV['STRIPE_PUBLISHABLE_KEY'], ENV['STRIPE_SECRET_KEY'], ENV['STRIPE_SIGNING_SECRET']

  # Configure Braintree (if used)
  # config.braintree.merchant_id = Rails.application.credentials.dig(:braintree, :merchant_id)
  # config.braintree.public_key = Rails.application.credentials.dig(:braintree, :public_key)
  # config.braintree.private_key = Rails.application.credentials.dig(:braintree, :private_key)

  # Configure Paddle Billing (if used)
  # config.paddle_billing.client_side_token = Rails.application.credentials.dig(:paddle_billing, :client_side_token)
  # config.paddle_billing.api_key = Rails.application.credentials.dig(:paddle_billing, :api_key)
  # config.paddle_billing.signing_secret = Rails.application.credentials.dig(:paddle_billing, :signing_secret)

  # Configure Paddle Classic (if used)
  # config.paddle_classic.vendor_id = Rails.application.credentials.dig(:paddle_classic, :vendor_id)
  # config.paddle_classic.vendor_auth_code = Rails.application.credentials.dig(:paddle_classic, :vendor_auth_code)
  # config.paddle_classic.public_key_base64 = Rails.application.credentials.dig(:paddle_classic, :public_key_base64)

  # Example webhook event handler (uncomment and define the class if needed for custom logic)
  # Pay gem v8+ typically handles webhook routing automatically when the engine is mounted.
  # Custom logic can be placed in classes like app/models/pay/webhooks/stripe/invoice_payment_succeeded.rb
  # or potentially by subscribing to ActiveSupport::Notifications like below.
  # The StripeWebhookHandler class created earlier (app/models/stripe_webhook_handler.rb)
  # might need to be adapted or used within specific event notification subscribers if Pay doesn't call it directly.

  # config.braintree.webhook_event_handler = ->(event) { BraintreeWebhookHandler.handle(event) }
  # config.paddle_billing.webhook_event_handler = ->(event) { PaddleBillingWebhookHandler.handle(event) }
  # config.paddle_classic.webhook_event_handler = ->(event) { PaddleClassicWebhookHandler.handle(event) }
end

# Explicitly subscribe webhook events to handlers if needed.
# Pay v8+ uses convention (e.g., Pay::Webhooks::Stripe::CheckoutSessionCompleted for stripe.checkout.session.completed),
# but we can explicitly map async payments to the same handler.
ActiveSupport.on_load(:pay) do
  # Ensure our custom handler is used for both completed and async success events for job payments.
  job_payment_handler = Pay::Webhooks::Stripe::CheckoutSessionCompleted.new

  # Pay's default handler for checkout.session.completed might already exist,
  # but explicitly subscribing ensures our logic runs.
  Pay::Webhooks.delegator.subscribe 'stripe.checkout.session.completed',
      job_payment_handler

  # Map the async success event to the same handler.
  Pay::Webhooks
    .delegator.subscribe 'stripe.checkout.session.async_payment_succeeded',
      job_payment_handler

  # Subscribe to pricing-related webhook events for subscription plan synchronization
  price_created_handler = Pay::Webhooks::Stripe::PriceCreated.new
  price_updated_handler = Pay::Webhooks::Stripe::PriceUpdated.new
  product_updated_handler = Pay::Webhooks::Stripe::ProductUpdated.new

  Pay::Webhooks.delegator.subscribe 'stripe.price.created',
      price_created_handler
  Pay::Webhooks.delegator.subscribe 'stripe.price.updated',
      price_updated_handler
  Pay::Webhooks.delegator.subscribe 'stripe.product.updated',
      product_updated_handler

  # You can add other explicit subscriptions here if needed for other events.
end

# Removed the potentially problematic ActiveSupport::Notifications.subscribe(/pay\./) block.
