source 'https://rubygems.org'
ruby '3.3.6'

# Core Rails
gem 'rails', '~> 8.0.2'
gem 'propshaft' # Modern asset pipeline
gem 'pg', '~> 1.1' # PostgreSQL adapter
gem 'puma', '>= 5.0' # Web server
gem 'bootsnap', require: false # Reduces boot times
gem 'tzinfo-data', platforms: %i[windows jruby] # Timezone data
gem 'sqlite3', '~> 2.0', '>= 2.0.2'
# Authentication & Security
gem 'authentication-zero', '~> 4.0'
gem 'bcrypt', '~> 3.1.7' # Password hashing
gem 'name_of_person' # Name handling

gem 'sentry-ruby'
gem 'sentry-rails'

# Background Processing & Caching
gem 'solid_cache' # Database-backed cache
gem 'solid_queue' # Database-backed queue
gem 'solid_cable' # Database-backed Action Cable

# Frontend & JavaScript
gem 'turbo-rails' # Hotwire's SPA-like page accelerator
gem 'stimulus-rails' # Hotwire's modest JavaScript framework
gem 'shakapacker', '8.2.0' # Webpack integration
gem 'react_on_rails', '14.2.0' # React integration
gem 'cssbundling-rails', '~> 1.4' # CSS bundling

# Search
gem 'searchkick', '~> 5.5' # Elasticsearch integration
gem 'elasticsearch', '~> 8.0' # Elasticsearch client
gem 'pagy', '~> 9.3' # Pagination

# Analytics and reporting
gem 'groupdate' # Date grouping for analytics

# API
gem 'jbuilder' # JSON API builder

# Deployment
gem 'kamal', require: false # Docker deployment
gem 'thruster', require: false # HTTP asset caching for Puma

# Payments
gem 'pay', '~> 10.1' # Check for the latest version
gem 'stripe', '~> 15'

group :development, :test do
  gem 'debug', platforms: %i[mri windows], require: 'debug/prelude' # Debugging
  gem 'brakeman', require: false # Security analysis
  gem 'faker' # Test data generation
  gem 'rubocop-rails-omakase', require: false # Ruby style checking
end

group :development do
  gem 'web-console' # Interactive console
  gem 'hotwire-spark' # Hotwire components
  gem 'annotaterb' # Model annotations
  gem 'htmlbeautifier' # HTML formatting
  gem 'erb_lint', require: false # ERB linting
end

group :test do
  gem 'capybara' # Integration testing
  gem 'selenium-webdriver' # Browser automation
  gem 'rails-controller-testing' # Controller testing helpers
  gem 'mocha' # Mocking and stubbing for tests
end

gem 'phosphor_icons', '~> 0.3.0'

gem 'image_processing', '~> 1.14'

# Scheduling
gem 'whenever', require: false

gem 'aws-sdk-s3', require: false

gem 'mission_control-jobs', '~> 1.0'
