# Rails Application Screenshot Documentation Plan

## Overview

This document provides a comprehensive plan for creating automated screenshot documentation of all pages in the Rails application using Playwright MCP. The system will capture screenshots organized by user type (Talent, Scout, SuperAdmin) for client content review.

## Prerequisites

### Dependencies

- Playwright MCP browser automation
- Rails application running on `localhost:5010`
- Seeded database with test users
- Node.js and npm/yarn for any additional tooling

### Required Test Users

Based on the application's authentication system, we need these test users:

```ruby
# Scout User
email: "<EMAIL>"
password: "Secret1*3*5*"
role: "scout"

# Talent User
email: "<EMAIL>"
password: "Secret1*3*5*"
role: "talent"

# SuperAdmin User
email: "<EMAIL>"
password: "Secret1*3*5*"
role: "superadmin"
```

## Implementation Tasks

### Task 1: Setup and Preparation

**Duration: 20 minutes**

1. **Create directory structure**

   ```bash
   mkdir -p screenshots/{talent,scout,superadmin}
   mkdir -p scripts/screenshot-automation
   ```

2. **Verify test users exist**

   ```bash
   bin/rails runner "
   users = ['<EMAIL>', '<EMAIL>', '<EMAIL>']
   users.each do |email|
     user = User.find_by(email: email)
     if user
       puts \"✓ #{email} exists with roles: #{user.roles.pluck(:name).join(', ')}\"
     else
       puts \"✗ #{email} not found\"
     end
   end
   "
   ```

3. **Start Rails server**
   ```bash
   bin/dev
   ```

### Task 2: Create Route Discovery Script

**Duration: 20 minutes**

Create `scripts/screenshot-automation/discover_routes.rb`:

```ruby
#!/usr/bin/env ruby

require_relative '../../config/environment'

class RouteDiscovery
  def self.generate_route_tree
    routes = Rails.application.routes.routes

    # Group routes by namespace/user type
    talent_routes = []
    scout_routes = []
    superadmin_routes = []
    public_routes = []

    routes.each do |route|
      next if route.path.spec.to_s.include?(':format')
      next if route.path.spec.to_s.include?('*')
      next if route.verb == 'OPTIONS'

      path = route.path.spec.to_s
      controller = route.defaults[:controller]
      action = route.defaults[:action]

      route_info = {
        path: path,
        controller: controller,
        action: action,
        verb: route.verb,
        name: route.name
      }

      case controller
      when /^talent\//
        talent_routes << route_info
      when /^scout\//
        scout_routes << route_info
      when /^super_admin\//
        superadmin_routes << route_info
      else
        # Public or shared routes
        public_routes << route_info unless controller.nil?
      end
    end

    {
      talent: talent_routes.sort_by { |r| r[:path] },
      scout: scout_routes.sort_by { |r| r[:path] },
      superadmin: superadmin_routes.sort_by { |r| r[:path] },
      public: public_routes.sort_by { |r| r[:path] }
    }
  end

  def self.save_routes_json
    routes = generate_route_tree
    File.write('scripts/screenshot-automation/routes.json', JSON.pretty_generate(routes))
    puts "Routes saved to scripts/screenshot-automation/routes.json"
  end
end

RouteDiscovery.save_routes_json if __FILE__ == $0
```

### Task 3: Create Authentication Helper Script

**Duration: 20 minutes**

Create `scripts/screenshot-automation/auth_helper.js`:

```javascript
// Authentication helper for Playwright MCP
class AuthHelper {
  constructor(page) {
    this.page = page;
    this.baseUrl = "http://localhost:5010";
  }

  async signIn(email, password) {
    console.log(`Signing in as ${email}`);

    // Navigate to sign in page
    await this.page.goto(`${this.baseUrl}/sign_in`);

    // Fill in credentials
    await this.page.fill('input[name="email"]', email);
    await this.page.fill('input[name="password"]', password);

    // Submit form
    await this.page.click('button[type="submit"], input[type="submit"]');

    // Wait for redirect after successful login
    await this.page.waitForURL(/\/launchpad|\/talent|\/scout|\/super_admin/, {
      timeout: 10000,
    });

    console.log(`Successfully signed in as ${email}`);
  }

  async signOut() {
    try {
      // Look for sign out link/button
      await this.page.click('a[href*="sign_out"], button:has-text("Sign Out")');
      await this.page.waitForURL(/\/sign_in/, { timeout: 5000 });
      console.log("Successfully signed out");
    } catch (error) {
      console.log("Sign out failed or already signed out:", error.message);
    }
  }

  async ensureSignedOut() {
    // Clear all cookies and session data
    await this.page.context().clearCookies();
    await this.page.goto(`${this.baseUrl}/sign_in`);
  }
}

module.exports = AuthHelper;
```

### Task 4: Create Main Screenshot Script

**Duration: 30 minutes**

Create `scripts/screenshot-automation/capture_screenshots.js`:

```javascript
const fs = require("fs");
const path = require("path");

class ScreenshotCapture {
  constructor() {
    this.baseUrl = "http://localhost:5010";
    this.routes = JSON.parse(
      fs.readFileSync("scripts/screenshot-automation/routes.json", "utf8")
    );
    this.users = {
      talent: { email: "<EMAIL>", password: "Secret1*3*5*" },
      scout: { email: "<EMAIL>", password: "Secret1*3*5*" },
      superadmin: { email: "<EMAIL>", password: "Secret1*3*5*" },
    };
  }

  sanitizeFilename(str) {
    return str.replace(/[^a-z0-9]/gi, "_").toLowerCase();
  }

  async captureUserTypeRoutes(userType, page, authHelper) {
    console.log(`\n=== Capturing ${userType.toUpperCase()} routes ===`);

    const user = this.users[userType];
    const routes = this.routes[userType] || [];

    // Sign in as the user type
    await authHelper.ensureSignedOut();
    await authHelper.signIn(user.email, user.password);

    const screenshots = [];

    for (const route of routes) {
      try {
        console.log(`Capturing: ${route.path}`);

        // Navigate to the route
        const url = `${this.baseUrl}${route.path}`;
        await page.goto(url, { waitUntil: "networkidle", timeout: 10000 });

        // Wait for page to be fully loaded
        await page.waitForTimeout(2000);

        // Generate filename
        const filename = `${this.sanitizeFilename(
          route.controller
        )}_${this.sanitizeFilename(route.action)}.png`;
        const filepath = `screenshots/${userType}/${filename}`;

        // Take screenshot
        await page.screenshot({
          path: filepath,
          fullPage: true,
          type: "png",
        });

        screenshots.push({
          route: route,
          filename: filename,
          filepath: filepath,
          url: url,
          status: "success",
        });

        console.log(`✓ Saved: ${filepath}`);
      } catch (error) {
        console.log(`✗ Failed to capture ${route.path}: ${error.message}`);
        screenshots.push({
          route: route,
          filename: null,
          filepath: null,
          url: `${this.baseUrl}${route.path}`,
          status: "failed",
          error: error.message,
        });
      }
    }

    return screenshots;
  }

  async captureAll() {
    const results = {};

    // Note: This would be called via Playwright MCP
    console.log("Screenshot capture would be initiated via Playwright MCP");
    console.log(
      "Use the Playwright MCP browser automation tools to execute this script"
    );

    return results;
  }
}

module.exports = ScreenshotCapture;
```

### Task 5: Create Markdown Generator Script

**Duration: 25 minutes**

Create `scripts/screenshot-automation/generate_markdown.rb`:

```ruby
#!/usr/bin/env ruby

require 'json'
require 'fileutils'

class MarkdownGenerator
  def initialize
    @routes = JSON.parse(File.read('scripts/screenshot-automation/routes.json'))
    @base_path = File.expand_path('../..', __dir__)
  end

  def generate_user_type_markdown(user_type, screenshots_data)
    content = []
    content << "# #{user_type.capitalize} Routes Screenshots"
    content << ''
    content << "Generated on: #{Time.now.strftime('%Y-%m-%d %H:%M:%S')}"
    content << ''
    content << '## Route Tree'
    content << ''

    routes = @routes[user_type.to_s] || []

    # Group routes by controller for better organization
    grouped_routes = routes.group_by { |route| route['controller'] }

    grouped_routes.sort.each do |controller, controller_routes|
      content << "### #{controller.gsub('/', ' / ').titleize}"
      content << ''

      controller_routes.each do |route|
        content << "#### #{route['action'].titleize} - `#{route['path']}`"
        content << ''
        content << "**Controller:** `#{route['controller']}`  "
        content << "**Action:** `#{route['action']}`  "
        content << "**HTTP Method:** `#{route['verb']}`  "
        content << "**Route Name:** `#{route['name']}`" if route['name']
        content << ''

        # Find corresponding screenshot
        screenshot = find_screenshot(screenshots_data, route)
        if screenshot && screenshot['status'] == 'success'
          content <<
            "![#{route['controller']}_#{route['action']}](../screenshots/#{user_type}/#{screenshot['filename']})"
        else
          content << '*Screenshot not available*'
          content << ''
          if screenshot && screenshot['error']
            content << "**Error:** #{screenshot['error']}"
          end
        end
        content << ''
        content << '---'
        content << ''
      end
    end

    content.join("\n")
  end

  def find_screenshot(screenshots_data, route)
    return nil unless screenshots_data
    screenshots_data.find do |screenshot|
      screenshot['route']['controller'] == route['controller'] &&
        screenshot['route']['action'] == route['action']
    end
  end

  def generate_all_markdown(results = {})
    %w[talent scout superadmin].each do |user_type|
      screenshots_data = results[user_type] || []
      markdown_content =
        generate_user_type_markdown(user_type, screenshots_data)

      filename = "#{user_type}-routes-screenshots.md"
      File.write(filename, markdown_content)
      puts "Generated: #{filename}"
    end
  end

  def generate_summary_markdown(results = {})
    content = []
    content << '# Application Screenshots Summary'
    content << ''
    content << "Generated on: #{Time.now.strftime('%Y-%m-%d %H:%M:%S')}"
    content << ''

    %w[talent scout superadmin].each do |user_type|
      routes_count = (@routes[user_type] || []).length
      screenshots_data = results[user_type] || []
      success_count = screenshots_data.count { |s| s['status'] == 'success' }

      content << "## #{user_type.capitalize} Routes"
      content << ''
      content << "- **Total Routes:** #{routes_count}"
      content << "- **Screenshots Captured:** #{success_count}"
      content <<
        "- **Success Rate:** #{routes_count > 0 ? (success_count.to_f / routes_count * 100).round(1) : 0}%"
      content << ''
      content <<
        "[View #{user_type.capitalize} Screenshots](#{user_type}-routes-screenshots.md)"
      content << ''
    end

    File.write('screenshots-summary.md', content.join("\n"))
    puts 'Generated: screenshots-summary.md'
  end
end

# Generate markdown files
generator = MarkdownGenerator.new
generator.generate_all_markdown
generator.generate_summary_markdown

puts "\nMarkdown files generated successfully!"
```

### Task 6: Create Playwright MCP Execution Script

**Duration: 30 minutes**

Create `scripts/screenshot-automation/playwright_mcp_runner.md`:

````markdown
# Playwright MCP Screenshot Execution Guide

This guide provides step-by-step instructions for using Playwright MCP to capture screenshots.

## Prerequisites

1. Rails server running on localhost:5010
2. Database seeded with test users
3. All script files created in previous tasks

## Execution Steps

### Step 1: Discover Routes

```bash
cd /path/to/rails/app
ruby scripts/screenshot-automation/discover_routes.rb
```
````

### Step 2: Create Directory Structure

```bash
mkdir -p screenshots/{talent,scout,superadmin}
```

### Step 3: Use Playwright MCP for Screenshot Capture

#### Initialize Browser

Use Playwright MCP to start browser session:

- Navigate to http://localhost:5010
- Set viewport size to 1920x1080 for consistent screenshots

#### Capture Talent Routes

1. **Authentication**

   - Navigate to /sign_in
   - Fill email: <EMAIL>
   - Fill password: Secret1*3*5\*
   - Submit form
   - Wait for redirect to launchpad or talent area

2. **Route Capture Loop**
   For each route in talent routes:
   - Navigate to route URL
   - Wait for page load (2-3 seconds)
   - Take full page screenshot
   - Save to screenshots/talent/[controller]\_[action].png

#### Capture Scout Routes

1. **Sign Out and Re-authenticate**

   - Clear cookies/session
   - Navigate to /sign_in
   - Fill email: <EMAIL>
   - Fill password: Secret1*3*5\*
   - Submit form

2. **Route Capture Loop**
   For each route in scout routes:
   - Navigate to route URL
   - Wait for page load
   - Take full page screenshot
   - Save to screenshots/scout/[controller]\_[action].png

#### Capture SuperAdmin Routes

1. **Sign Out and Re-authenticate**

   - Clear cookies/session
   - Navigate to /sign_in
   - Fill email: <EMAIL>
   - Fill password: Secret1*3*5\*
   - Submit form

2. **Route Capture Loop**
   For each route in superadmin routes:
   - Navigate to route URL
   - Wait for page load
   - Take full page screenshot
   - Save to screenshots/superadmin/[controller]\_[action].png

### Step 4: Generate Documentation

```bash
ruby scripts/screenshot-automation/generate_markdown.rb
```

````

### Task 7: Error Handling and Edge Cases
**Duration: 20 minutes**

Create `scripts/screenshot-automation/error_handler.rb`:

```ruby
#!/usr/bin/env ruby

class ScreenshotErrorHandler
  COMMON_ERRORS = {
    'authentication_failed' => 'User authentication failed - check credentials',
    'route_not_found' => 'Route returned 404 - may require parameters',
    'permission_denied' => 'User lacks permission to access route',
    'timeout' => 'Page load timeout - may need longer wait time',
    'javascript_error' => 'JavaScript error on page - check console',
    'network_error' => 'Network connection issue'
  }.freeze

  def self.categorize_error(error_message)
    case error_message.downcase
    when /404|not found/
      'route_not_found'
    when /401|unauthorized|forbidden|403/
      'permission_denied'
    when /timeout/
      'timeout'
    when /javascript|js error/
      'javascript_error'
    when /network|connection/
      'network_error'
    when /sign.?in|login|authentication/
      'authentication_failed'
    else
      'unknown_error'
    end
  end

  def self.get_suggestion(error_type)
    suggestions = {
      'route_not_found' => [
        'Check if route requires URL parameters',
        'Verify route is accessible to this user type',
        'Check if route requires specific data to exist'
      ],
      'permission_denied' => [
        'Verify user has correct role/permissions',
        'Check if route requires organization membership',
        'Ensure user onboarding is complete'
      ],
      'timeout' => [
        'Increase page load timeout',
        'Check for slow database queries',
        'Verify all assets are loading properly'
      ],
      'authentication_failed' => [
        'Verify test user credentials',
        'Check if user account is verified',
        'Ensure user has completed required onboarding'
      ]
    }

    suggestions[error_type] || ['Review error details and application logs']
  end

  def self.generate_error_report(failed_screenshots)
    return if failed_screenshots.empty?

    puts "\n=== ERROR REPORT ==="

    error_groups = failed_screenshots.group_by { |s| categorize_error(s['error'] || '') }

    error_groups.each do |error_type, screenshots|
      puts "\n#{error_type.upcase.gsub('_', ' ')} (#{screenshots.count} routes):"
      puts COMMON_ERRORS[error_type] if COMMON_ERRORS[error_type]

      screenshots.each do |screenshot|
        puts "  - #{screenshot['url']}: #{screenshot['error']}"
      end

      puts "\nSuggestions:"
      get_suggestion(error_type).each do |suggestion|
        puts "  • #{suggestion}"
      end
    end
  end
end
````

### Task 8: Configuration and Customization

**Duration: 15 minutes**

Create `scripts/screenshot-automation/config.json`:

```json
{
  "browser": {
    "viewport": {
      "width": 1920,
      "height": 1080
    },
    "timeout": 10000,
    "waitAfterNavigation": 2000
  },
  "authentication": {
    "users": {
      "talent": {
        "email": "<EMAIL>",
        "password": "Secret1*3*5*"
      },
      "scout": {
        "email": "<EMAIL>",
        "password": "Secret1*3*5*"
      },
      "superadmin": {
        "email": "<EMAIL>",
        "password": "Secret1*3*5*"
      }
    }
  },
  "screenshots": {
    "format": "png",
    "fullPage": true,
    "quality": 90
  },
  "routes": {
    "exclude_patterns": ["/rails/", "/pay/", "/jobs/", "/:format", "/*path"],
    "include_only": {
      "talent": ["^/talent/"],
      "scout": ["^/scout/"],
      "superadmin": ["^/super_admin/"]
    }
  },
  "output": {
    "screenshots_dir": "screenshots",
    "markdown_dir": ".",
    "routes_file": "scripts/screenshot-automation/routes.json"
  }
}
```

### Task 9: Validation and Quality Assurance

**Duration: 20 minutes**

Create `scripts/screenshot-automation/validate_screenshots.rb`:

```ruby
#!/usr/bin/env ruby

require 'json'

class ScreenshotValidator
  def initialize
    @config = JSON.parse(File.read('scripts/screenshot-automation/config.json'))
    @screenshots_dir = @config['output']['screenshots_dir']
  end

  def validate_all
    puts '=== SCREENSHOT VALIDATION ==='

    %w[talent scout superadmin].each do |user_type|
      validate_user_type(user_type)
    end

    generate_validation_report
  end

  def validate_user_type(user_type)
    puts "\nValidating #{user_type} screenshots..."

    dir_path = File.join(@screenshots_dir, user_type)
    unless Dir.exist?(dir_path)
      puts "❌ Directory missing: #{dir_path}"
      return
    end

    screenshots = Dir.glob(File.join(dir_path, '*.png'))
    puts "📸 Found #{screenshots.count} screenshots"

    screenshots.each do |screenshot_path|
      validate_screenshot_file(screenshot_path)
    end
  end

  def validate_screenshot_file(file_path)
    filename = File.basename(file_path)
    file_size = File.size(file_path)

    if file_size < 1000
      # Less than 1KB likely indicates an error
      puts "⚠️  Suspiciously small file: #{filename} (#{file_size} bytes)"
    elsif file_size > 5_000_000
      # Larger than 5MB
      puts "⚠️  Large file: #{filename} (#{file_size / 1024 / 1024}MB)"
    else
      puts "✅ #{filename} (#{file_size / 1024}KB)"
    end
  end

  def generate_validation_report
    puts "\n=== VALIDATION SUMMARY ==="

    total_screenshots = 0
    %w[talent scout superadmin].each do |user_type|
      dir_path = File.join(@screenshots_dir, user_type)
      count =
        Dir.exist?(dir_path) ? Dir.glob(File.join(dir_path, '*.png')).count : 0
      total_screenshots += count
      puts "#{user_type.capitalize}: #{count} screenshots"
    end

    puts "Total: #{total_screenshots} screenshots captured"
  end
end

ScreenshotValidator.new.validate_all if __FILE__ == $0
```

## Troubleshooting Guide

### Common Issues and Solutions

#### Authentication Problems

- **Issue**: Login fails for test users
- **Solution**: Verify users exist in database with correct passwords
- **Command**: `bin/rails runner "User.find_by(email: '<EMAIL>')&.authenticate('Secret1*3*5*')"`

#### Route Access Issues

- **Issue**: 404 errors on valid routes
- **Solution**: Check if routes require URL parameters or specific data
- **Example**: Job detail routes need existing job IDs

#### Permission Errors

- **Issue**: 403/Unauthorized errors
- **Solution**: Verify user roles and organization memberships
- **Command**: `bin/rails runner "User.find_by(email: '<EMAIL>').roles.pluck(:name)"`

#### Screenshot Quality Issues

- **Issue**: Blank or partial screenshots
- **Solution**: Increase wait times, check for JavaScript errors
- **Adjustment**: Modify `waitAfterNavigation` in config.json

#### Performance Issues

- **Issue**: Slow screenshot capture
- **Solution**: Optimize database queries, reduce page complexity
- **Monitoring**: Check Rails logs for slow queries

### Debugging Commands

```bash
# Check user authentication
bin/rails runner "
user = User.find_by(email: '<EMAIL>')
puts 'User found: ' + user.present?.to_s
puts 'Roles: ' + user.roles.pluck(:name).join(', ') if user
puts 'Verified: ' + user.verified?.to_s if user
"

# List all routes for a namespace
bin/rails routes | grep "talent"
bin/rails routes | grep "scout"
bin/rails routes | grep "super_admin"

# Check organization memberships
bin/rails runner "
User.find_by(email: '<EMAIL>')&.organizations&.each do |org|
  puts \"Org: #{org.name} (ID: #{org.id})\"
end
"
```

## Maintenance and Updates

### Regular Maintenance Tasks

#### Weekly Route Updates

1. Run route discovery script to capture new routes
2. Update screenshot documentation for changed pages
3. Validate screenshot quality and completeness

#### Monthly Full Refresh

1. Clear all existing screenshots
2. Run complete screenshot capture process
3. Update all markdown documentation
4. Review and update error handling patterns

#### After Major Application Updates

1. Verify test user credentials still work
2. Check for new route patterns or authentication changes
3. Update configuration files if needed
4. Re-run validation scripts

### Automation Opportunities

#### CI/CD Integration

```yaml
# Example GitHub Actions workflow
name: Update Screenshots
on:
  schedule:
    - cron: "0 2 * * 1" # Weekly on Monday at 2 AM
  workflow_dispatch:

jobs:
  screenshots:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Rails
        run: |
          bundle install
          bin/rails db:setup
      - name: Generate Screenshots
        run: |
          bin/dev &
          sleep 30
          ruby scripts/screenshot-automation/discover_routes.rb
          # Run Playwright MCP screenshot capture
          ruby scripts/screenshot-automation/generate_markdown.rb
```

#### Monitoring and Alerts

- Set up alerts for failed screenshot captures
- Monitor screenshot file sizes for anomalies
- Track route coverage percentage over time

## Expected Deliverables

### File Structure

```
├── screenshots/
│   ├── talent/
│   │   ├── talent_jobs_index.png
│   │   ├── talent_jobs_show.png
│   │   └── ...
│   ├── scout/
│   │   ├── scout_jobs_index.png
│   │   ├── scout_applicants_index.png
│   │   └── ...
│   └── superadmin/
│       ├── super_admin_dashboard_index.png
│       ├── super_admin_admin_users_index.png
│       └── ...
├── scripts/screenshot-automation/
│   ├── discover_routes.rb
│   ├── auth_helper.js
│   ├── capture_screenshots.js
│   ├── generate_markdown.rb
│   ├── error_handler.rb
│   ├── validate_screenshots.rb
│   ├── config.json
│   ├── routes.json
│   └── playwright_mcp_runner.md
├── talent-routes-screenshots.md
├── scout-routes-screenshots.md
├── superadmin-routes-screenshots.md
└── screenshots-summary.md
```

### Documentation Files

1. **talent-routes-screenshots.md** - Complete talent user journey with screenshots
2. **scout-routes-screenshots.md** - Complete scout user journey with screenshots
3. **superadmin-routes-screenshots.md** - Complete admin interface with screenshots
4. **screenshots-summary.md** - Overview and statistics

### Quality Metrics

- **Route Coverage**: Aim for 95%+ of accessible routes
- **Screenshot Quality**: Full page, consistent viewport, no loading states
- **Documentation Completeness**: All routes documented with metadata
- **Error Rate**: Less than 5% failed captures

## Execution Prompt Template

Use this prompt to instruct Augment Agent to execute the screenshot documentation plan:

---

**PROMPT FOR AUGMENT AGENT EXECUTION:**

```
Please execute the Rails application screenshot documentation plan located at .ai/prds/screenshot_prd.md.

Follow these steps:

1. **Setup Phase**:
   - Create the required directory structure
   - Verify test users exist in the database
   - Ensure Rails server is running on localhost:5010

2. **Route Discovery**:
   - Create and run the route discovery script
   - Generate the routes.json file with all application routes

3. **Screenshot Capture**:
   - Use Playwright MCP to systematically capture screenshots
   - Authenticate as each user type (talent, scout, superadmin)
   - Navigate to each route and capture full-page screenshots
   - Handle errors gracefully and log any issues

4. **Documentation Generation**:
   - Generate markdown files for each user type
   - Create a summary report with statistics
   - Validate screenshot quality and completeness

5. **Quality Assurance**:
   - Run validation scripts
   - Generate error reports for any failed captures
   - Provide recommendations for improvements

Please use the task management system to track progress through each phase and provide status updates. Focus on creating comprehensive, high-quality documentation that will be valuable for client content review.

If you encounter any authentication issues, route access problems, or technical difficulties, please refer to the troubleshooting guide in the PRD and ask for assistance if needed.
```

---

This comprehensive plan provides everything needed to implement automated screenshot documentation for the Rails application using Playwright MCP, with proper error handling, validation, and maintenance procedures.
