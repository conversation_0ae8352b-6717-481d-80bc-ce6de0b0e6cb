.. list-table::
:header-rows: 1
:widths: 12 28 60
	•		•	Phase
	•	Scope
	•	Key changes / interfaces
	•		•	1
	•	Subscription gate infrastructure
	•	Talent::SubscriptionAccess concern; typed helpers; overlay partial; Talent::UpgradeController#show; route; layout hook; active-only rule (no trialing/past_due).
	•		•	2
	•	Apply gates + data masking
	•	Add prepend_before_action :require_talent_subscription! on restricted controllers; mask GET/HEAD with dummy data; hard-redirect on mutations (incl. saved/unsaved job); guard common set_* loaders.
	•		•	3
	•	Tests
	•	Unit tests for concern; controller integration for GET/HEAD vs POST/PUT/DELETE; system specs for overlay + hard-redirect; subscription-state matrix.

.. rubric:: TASK CHECKLIST

Phase 1 — Infrastructure
	•	☐ Add app/controllers/concerns/talent/subscription_access.rb with gate/mask helpers.
	•	☐ Add Talent::UpgradeController#show and view using plan helper.
	•	☐ Add route GET /talent/upgrade → talent/upgrade#show.
	•	☐ Add app/views/talent/shared/_subscription_gate.html.erb overlay (soft gate).
	•	☐ Update app/controllers/talent/base_controller.rb to include concern; expose helpers.
	•	☐ Update app/views/layouts/talent.html.erb to render overlay when flagged.

Phase 2 — Apply gates + masking
	•	☐ Gate: Talent::JobsController (index, show); mask data on safe verbs; hard-redirect on mutations (save/unsave).
	•	☐ Gate: Talent::MessagesController (index); hard-redirect create.
	•	☐ Gate: Talent::ConversationsController (index, show, archive, unarchive, bookmark, unbookmark).
	•	☐ Gate: Talent::JobApplicationsController (new, create, show, edit, update, withdraw).
	•	☐ Gate: Talent::ChatRequestsController (index, accept, decline) — no list access; no accept/decline.
	•	☐ Guard common set_* loaders to no-op under mask to avoid leaking records.

Phase 3 — Tests
	•	☐ Unit: concern behavior (active-only; GET/HEAD masked; mutations redirect).
	•	☐ Integration: each gated controller (safe verbs masked; mutations redirect).
	•	☐ System: overlay renders with dummy background + consistent copy; upgrade flow shows plans.
	•	☐ Matrix: subscription states (active, trialing, past_due, canceled, none).

.. rubric:: POLICY DECISIONS (APPLIED)
	•	Eligibility: active subscription only unlocks; trialing/past_due do not unlock.
	•	Chat requests: may be received (profile visible) but no list/view/accept/decline access.
	•	Profile visibility: unchanged; public/profile availability remains.
	•	Gating UX: soft-gate overlay for safe verbs; hard redirect for mutations; HEAD behaves like GET.
	•	Saved/unsaved jobs: treated as mutations → hard redirect; GET views masked so jobs aren’t visible.
	•	Multiple subs: unlocking condition is any active; users expected to have at most one active.

Existing foundations referenced: Talent layout and controllers exist (layouts/talent.html.erb; base controller + chat/messages/conversations/jobs/applications controllers).  ￼  ￼  ￼  ￼  ￼

Plan source of truth for plans: SubscriptionHelper#available_plans_for_new_subscriptions already returns talent plans; reuse it in overlay/upgrade.  ￼

⸻

PHASE 1 — Subscription gate infrastructure

Affected files
	•	app/controllers/concerns/talent/subscription_access.rb — new concern with gate/mask helpers.
	•	app/controllers/talent/upgrade_controller.rb — new controller.
	•	config/routes.rb — add get '/talent/upgrade'.
	•	app/views/talent/shared/_subscription_gate.html.erb — new overlay partial.
	•	app/views/talent/upgrade/show.html.erb — upgrade page using plan helper.
	•	app/controllers/talent/base_controller.rb — include concern; expose helpers. (Base exists.)  ￼
	•	app/views/layouts/talent.html.erb — render overlay if flagged. (Layout exists.)  ￼

Changes
	•	app/controllers/concerns/talent/subscription_access.rb
.. code:: ruby

# frozen_string_literal: true
module Talent
  module SubscriptionAccess
    extend ActiveSupport::Concern

    SAFE_VERBS = %w[GET HEAD OPTIONS].freeze

    included do
      helper_method :subscription_gate?, :mask_subscription_data?
    end

    # Active-only: no trialing/past_due unlock
    def talent_subscription_active?
      pp = Current.user&.payment_processor
      return false unless pp

      # Explicit status filter to avoid Pay's broader :active scopes including trialing
      pp.subscriptions.where(status: 'active').exists?
    end

    # Soft gate for safe verbs, hard redirect for mutations
    def require_talent_subscription!
      return if talent_subscription_active?

      if SAFE_VERBS.include?(request.request_method)
        @subscription_gate = true
        @mask_subscription_data = true
        # Do not redirect; views/layout will render overlay
      else
        redirect_to talent_upgrade_path, notice: 'Upgrade to access this feature.'
      end
    end

    def subscription_gate? = !!@subscription_gate
    def mask_subscription_data? = !!@mask_subscription_data
  end
end


	•	app/controllers/talent/upgrade_controller.rb
.. code:: ruby

# frozen_string_literal: true
module Talent
  class UpgradeController < BaseController
    skip_before_action :require_talent_signup_completed, only: :show

    def show
      @available_plans = helpers.available_plans_for_new_subscriptions
    end
  end
end


	•	config/routes.rb
.. code:: ruby

namespace :talent do
  get 'upgrade', to: 'upgrade#show', as: :upgrade
end


	•	app/views/talent/shared/_subscription_gate.html.erb
.. code:: erb

<div id="subscription-gate"
     class="fixed inset-0 z-50 flex items-center justify-center bg-stone-900/60 backdrop-blur">
  <div class="w-full max-w-lg p-6 bg-white shadow-xl rounded-2xl ring-1 ring-stone-200">
    <h2 class="text-lg font-semibold text-stone-900">
      Upgrade to unlock messaging, jobs, and applications
    </h2>
    <p class="mt-2 text-sm text-stone-600">
      Your profile stays visible and can receive chat requests, but access is restricted until you subscribe.
    </p>

    <% plans = @available_plans || helpers.available_plans_for_new_subscriptions %>
    <% if plans.present? %>
      <div class="grid gap-3 mt-4">
        <% plans.each do |plan_id, data| %>
          <div class="p-4 border rounded-lg border-stone-200">
            <div class="flex items-center justify-between">
              <div>
                <div class="font-medium text-stone-900"><%= data[:name] %></div>
                <div class="text-xs text-stone-600"><%= data[:billing_cycle] %></div>
              </div>
              <%= button_to talent_subscription_path,
                            method: :post,
                            params: { plan: plan_id },
                            class: "inline-flex items-center rounded-lg px-3 py-2 text-sm font-medium bg-stone-900 text-white hover:bg-stone-800" do %>
                Subscribe
              <% end %>
            </div>
          </div>
        <% end %>
      </div>
    <% end %>
  </div>
</div>

.. note::
Plan helper already exists and returns talent plans.  ￼

	•	app/views/talent/upgrade/show.html.erb
.. code:: erb

<%= render "talent/shared/subscription_gate", locals: { } %>


	•	app/controllers/talent/base_controller.rb
.. code:: ruby

include Talent::SubscriptionAccess
helper_method :subscription_gate?, :mask_subscription_data?


	•	app/views/layouts/talent.html.erb — render overlay at the end of <main> when flagged
.. code:: erb

<div class="container px-4 py-8 mx-auto">
  <%= yield %>
</div>

<%# Soft gate overlay renders above page when subscription required %>
<% if defined?(subscription_gate?) && subscription_gate? %>
  <%= render "talent/shared/subscription_gate" %>
<% end %>

Layout exists and is the right hook point.  ￼

Unit tests (Phase 1)
	•	test/controllers/concerns/talent/subscription_access_test.rb:
	•	GET to a dummy action under gate → assigns @subscription_gate=true and returns 200 with overlay present.
	•	HEAD behaves like GET (overlay; no redirect).
	•	POST under gate → 302 to /talent/upgrade.
	•	talent_subscription_active? returns true only for status 'active'; returns false for 'trialing', 'past_due'.

⸻

PHASE 2 — Apply gates + data masking

Affected files
	•	app/controllers/talent/jobs_controller.rb — gate; mask counts/collections; guard set_job. (Controller exists.)  ￼
	•	app/controllers/talent/messages_controller.rb — gate; mask lists; redirect on create. (Exists.)  ￼
	•	app/controllers/talent/conversations_controller.rb — gate; mask counts; guard set_conversation usage. (Exists.)  ￼
	•	app/controllers/talent/job_applications_controller.rb — gate; guard set_job/set_job_application; redirect on mutations. (Exists.)  ￼
	•	app/controllers/talent/chat_requests_controller.rb — gate; no list access; redirect on accept/decline. (Exists.)  ￼
	•	(Saved Jobs controller) — add gate on save/unsave actions (snippet exists with set_job + turbo_stream.replace).  ￼

Changes
	•	In each restricted controller, add at top:
.. code:: ruby

prepend_before_action :require_talent_subscription!, only: %i[index show new edit create update destroy accept decline withdraw archive unarchive bookmark unbookmark]


	•	Mask data in safe verbs (dummy/empty so CSS/JS overlay removal won’t leak):
Talent::JobsController
.. code:: ruby

def index
  return mask_jobs_index if mask_subscription_data?
  # existing logic…
end

def show
  return mask_jobs_show if mask_subscription_data?
  # existing logic…
end

private

def mask_jobs_index
  @tab = params[:tab] || 'all'
  @all_jobs_count = 0
  @saved_jobs_count = 0
  @jobs = []
  render :index
end

def mask_jobs_show
  # Minimal placeholder; do not hit DB
  @job = Job.new(title: 'Premium job')
  @is_saved = false
  render :show
end

def set_job
  return if mask_subscription_data?
  @job = Job.find(params[:id])
end

(Controller scaffolding present; set_job exists.)  ￼  ￼
Talent::MessagesController
.. code:: ruby

def index
  if mask_subscription_data?
    @conversations = []
    @recent_messages = []
    return render :index
  end
  # existing logic…
end

def create
  return redirect_to(talent_upgrade_path, notice: 'Upgrade to message.') unless talent_subscription_active?
  # existing logic…
end

(Index/create exist.)  ￼  ￼
Talent::ConversationsController
.. code:: ruby

def index
  if mask_subscription_data?
    @conversations = Conversation.none
    @active_count = 0
    @archived_count = 0
    @chat_requests_count = 0
    return render :index
  end
  # existing logic…
end

(Index and counts exist.)  ￼
Talent::JobApplicationsController
.. code:: ruby

def new
  return redirect_to(talent_upgrade_path, notice: 'Upgrade to apply.') unless talent_subscription_active?
  # existing logic…
end

def create
  return redirect_to(talent_upgrade_path, notice: 'Upgrade to apply.') unless talent_subscription_active?
  # existing logic…
end

def show
  if mask_subscription_data?
    @job_application = JobApplication.new
    return render :show
  end
  # existing logic…
end

private

def set_job
  return if mask_subscription_data?
  @job = Job.find(params[:job_id])
end

def set_job_application
  return if mask_subscription_data?
  @job_application = Current.user.job_applications.find(params[:id])
end

(New/create/show/setters exist.)  ￼  ￼  ￼
Talent::ChatRequestsController — fully blocked
.. code:: ruby

def index
  return redirect_to(talent_upgrade_path, notice: 'Upgrade to manage chat requests.') unless talent_subscription_active?
end

def accept
  return redirect_to(talent_upgrade_path, notice: 'Upgrade to accept chat requests.') unless talent_subscription_active?
  # existing logic…
end

def decline
  return redirect_to(talent_upgrade_path, notice: 'Upgrade to decline chat requests.') unless talent_subscription_active?
  # existing logic…
end

(Index/accept/decline exist.)  ￼  ￼
Saved/Unsave Jobs (treat as mutations)
	•	Controller handling unsave/save (snippet with turbo_stream.replace): add
.. code:: ruby

prepend_before_action :require_talent_subscription!, only: %i[create destroy]

	•	And guard loader:
.. code:: ruby

def set_job
  return if mask_subscription_data?
  @job = Job.find(params[:id])
end


(Snippet exists with set_job.)  ￼

Unit tests (Phase 2)
	•	test/controllers/talent/jobs_controller_test.rb:
	•	Non-subscriber GET /talent/jobs → 200 + overlay present; assigns empty @jobs; counts = 0.
	•	Non-subscriber GET /talent/jobs/:id → 200 + overlay; @job is new record (not persisted).
	•	Non-subscriber POST /talent/saved_jobs (or equivalent) → 302 to /talent/upgrade.
	•	test/controllers/talent/messages_controller_test.rb:
	•	GET /talent/messages masked; POST /talent/conversations/:id/messages redirects.
	•	test/controllers/talent/conversations_controller_test.rb:
	•	GET /talent/conversations masked; counts zero.
	•	test/controllers/talent/job_applications_controller_test.rb:
	•	GET /talent/jobs/:job_id/applications/new redirects; POST redirects; GET /applications/:id masked.
	•	test/controllers/talent/chat_requests_controller_test.rb:
	•	GET /talent/chat_requests redirects (no access); POST accept/decline redirects.

⸻

PHASE 3 — System tests + state matrix

Affected files
	•	test/system/talent_subscription_gate_test.rb — overlay/system flow.
	•	test/controllers/concerns/talent/subscription_access_test.rb — unit for concern.

Scenarios
	•	System: As non-subscriber, visit jobs/messages/conversations/applications → overlay visible, background masked (no real records rendered). As subscriber (factory with a Stripe Pay::Subscription status 'active'), no overlay anywhere; jobs/messages accessible.
	•	HEAD requests: HEAD /talent/jobs returns 200 and triggers mask path (assert no redirect).
	•	State matrix:
	•	status: 'active' → no gate.
	•	status: 'trialing' → gate.
	•	status: 'past_due' → gate.
	•	status: 'canceled' / none → gate.

⸻

OPEN EDGE-CASE NOTES (brief)
	•	If any view assumes persisted records (e.g., calls @job.id), ensure conditional safe-guards in templates (or set placeholder id via -1 in mask if required). Keep template tweaks localized to job show partials if failures occur.
	•	Background “dummy data” policy is satisfied by returning empty collections/minimal new-records to avoid DB leakage even if overlay is hidden.

Design/copy
	•	Overlay uses existing stone palette and heading/body styles consistent with talent layout. (Talent layout classes present.)  ￼

Plan integration with existing plan UI
	•	Overlay/upgrade fetch plan cards from available_plans_for_new_subscriptions to keep a single source of truth for names/billing/features.  ￼