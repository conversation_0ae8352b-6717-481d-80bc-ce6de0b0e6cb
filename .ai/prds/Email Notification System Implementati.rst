# Email Notification System Implementation Plan (Using Noticed Gem)

## Open Questions
- **Email frequency for job alerts**: Should talent receive real-time notifications for each matching job, or should we batch them into daily/weekly digests?
- **In-app notifications**: Should we also implement in-app notifications alongside emails using Noticed's database delivery method?
- **Notification retention**: How long should we keep notification records in the database?

## Task Checklist

### Phase 1: Infrastructure & Core Notifications
☐ Add Noticed gem and generate installation files
☐ Create email preference models and migrations
☐ Create notification classes for job lifecycle
☐ Create notification classes for messaging
☐ Add email preference management to User model
☐ Update user settings UI for email preferences
☐ Write unit tests for notifications
☐ Write unit tests for email preferences

### Phase 2: User Journey & Engagement Emails
☐ Create welcome notification series
☐ Create profile completion notifications
☐ Create job alert notifications
☐ Create performance summary notifications
☐ Create milestone notifications
☐ Add notification scheduling jobs
☐ Write unit tests for notification scheduling
☐ Write unit tests for job matching

### Phase 3: Retention & Behavioral Notifications
☐ Create inactive user notifications
☐ Create abandoned action notifications
☐ Create feature announcement notifications
☐ Add behavioral trigger jobs
☐ Write unit tests for behavioral triggers
☐ Write unit tests for retention notifications

## Phase 1: Infrastructure & Core Notifications

### Affected Files
- ``Gemfile`` - Add Noticed gem
- ``db/migrate/xxx_create_noticed_tables.rb`` - Noticed gem migrations
- ``db/migrate/xxx_create_email_preferences.rb`` - User email preferences
- ``app/models/email_preference.rb`` - Email preference model
- ``app/models/concerns/notifiable.rb`` - User notification concern
- ``app/notifications/application_notification.rb`` - Base notification class
- ``app/notifications/job_posted_notification.rb`` - Job posted notification
- ``app/notifications/application_received_notification.rb`` - Application received
- ``app/notifications/application_status_notification.rb`` - Application status changes
- ``app/notifications/new_message_notification.rb`` - New message notification
- ``app/views/user_mailer/*.html.erb`` - Email templates
- ``app/controllers/users/settings_controller.rb`` - Settings controller
- ``app/views/users/settings/notifications.html.erb`` - Email preferences UI

### Code Changes

**Gemfile**

```ruby
# Notifications
gem 'noticed', '~> 2.0'
```

**Run installation**

```bash
bundle install
rails generate noticed:install
rails db:migrate
```

**Database Migrations**

``db/migrate/xxx_create_email_preferences.rb``:

```ruby
class CreateEmailPreferences < ActiveRecord::Migration[7.0]
  def change
    create_table :email_preferences do |t|
      t.references :user, null: false, foreign_key: true
      
      # Notification categories
      t.boolean :job_notifications, default: true
      t.boolean :message_notifications, default: true
      t.boolean :marketing_emails, default: true
      t.boolean :performance_summaries, default: true
      
      # Frequency settings
      t.integer :job_alert_frequency, default: 0 # 0: real-time, 1: daily, 2: weekly
      t.integer :summary_frequency, default: 2 # 0: never, 1: weekly, 2: monthly
      
      t.timestamps
    end
    
    add_index :email_preferences, :user_id, unique: true
  end
end
```

**Models**

``app/models/email_preference.rb``:

```ruby
class EmailPreference < ApplicationRecord
  belongs_to :user
  
  enum job_alert_frequency: { real_time: 0, daily: 1, weekly: 2 }
  enum summary_frequency: { never: 0, weekly: 1, monthly: 2 }
  
  def can_receive?(category)
    case category
    when :job_notifications
      job_notifications?
    when :message_notifications
      message_notifications?
    when :marketing_emails
      marketing_emails?
    when :performance_summaries
      performance_summaries?
    else
      true # Allow transactional emails by default
    end
  end
  
  def self.categories
    {
      job_notifications: {
        label: 'Job updates',
        description: 'New jobs posted, applications received, job status changes'
      },
      message_notifications: {
        label: 'Messages',
        description: 'New messages from scouts or talent'
      },
      marketing_emails: {
        label: 'Tips and milestones',
        description: 'Profile tips, achievement celebrations, platform updates'
      },
      performance_summaries: {
        label: 'Performance reports',
        description: 'Weekly or monthly summaries of your activity'
      }
    }
  end
end
```

``app/models/concerns/notifiable.rb``:

```ruby
module Notifiable
  extend ActiveSupport::Concern
  
  included do
    has_many :notifications, as: :recipient, dependent: :destroy, class_name: "Noticed::Notification"
    has_many :notification_events, through: :notifications, source: :event, class_name: "Noticed::Event"
    has_one :email_preference, dependent: :destroy
    
    after_create :create_default_email_preference
  end
  
  def can_receive_email?(category)
    return false unless email_verified?
    email_preference&.can_receive?(category) != false
  end
  
  private
  
  def create_default_email_preference
    EmailPreference.create!(user: self)
  end
end
```

**Update User model**

``app/models/user.rb``:

```ruby
class User < ApplicationRecord
  include Notifiable
  
  # ... existing code ...
end
```

**Notifications**

``app/notifications/application_notification.rb``:

```ruby
class ApplicationNotification < Noticed::Event
  deliver_by :database
  
  # Email delivery with preference check
  deliver_by :email do |config|
    config.mailer = "UserMailer"
    config.if = ->(notification) {
      recipient = notification.recipient
      recipient.can_receive_email?(notification.email_category)
    }
  end
  
  # Override in subclasses
  def email_category
    :general
  end
  
  # Helper to get the recipient user
  def recipient_user
    recipient.is_a?(User) ? recipient : recipient.user
  end
end
```

``app/notifications/job_posted_notification.rb``:

```ruby
class JobPostedNotification < ApplicationNotification
  deliver_by :email do |config|
    config.mailer = "UserMailer"
    config.method = :job_posted
    config.if = ->(notification) {
      notification.recipient_user.can_receive_email?(:job_notifications)
    }
  end
  
  param :job
  
  def email_category
    :job_notifications
  end
  
  def message
    "Your job '#{params[:job].title}' has been posted successfully"
  end
  
  def url
    job_path(params[:job])
  end
end
```

``app/notifications/application_received_notification.rb``:

```ruby
class ApplicationReceivedNotification < ApplicationNotification
  deliver_by :email do |config|
    config.mailer = "UserMailer"
    config.method = :application_received
    config.params = ->(notification) {
      { 
        application: notification.params[:application],
        milestone: notification.params[:milestone]
      }
    }
    config.if = ->(notification) {
      notification.recipient_user.can_receive_email?(:job_notifications)
    }
  end
  
  param :application
  param :job
  param :milestone # nil, :first, :five, :ten, :twenty
  
  def email_category
    :job_notifications
  end
  
  def message
    case params[:milestone]
    when :first
      "You received your first application for '#{params[:job].title}'"
    when :five
      "5 talented writers have applied to '#{params[:job].title}'"
    when :ten
      "10 talented writers have applied to '#{params[:job].title}'"
    when :twenty
      "20+ talented writers have applied to '#{params[:job].title}'"
    else
      "New application received for '#{params[:job].title}'"
    end
  end
  
  def url
    job_applications_path(params[:job])
  end
end
```

``app/notifications/application_status_notification.rb``:

```ruby
class ApplicationStatusNotification < ApplicationNotification
  deliver_by :email do |config|
    config.mailer = "UserMailer"
    config.method = :application_status_changed
    config.if = ->(notification) {
      notification.recipient_user.can_receive_email?(:job_notifications)
    }
  end
  
  param :application
  param :job
  param :previous_status
  
  def email_category
    :job_notifications
  end
  
  def message
    case params[:application].status
    when 'viewed'
      "#{params[:job].scout.organization_name} viewed your application"
    when 'interviewing'
      "#{params[:job].scout.organization_name} wants to interview you!"
    when 'accepted'
      "Congratulations! Your application for '#{params[:job].title}' was accepted"
    when 'rejected'
      "Update on your application for '#{params[:job].title}'"
    end
  end
  
  def url
    talent_application_path(params[:application])
  end
end
```

``app/notifications/new_message_notification.rb``:

```ruby
class NewMessageNotification < ApplicationNotification
  deliver_by :email do |config|
    config.mailer = "UserMailer"
    config.method = :new_message
    config.if = ->(notification) {
      notification.recipient_user.can_receive_email?(:message_notifications)
    }
  end
  
  param :message
  param :conversation
  
  def email_category
    :message_notifications
  end
  
  def message
    "New message from #{params[:message].sender.display_name}"
  end
  
  def url
    conversation_path(params[:conversation])
  end
end
```

**Update existing mailer to work with Noticed**

``app/mailers/user_mailer.rb``:

```ruby
class UserMailer < ApplicationMailer
  def job_posted(notification)
    @user = notification.recipient
    @job = notification.params[:job]
    @scout = @job.scout
    
    mail(
      to: @user.email,
      subject: "Your job '#{@job.title}' has been posted"
    )
  end
  
  def application_received(notification)
    @user = notification.recipient
    @application = notification.params[:application]
    @job = @application.job
    @scout = @job.scout
    @talent = @application.talent
    @milestone = notification.params[:milestone]
    
    subject = case @milestone
              when :first
                "You received your first application for '#{@job.title}'"
              when :five, :ten, :twenty
                "#{@milestone == :twenty ? '20+' : @milestone.to_s.capitalize} talented writers have applied to '#{@job.title}'"
              else
                "New application received for '#{@job.title}'"
              end
    
    mail(to: @user.email, subject: subject)
  end
  
  def application_status_changed(notification)
    @user = notification.recipient
    @application = notification.params[:application]
    @job = @application.job
    @talent = @application.talent
    @scout = @job.scout
    
    subject = case @application.status
              when 'viewed'
                "#{@scout.organization_name} viewed your application"
              when 'interviewing'
                "#{@scout.organization_name} wants to interview you!"
              when 'accepted'
                "Congratulations! Your application was accepted"
              when 'rejected'
                "Update on your application for '#{@job.title}'"
              end
    
    mail(to: @user.email, subject: subject)
  end
  
  def new_message(notification)
    @user = notification.recipient
    @message = notification.params[:message]
    @conversation = notification.params[:conversation]
    @sender = @message.sender
    
    mail(
      to: @user.email,
      subject: "New message from #{@sender.display_name}"
    )
  end
end
```

**Email Templates**

``app/views/user_mailer/job_posted.html.erb``:

```erb
<h2>Your job has been posted!</h2>

<p>Hi <%= @scout.display_name %>,</p>

<p>Great news! Your job "<%= @job.title %>" is now live on Ghostwrote and visible to our talented writers.</p>

<div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
  <h3><%= @job.title %></h3>
  <p><%= truncate(@job.description, length: 200) %></p>
  <p>
    <strong>Budget:</strong> <%= number_to_currency(@job.rate_amount) %><br>
    <strong>Type:</strong> <%= @job.job_type.humanize %><br>
    <strong>Expires:</strong> <%= @job.expires_at.strftime("%B %d, %Y") %>
  </p>
</div>

<p>
  <%= link_to "View Your Job", job_url(@job), 
      style: "background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;" %>
</p>

<h3>What happens next?</h3>
<ul>
  <li>Talented writers will start applying to your job</li>
  <li>We'll notify you as soon as you receive applications</li>
  <li>You can review applications and message writers directly</li>
</ul>

<p>Best regards,<br>The Ghostwrote Team</p>
```

``app/views/user_mailer/application_received.html.erb``:

```erb
<% if @milestone == :first %>
  <h2>🎉 You received your first application!</h2>
  <p>Hi <%= @scout.display_name %>,</p>
  <p>Exciting news! <%= @talent.display_name %> just applied to your job "<%= @job.title %>".</p>
<% else %>
  <h2>New application for "<%= @job.title %>"</h2>
  <p>Hi <%= @scout.display_name %>,</p>
  <p><%= @talent.display_name %> just applied to your job.</p>
<% end %>

<div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
  <h3><%= @talent.display_name %></h3>
  <p><%= @talent.tagline %></p>
  <% if @talent.skills.any? %>
    <p><strong>Skills:</strong> <%= @talent.skills.pluck(:name).join(", ") %></p>
  <% end %>
  <p><strong>Rate:</strong> <%= number_to_currency(@talent.hourly_rate) %>/hour</p>
</div>

<h3>Application Message:</h3>
<p style="background: #fff; padding: 15px; border-left: 4px solid #007bff;">
  <%= simple_format(@application.cover_letter) %>
</p>

<p>
  <%= link_to "Review Application", job_application_url(@job, @application), 
      style: "background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;" %>
</p>

<% if @milestone %>
  <p style="margin-top: 30px; padding: 15px; background: #e8f4f8; border-radius: 4px;">
    <strong>🎯 Milestone reached!</strong> You've received <%= @milestone == :twenty ? '20+' : @milestone.to_s %> applications for this job.
  </p>
<% end %>

<p>Best regards,<br>The Ghostwrote Team</p>
```

``app/views/user_mailer/application_status_changed.html.erb``:

```erb
<% case @application.status %>
<% when 'viewed' %>
  <h2>Your application was viewed!</h2>
  <p>Hi <%= @talent.display_name %>,</p>
  <p>Good news! <%= @scout.organization_name %> has viewed your application for "<%= @job.title %>".</p>
  <p>They're reviewing applications and may reach out soon if they're interested in moving forward.</p>

<% when 'interviewing' %>
  <h2>🎉 Interview request!</h2>
  <p>Hi <%= @talent.display_name %>,</p>
  <p>Great news! <%= @scout.organization_name %> wants to interview you for "<%= @job.title %>".</p>
  <p>They're interested in learning more about you and discussing the project in detail.</p>

<% when 'accepted' %>
  <h2>🎉 Congratulations! Your application was accepted!</h2>
  <p>Hi <%= @talent.display_name %>,</p>
  <p>Amazing news! <%= @scout.organization_name %> has accepted your application for "<%= @job.title %>".</p>
  <p>You can now start working on this project. The scout will reach out with next steps.</p>

<% when 'rejected' %>
  <h2>Update on your application</h2>
  <p>Hi <%= @talent.display_name %>,</p>
  <p>Thank you for applying to "<%= @job.title %>". After careful consideration, <%= @scout.organization_name %> has decided to move forward with another candidate.</p>
  <p>Don't be discouraged! There are many other opportunities on Ghostwrote that might be a great fit for your skills.</p>
<% end %>

<div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
  <h3><%= @job.title %></h3>
  <p><strong>Company:</strong> <%= @scout.organization_name %></p>
  <p><strong>Budget:</strong> <%= number_to_currency(@job.rate_amount) %></p>
</div>

<p>
  <%= link_to "View Application", talent_application_url(@application), 
      style: "background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;" %>
</p>

<% if @application.rejected? %>
  <p>
    <%= link_to "Browse More Jobs", jobs_url, 
        style: "background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block; margin-top: 10px;" %>
  </p>
<% end %>

<p>Best regards,<br>The Ghostwrote Team</p>
```

``app/views/user_mailer/new_message.html.erb``:

```erb
<h2>New message from <%= @sender.display_name %></h2>

<p>Hi <%= @user.display_name %>,</p>

<p>You have a new message in your conversation with <%= @sender.display_name %>.</p>

<div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
  <p><strong><%= @sender.display_name %>:</strong></p>
  <p style="margin-left: 20px;"><%= truncate(@message.content, length: 200) %></p>
</div>

<p>
  <%= link_to "View Conversation", conversation_url(@conversation), 
      style: "background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;" %>
</p>

<p>Best regards,<br>The Ghostwrote Team</p>
```

**Settings UI**

``app/controllers/users/settings_controller.rb``:

```ruby
class Users::SettingsController < ApplicationController
  before_action :authenticate_user!
  
  def notifications
    @email_preference = current_user.email_preference || current_user.create_email_preference
  end
  
  def update_notifications
    @email_preference = current_user.email_preference
    
    if @email_preference.update(email_preference_params)
      redirect_to notifications_settings_path, notice: 'Email preferences updated successfully.'
    else
      render :notifications
    end
  end
  
  private
  
  def email_preference_params
    params.require(:email_preference).permit(
      :job_notifications,
      :message_notifications,
      :marketing_emails,
      :performance_summaries,
      :job_alert_frequency,
      :summary_frequency
    )
  end
end
```

``app/views/users/settings/notifications.html.erb``:

```erb
<div class="settings-container">
  <h1>Email Notifications</h1>
  <p class="text-muted">Control which emails you receive from Ghostwrote</p>
  
  <%= form_with model: @email_preference, url: update_notifications_settings_path, method: :patch do |f| %>
    <div class="notification-settings">
      <h3>Email Categories</h3>
      
      <% EmailPreference.categories.each do |key, config| %>
        <div class="notification-category">
          <div class="notification-toggle">
            <%= f.check_box key, class: 'form-check-input' %>
            <div class="notification-info">
              <label for="email_preference_<%= key %>" class="notification-label">
                <%= config[:label] %>
              </label>
              <p class="notification-description"><%= config[:description] %></p>
            </div>
          </div>
        </div>
      <% end %>
      
      <% if current_user.talent? %>
        <h3>Job Alert Frequency</h3>
        <div class="frequency-setting">
          <%= f.label :job_alert_frequency, 'How often would you like to receive job alerts?' %>
          <%= f.select :job_alert_frequency, 
                       options_for_select([
                         ['Real-time (as jobs are posted)', 'real_time'],
                         ['Daily digest', 'daily'],
                         ['Weekly digest', 'weekly']
                       ], @email_preference.job_alert_frequency),
                       class: 'form-select' %>
        </div>
      <% end %>
      
      <h3>Performance Summary Frequency</h3>
      <div class="frequency-setting">
        <%= f.label :summary_frequency, 'How often would you like to receive performance summaries?' %>
        <%= f.select :summary_frequency, 
                     options_for_select([
                       ['Never', 'never'],
                       ['Weekly', 'weekly'],
                       ['Monthly', 'monthly']
                     ], @email_preference.summary_frequency),
                     class: 'form-select' %>
      </div>
      
      <div class="form-actions">
        <%= f.submit 'Save Preferences', class: 'btn btn-primary' %>
      </div>
    </div>
  <% end %>
</div>
```

**Routes**

``config/routes.rb``:

```ruby
Rails.application.routes.draw do
  # ... existing routes ...
  
  namespace :users do
    resource :settings, only: [] do
      get :notifications
      patch :update_notifications
    end
  end
end
```

**Trigger notifications in existing code**

``app/models/job.rb``:

```ruby
class Job < ApplicationRecord
  # ... existing code ...
  
  after_create :notify_scout_job_posted
  
  private
  
  def notify_scout_job_posted
    JobPostedNotification.with(job: self).deliver(scout.user)
  end
end
```

``app/models/application.rb``:

```ruby
class Application < ApplicationRecord
  # ... existing code ...
  
  after_create :notify_scout_application_received
  after_update :notify_talent_status_changed, if: :saved_change_to_status?
  
  private
  
  def notify_scout_application_received
    milestone = determine_application_milestone
    ApplicationReceivedNotification.with(
      application: self,
      job: job,
      milestone: milestone
    ).deliver(job.scout.user)
  end
  
  def notify_talent_status_changed
    return unless %w[viewed interviewing accepted rejected].include?(status)
    
    ApplicationStatusNotification.with(
      application: self,
      job: job,
      previous_status: status_before_last_save
    ).deliver(talent.user)
  end
  
  def determine_application_milestone
    count = job.applications.count
    case count
    when 1 then :first
    when 5 then :five
    when 10 then :ten
    when 20 then :twenty
    else nil
    end
  end
end
```

``app/models/message.rb``:

```ruby
class Message < ApplicationRecord
  # ... existing code ...
  
  after_create :notify_recipient
  
  private
  
  def notify_recipient
    NewMessageNotification.with(
      message: self,
      conversation: conversation
    ).deliver(recipient)
  end
end
```

**Unit Tests**

``spec/models/email_preference_spec.rb``:

```ruby
require 'rails_helper'

RSpec.describe EmailPreference, type: :model do
  let(:user) { create(:user) }
  let(:preference) { create(:email_preference, user: user) }
  
  describe '#can_receive?' do
    context 'with all preferences enabled' do
      it 'allows all email categories' do
        expect(preference.can_receive?(:job_notifications)).to be true
        expect(preference.can_receive?(:message_notifications)).to be true
        expect(preference.can_receive?(:marketing_emails)).to be true
      end
    end
    
    context 'with job notifications disabled' do
      before { preference.update!(job_notifications: false) }
      
      it 'blocks job notification emails' do
        expect(preference.can_receive?(:job_notifications)).to be false
      end
      
      it 'allows other email categories' do
        expect(preference.can_receive?(:message_notifications)).to be true
      end
    end
  end
end
```

``spec/notifications/job_posted_notification_spec.rb``:

```ruby
require 'rails_helper'

RSpec.describe JobPostedNotification do
  let(:scout) { create(:scout) }
  let(:job) { create(:job, scout: scout) }
  let(:user) { scout.user }
  
  describe 'email delivery' do
    it 'sends email when preferences allow' do
      expect {
        described_class.with(job: job).deliver(user)
      }.to change { ActionMailer::Base.deliveries.count }.by(1)
    end
    
    it 'does not send email when preferences disabled' do
      user.email_preference.update!(job_notifications: false)
      
      expect {
        described_class.with(job: job).deliver(user)
      }.not_to change { ActionMailer::Base.deliveries.count }
    end
    
    it 'creates notification record' do
      expect {
        described_class.with(job: job).deliver(user)
      }.to change { user.notifications.count }.by(1)
    end
  end
end
```

## Phase 2: User Journey & Engagement Emails

### Affected Files
- ``app/notifications/welcome_notification.rb`` - Welcome email series
- ``app/notifications/profile_incomplete_notification.rb`` - Profile completion reminder
- ``app/notifications/job_alert_notification.rb`` - Job alerts for talent
- ``app/notifications/performance_summary_notification.rb`` - Performance summaries
- ``app/notifications/milestone_achieved_notification.rb`` - Achievement celebrations
- ``app/services/job_matcher_service.rb`` - Job-talent matching logic
- ``app/services/performance_calculator_service.rb`` - User metrics calculation
- ``app/jobs/send_welcome_series_job.rb`` - Welcome email scheduling
- ``app/jobs/send_job_alerts_job.rb`` - Job alert processing
- ``app/jobs/send_performance_summaries_job.rb`` - Performance summaries
- ``app/jobs/check_profile_completion_job.rb`` - Profile completion checks
- ``lib/tasks/notifications.rake`` - Scheduled notification tasks

### Code Changes

**Notifications**

``app/notifications/welcome_notification.rb``:

```ruby
class WelcomeNotification < ApplicationNotification
  deliver_by :email do |config|
    config.mailer = "UserMailer"
    config.method = ->(notification) { 
      "welcome_#{notification.params[:step]}_#{notification.params[:user_type]}" 
    }
    config.if = ->(notification) {
      notification.recipient.can_receive_email?(:marketing_emails)
    }
  end
  
  param :step # :day1, :day3, :day7
  param :user_type # :scout, :talent
  
  def email_category
    :marketing_emails
  end
  
  def message
    case params[:step]
    when :day1
      "Welcome to Ghostwrote!"
    when :day3
      "Tips to get started on Ghostwrote"
    when :day7
      "Success stories from our community"
    end
  end
end
```

``app/notifications/profile_incomplete_notification.rb``:

```ruby
class ProfileIncompleteNotification < ApplicationNotification
  deliver_by :email do |config|
    config.mailer = "UserMailer"
    config.method = :profile_incomplete
    config.if = ->(notification) {
      notification.recipient.can_receive_email?(:marketing_emails)
    }
  end
  
  param :missing_fields
  param :user_type # :scout, :talent
  
  def email_category
    :marketing_emails
  end
  
  def message
    "Complete your profile to #{params[:user_type] == :scout ? 'attract top talent' : 'get more opportunities'}"
  end
  
  def url
    params[:user_type] == :scout ? edit_scout_profile_path : edit_talent_profile_path
  end
end
```

``app/notifications/job_alert_notification.rb``:

```ruby
class JobAlertNotification < ApplicationNotification
  deliver_by :email do |config|
    config.mailer = "UserMailer"
    config.method = :job_alert
    config.if = ->(notification) {
      notification.recipient.can_receive_email?(:job_notifications)
    }
  end
  
  param :jobs
  param :frequency # :real_time, :daily, :weekly
  
  def email_category
    :job_notifications
  end
  
  def message
    if params[:frequency] == :real_time && params[:jobs].count == 1
      "New job: #{params[:jobs].first.title}"
    else
      "#{params[:jobs].count} new jobs match your skills"
    end
  end
  
  def url
    jobs_path(skills: recipient.talent.skill_ids)
  end
end
```

``app/notifications/performance_summary_notification.rb``:

```ruby
class PerformanceSummaryNotification < ApplicationNotification
  deliver_by :email do |config|
    config.mailer = "UserMailer"
    config.method = :performance_summary
    config.if = ->(notification) {
      notification.recipient.can_receive_email?(:performance_summaries) &&
      notification.params[:metrics][:has_activity]
    }
  end
  
  param :metrics
  param :period # :weekly, :monthly
  param :user_type # :scout, :talent
  
  def email_category
    :performance_summaries
  end
  
  def message
    "Your #{params[:period]} performance summary"
  end
end
```

``app/notifications/milestone_achieved_notification.rb``:

```ruby
class MilestoneAchievedNotification < ApplicationNotification
  deliver_by :email do |config|
    config.mailer = "UserMailer"
    config.method = :milestone_achieved
    config.if = ->(notification) {
      notification.recipient.can_receive_email?(:marketing_emails)
    }
  end
  
  param :milestone_type # :first_job, :five_jobs, :thousand_earned, etc.
  param :milestone_data
  
  def email_category
    :marketing_emails
  end
  
  def message
    case params[:milestone_type]
    when :first_job
      "🎉 You completed your first job!"
    when :five_jobs
      "🎉 5 jobs completed!"
    when :thousand_earned
      "🎉 You've earned $1,000 on Ghostwrote!"
    when :first_hire
      "🎉 You made your first hire!"
    else
      "🎉 Achievement unlocked!"
    end
  end
end
```

**Add email templates**

``app/views/user_mailer/welcome_day1_scout.html.erb``:

```erb
<h2>Welcome to Ghostwrote!</h2>

<p>Hi <%= @user.scout.display_name %>,</p>

<p>We're thrilled to have you join Ghostwrote, where finding talented ghostwriters is simple and efficient.</p>

<h3>Get started in 3 easy steps:</h3>
<ol>
  <li><strong>Complete your profile</strong> - Add your organization details to build trust with writers</li>
  <li><strong>Post your first job</strong> - It takes just 2 minutes to create a job posting</li>
  <li><strong>Review applications</strong> - Connect with talented writers who match your needs</li>
</ol>

<p>
  <%= link_to "Post Your First Job", new_job_url, 
      style: "background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;" %>
</p>

<p>Need help? Our team is here to support you every step of the way.</p>

<p>Best regards,<br>The Ghostwrote Team</p>
```

``app/views/user_mailer/job_alert.html.erb``:

```erb
<% if @frequency == :real_time && @jobs.count == 1 %>
  <h2>New job matching your skills!</h2>
<% else %>
  <h2><%= @jobs.count %> new jobs match your skills</h2>
<% end %>

<p>Hi <%= @user.talent.display_name %>,</p>

<% @jobs.each do |job| %>
  <div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h3><%= link_to job.title, job_url(job), style: "color: #007bff; text-decoration: none;" %></h3>
    <p><%= truncate(job.description, length: 200) %></p>
    <p>
      <strong>Budget:</strong> <%= number_to_currency(job.rate_amount) %> | 
      <strong>Type:</strong> <%= job.job_type.humanize %> |
      <strong>Posted:</strong> <%= time_ago_in_words(job.created_at) %> ago
    </p>
    <p>
      <%= link_to "Apply Now", new_job_application_url(job), 
          style: "background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; display: inline-block;" %>
    </p>
  </div>
<% end %>

<% if @jobs.count > 5 %>
  <p>
    <%= link_to "View All Matching Jobs", jobs_url(skills: @user.talent.skill_ids), 
        style: "background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;" %>
  </p>
<% end %>

<p>Best regards,<br>The Ghostwrote Team</p>
```

**Services**

``app/services/job_matcher_service.rb``:

```ruby
class JobMatcherService
  def initialize(talent)
    @talent = talent
  end
  
  def find_matching_jobs(since: 1.day.ago)
    return Job.none unless @talent.skills.any?
    
    base_query = Job.active
                    .where(created_at: since..)
                    .includes(:scout, :job_skills)
    
    # Skill matching
    skill_matches = base_query.joins(:job_skills)
                             .where(job_skills: { skill_id: @talent.skill_ids })
                             .distinct
    
    # Apply filters and exclude applied jobs
    filtered_jobs = apply_filters(skill_matches)
    exclude_applied_jobs(filtered_jobs)
  end
  
  private
  
  def apply_filters(jobs)
    jobs = filter_by_rate(jobs) if @talent.minimum_rate.present?
    jobs = filter_by_location(jobs) if @talent.location_preference.present?
    jobs = filter_by_type(jobs) if @talent.preferred_job_types.any?
    jobs
  end
  
  def filter_by_rate(jobs)
    jobs.where('rate_amount >= ?', @talent.minimum_rate)
  end
  
  def filter_by_location(jobs)
    if @talent.remote_only?
      jobs.where(remote: true)
    else
      jobs.where('remote = ? OR location = ?', true, @talent.location_preference)
    end
  end
  
  def filter_by_type(jobs)
    jobs.where(job_type: @talent.preferred_job_types)
  end
  
  def exclude_applied_jobs(jobs)
    applied_ids = @talent.applications.pluck(:job_id)
    jobs.where.not(id: applied_ids)
  end
end
```

``app/services/performance_calculator_service.rb``:

```ruby
class PerformanceCalculatorService
  def initialize(user, period = :weekly)
    @user = user
    @period = period
    @start_date = period == :weekly ? 1.week.ago : 1.month.ago
  end
  
  def calculate
    if @user.scout?
      calculate_scout_metrics
    else
      calculate_talent_metrics
    end
  end
  
  private
  
  def calculate_scout_metrics
    scout = @user.scout
    jobs = scout.jobs.where(created_at: @start_date..)
    applications = Application.joins(:job)
                             .where(jobs: { scout_id: scout.id })
                             .where(created_at: @start_date..)
    
    {
      has_activity: jobs.any? || applications.any?,
      jobs_posted: jobs.count,
      applications_received: applications.count,
      average_applications_per_job: jobs.any? ? (applications.count.to_f / jobs.count).round(1) : 0,
      interviews_scheduled: applications.interviewing.count,
      projects_started: applications.accepted.count,
      top_performing_job: jobs.order(applications_count: :desc).first
    }
  end
  
  def calculate_talent_metrics
    talent = @user.talent
    applications = talent.applications.where(created_at: @start_date..)
    projects = talent.projects.where(updated_at: @start_date..)
    
    {
      has_activity: applications.any? || projects.any?,
      applications_sent: applications.count,
      applications_viewed: applications.viewed.count,
      interviews_scheduled: applications.interviewing.count,
      projects_won: applications.accepted.count,
      earnings: calculate_earnings(talent),
      profile_views: talent.profile_views.where(created_at: @start_date..).count,
      response_rate: calculate_response_rate(applications)
    }
  end
  
  def calculate_earnings(talent)
    talent.payments
          .completed
          .where(created_at: @start_date..)
          .sum(:amount)
  end
  
  def calculate_response_rate(applications)
    return 0 unless applications.any?
    responded = applications.where.not(status: 'pending').count
    (responded.to_f / applications.count * 100).round(1)
  end
end
```

**Background Jobs**

``app/jobs/send_welcome_series_job.rb``:

```ruby
class SendWelcomeSeriesJob < ApplicationJob
  queue_as :default
  
  def perform(user_id, step)
    user = User.find(user_id)
    return unless user.can_receive_email?(:marketing_emails)
    
    user_type = user.scout? ? :scout : :talent
    
    WelcomeNotification.with(
      step: step,
      user_type: user_type
    ).deliver(user)
    
    # Schedule next email in series
    case step
    when :day1
      self.class.set(wait: 2.days).perform_later(user_id, :day3)
    when :day3
      self.class.set(wait: 4.days).perform_later(user_id, :day7)
    end
  end
end
```

``app/jobs/send_job_alerts_job.rb``:

```ruby
class SendJobAlertsJob < ApplicationJob
  queue_as :default
  
  def perform(frequency = :real_time)
    talents = Talent.joins(user: :email_preference)
                   .where(users: { email_preferences: { job_alert_frequency: frequency } })
                   .where.not(users: { email_preferences: { job_notifications: false } })
    
    talents.find_each do |talent|
      matcher = JobMatcherService.new(talent)
      
      since = case frequency
              when :real_time then 1.hour.ago
              when :daily then 1.day.ago
              when :weekly then 1.week.ago
              end
      
      matching_jobs = matcher.find_matching_jobs(since: since)
      
      next unless matching_jobs.any?
      
      # Send individual emails for real-time single job, digest for multiple
      if frequency == :real_time && matching_jobs.count == 1
        JobAlertNotification.with(
          jobs: matching_jobs,
          frequency: frequency
        ).deliver(talent.user)
      elsif matching_jobs.count > 0
        JobAlertNotification.with(
          jobs: matching_jobs.limit(10),
          frequency: frequency
        ).deliver(talent.user)
      end
    end
  end
end
```

``app/jobs/send_performance_summaries_job.rb``:

```ruby
class SendPerformanceSummariesJob < ApplicationJob
  queue_as :low
  
  def perform(frequency = :weekly)
    users = User.joins(:email_preference)
                .where(email_preferences: { 
                  performance_summaries: true,
                  summary_frequency: frequency
                })
    
    users.find_each do |user|
      metrics = PerformanceCalculatorService.new(user, frequency).calculate
      
      next unless metrics[:has_activity]
      
      PerformanceSummaryNotification.with(
        metrics: metrics,
        period: frequency,
        user_type: user.scout? ? :scout : :talent
      ).deliver(user)
    end
  end
end
```

``app/jobs/check_profile_completion_job.rb``:

```ruby
class CheckProfileCompletionJob < ApplicationJob
  queue_as :low
  
  def perform
    # Check scouts with incomplete profiles
    Scout.where(created_at: 48.hours.ago..24.hours.ago)
         .incomplete_profile
         .includes(:user)
         .find_each do |scout|
      missing_fields = scout.missing_profile_fields
      
      ProfileIncompleteNotification.with(
        missing_fields: missing_fields,
        user_type: :scout
      ).deliver(scout.user)
    end
    
    # Check talent with incomplete profiles
    Talent.where(created_at: 48.hours.ago..24.hours.ago)
          .incomplete_profile
          .includes(:user)
          .find_each do |talent|
      missing_fields = talent.missing_profile_fields
      
      ProfileIncompleteNotification.with(
        missing_fields: missing_fields,
        user_type: :talent
      ).deliver(talent.user)
    end
  end
end
```

**Rake Tasks**

``lib/tasks/notifications.rake``:

```ruby
namespace :notifications do
  desc "Send real-time job alerts"
  task send_realtime_alerts: :environment do
    SendJobAlertsJob.perform_later(:real_time)
  end
  
  desc "Send daily job alert digests"
  task send_daily_alerts: :environment do
    SendJobAlertsJob.perform_later(:daily)
  end
  
  desc "Send weekly job alert digests"
  task send_weekly_alerts: :environment do
    SendJobAlertsJob.perform_later(:weekly)
  end
  
  desc "Send weekly performance summaries"
  task send_weekly_summaries: :environment do
    SendPerformanceSummariesJob.perform_later(:weekly)
  end
  
  desc "Send monthly performance summaries"
  task send_monthly_summaries: :environment do
    SendPerformanceSummariesJob.perform_later(:monthly)
  end
  
  desc "Check for incomplete profiles"
  task check_incomplete_profiles: :environment do
    CheckProfileCompletionJob.perform_later
  end
end
```

**Trigger welcome series on user creation**

``app/models/user.rb``:

```ruby
class User < ApplicationRecord
  # ... existing code ...
  
  after_create :schedule_welcome_series
  
  private
  
  def schedule_welcome_series
    SendWelcomeSeriesJob.perform_later(id, :day1)
  end
end
```

**Unit Tests**

``spec/services/job_matcher_service_spec.rb``:

```ruby
require 'rails_helper'

RSpec.describe JobMatcherService do
  let(:talent) { create(:talent, skills: [ruby_skill, rails_skill]) }
  let(:ruby_skill) { create(:skill, name: 'Ruby') }
  let(:rails_skill) { create(:skill, name: 'Rails') }
  let(:service) { described_class.new(talent) }
  
  describe '#find_matching_jobs' do
    let!(:matching_job) { create(:job, skills: [ruby_skill]) }
    let!(:non_matching_job) { create(:job, skills: [create(:skill)]) }
    let!(:applied_job) { create(:job, skills: [ruby_skill]) }
    
    before do
      create(:application, talent: talent, job: applied_job)
    end
    
    it 'returns jobs matching talent skills' do
      jobs = service.find_matching_jobs
      expect(jobs).to include(matching_job)
      expect(jobs).not_to include(non_matching_job)
    end
    
    it 'excludes already applied jobs' do
      jobs = service.find_matching_jobs
      expect(jobs).not_to include(applied_job)
    end
    
    it 'respects time range' do
      old_job = create(:job, skills: [ruby_skill], created_at: 2.days.ago)
      jobs = service.find_matching_jobs(since: 1.day.ago)
      expect(jobs).not_to include(old_job)
    end
  end
end
```

``spec/jobs/send_welcome_series_job_spec.rb``:

```ruby
require 'rails_helper'

RSpec.describe SendWelcomeSeriesJob, type: :job do
  let(:user) { create(:user, :scout) }
  
  describe '#perform' do
    it 'sends welcome notification' do
      expect {
        described_class.perform_now(user.id, :day1)
      }.to change { Noticed::Event.count }.by(1)
    end
    
    it 'schedules next email in series' do
      expect {
        described_class.perform_now(user.id, :day1)
      }.to have_enqueued_job(described_class)
        .with(user.id, :day3)
        .at(2.days.from_now)
    end
    
    it 'respects email preferences' do
      user.email_preference.update!(marketing_emails: false)
      
      expect {
        described_class.perform_now(user.id, :day1)
      }.not_to change { ActionMailer::Base.deliveries.count }
    end
  end
end
```

## Phase 3: Retention & Behavioral Notifications

### Affected Files
- ``app/notifications/inactive_user_notification.rb`` - Re-engagement emails
- ``app/notifications/abandoned_job_notification.rb`` - Job posting abandonment
- ``app/notifications/abandoned_application_notification.rb`` - Application abandonment
- ``app/notifications/feature_announcement_notification.rb`` - New features
- ``app/services/behavioral_trigger_service.rb`` - Behavioral detection logic
- ``app/jobs/check_inactive_users_job.rb`` - Inactive user detection
- ``app/jobs/check_abandoned_actions_job.rb`` - Abandonment detection
- ``app/jobs/check_milestones_job.rb`` - Milestone achievement detection

### Code Changes

**Notifications**

``app/notifications/inactive_user_notification.rb``:

```ruby
class InactiveUserNotification < ApplicationNotification
  deliver_by :email do |config|
    config.mailer = "UserMailer"
    config.method = :inactive_user
    config.if = ->(notification) {
      notification.recipient.can_receive_email?(:marketing_emails)
    }
  end
  
  param :days_inactive
  param :user_type # :scout, :talent
  param :suggested_content # jobs or success stories
  
  def email_category
    :marketing_emails
  end
  
  def message
    "We miss you at Ghostwrote! Check out what's new."
  end
end
```

``app/notifications/abandoned_job_notification.rb``:

```ruby
class AbandonedJobNotification < ApplicationNotification
  deliver_by :email do |config|
    config.mailer = "UserMailer"
    config.method = :abandoned_job_posting
    config.if = ->(notification) {
      notification.recipient.can_receive_email?(:job_notifications)
    }
  end
  
  param :job_draft
  
  def email_category
    :job_notifications
  end
  
  def message
    "Complete your job posting in just 2 minutes"
  end
  
  def url
    edit_job_draft_path(params[:job_draft])
  end
end
```

``app/notifications/abandoned_application_notification.rb``:

```ruby
class AbandonedApplicationNotification < ApplicationNotification
  deliver_by :email do |config|
    config.mailer = "UserMailer"
    config.method = :abandoned_application
    config.if = ->(notification) {
      notification.recipient.can_receive_email?(:job_notifications)
    }
  end
  
  param :job
  
  def email_category
    :job_notifications
  end
  
  def message
    "Don't miss out on '#{params[:job].title}'"
  end
  
  def url
    new_job_application_path(params[:job])
  end
end
```

``app/notifications/feature_announcement_notification.rb``:

```ruby
class FeatureAnnouncementNotification < ApplicationNotification
  deliver_by :email do |config|
    config.mailer = "UserMailer"
    config.method = :feature_announcement
    config.if = ->(notification) {
      notification.recipient.can_receive_email?(:marketing_emails)
    }
  end
  
  param :feature_name
  param :feature_description
  param :feature_url
  
  def email_category
    :marketing_emails
  end
  
  def message
    "New on Ghostwrote: #{params[:feature_name]}"
  end
  
  def url
    params[:feature_url]
  end
end
```

**Email Templates**

``app/views/user_mailer/inactive_user.html.erb``:

```erb
<h2>We miss you at Ghostwrote!</h2>

<p>Hi <%= @user.display_name %>,</p>

<p>It's been <%= @days_inactive %> days since your last visit. We wanted to check in and show you what's new on the platform.</p>

<% if @user.scout? %>
  <h3>Top writers are waiting for your next project</h3>
  <p>Post a job today and connect with talented ghostwriters who can bring your ideas to life.</p>
  
  <% if @suggested_content.any? %>
    <h3>Recent success story:</h3>
    <div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
      <p><em>"<%= @suggested_content.first.testimonial %>"</em></p>
      <p>- <%= @suggested_content.first.scout_name %></p>
    </div>
  <% end %>
  
  <p>
    <%= link_to "Post a New Job", new_job_url, 
        style: "background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;" %>
  </p>
<% else %>
  <h3>New opportunities matching your skills</h3>
  
  <% if @suggested_content.any? %>
    <% @suggested_content.first(3).each do |job| %>
      <div style="background: #f5f5f5; padding: 15px; border-radius: 8px; margin: 15px 0;">
        <h4><%= job.title %></h4>
        <p><%= truncate(job.description, length: 150) %></p>
        <p><strong>Budget:</strong> <%= number_to_currency(job.rate_amount) %></p>
      </div>
    <% end %>
  <% end %>
  
  <p>
    <%= link_to "Browse All Jobs", jobs_url, 
        style: "background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;" %>
  </p>
<% end %>

<p>Best regards,<br>The Ghostwrote Team</p>
```

``app/views/user_mailer/abandoned_job_posting.html.erb``:

```erb
<h2>Your job posting is almost ready!</h2>

<p>Hi <%= @scout.display_name %>,</p>

<p>We noticed you started creating a job posting but didn't finish. Your draft has been saved and you can complete it in just 2 minutes.</p>

<div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
  <h3>Your draft: <%= @job_draft.title || "Untitled Job" %></h3>
  <% if @job_draft.description.present? %>
    <p><%= truncate(@job_draft.description, length: 200) %></p>
  <% end %>
  <p><strong>Status:</strong> <%= @job_draft.completion_percentage %>% complete</p>
</div>

<p>
  <%= link_to "Complete Your Job Posting", edit_job_draft_url(@job_draft), 
      style: "background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;" %>
</p>

<h3>Why complete your posting?</h3>
<ul>
  <li>Get applications from qualified writers within hours</li>
  <li>Our talent pool has grown 30% this month</li>
  <li>Jobs posted this week receive 40% more applications</li>
</ul>

<p>Best regards,<br>The Ghostwrote Team</p>
```

**Services**

``app/services/behavioral_trigger_service.rb``:

```ruby
class BehavioralTriggerService
  def check_abandoned_job_postings
    # Find scouts who started but didn't complete job posting
    Job.draft
       .where(updated_at: 2.hours.ago..1.hour.ago)
       .includes(scout: :user)
       .find_each do |draft|
      next if draft.abandonment_notified?
      
      AbandonedJobNotification.with(
        job_draft: draft
      ).deliver(draft.scout.user)
      
      draft.update!(abandonment_notified: true)
    end
  end
  
  def check_abandoned_applications
    # Find talent who viewed job but didn't apply
    JobView.where(created_at: 24.hours.ago..1.hour.ago)
           .where(duration: 30..)  # Spent at least 30 seconds
           .includes(:talent, :job)
           .find_each do |view|
      talent = view.talent
      job = view.job
      
      next if talent.applications.exists?(job: job)
      next if job.expired?
      next if view.abandonment_notified?
      
      AbandonedApplicationNotification.with(
        job: job
      ).deliver(talent.user)
      
      view.update!(abandonment_notified: true)
    end
  end
  
  def check_milestone_achievements
    check_talent_milestones
    check_scout_milestones
  end
  
  private
  
  def check_talent_milestones
    Talent.includes(:user).find_each do |talent|
      # First job completed
      if talent.completed_projects.count == 1 && !talent.milestones_achieved.include?('first_job')
        MilestoneAchievedNotification.with(
          milestone_type: :first_job,
          milestone_data: { project: talent.completed_projects.first }
        ).deliver(talent.user)
        
        talent.milestones_achieved << 'first_job'
        talent.save!
      end
      
      # Earnings milestones
      total_earnings = talent.total_earnings
      if total_earnings >= 1000 && !talent.milestones_achieved.include?('thousand_earned')
        MilestoneAchievedNotification.with(
          milestone_type: :thousand_earned,
          milestone_data: { amount: total_earnings }
        ).deliver(talent.user)
        
        talent.milestones_achieved << 'thousand_earned'
        talent.save!
      end
    end
  end
  
  def check_scout_milestones
    Scout.includes(:user).find_each do |scout|
      # First successful hire
      if scout.completed_projects.count == 1 && !scout.milestones_achieved.include?('first_hire')
        MilestoneAchievedNotification.with(
          milestone_type: :first_hire,
          milestone_data: { project: scout.completed_projects.first }
        ).deliver(scout.user)
        
        scout.milestones_achieved << 'first_hire'
        scout.save!
      end
    end
  end
end
```

**Background Jobs**

``app/jobs/check_inactive_users_job.rb``:

```ruby
class CheckInactiveUsersJob < ApplicationJob
  queue_as :low
  
  def perform
    # Check scouts inactive for 30 days
    User.scout
        .where(last_sign_in_at: 60.days.ago..30.days.ago)
        .where.not(id: recently_notified_users('InactiveUserNotification', 7.days))
        .find_each do |user|
      days_inactive = (Time.current - user.last_sign_in_at) / 1.day
      
      InactiveUserNotification.with(
        days_inactive: days_inactive.round,
        user_type: :scout,
        suggested_content: recent_success_stories
      ).deliver(user)
    end
    
    # Check talent inactive for 14 days
    User.talent
        .where(last_sign_in_at: 30.days.ago..14.days.ago)
        .where.not(id: recently_notified_users('InactiveUserNotification', 7.days))
        .find_each do |user|
      days_inactive = (Time.current - user.last_sign_in_at) / 1.day
      talent = user.talent
      
      InactiveUserNotification.with(
        days_inactive: days_inactive.round,
        user_type: :talent,
        suggested_content: JobMatcherService.new(talent).find_matching_jobs.limit(3)
      ).deliver(user)
    end
  end
  
  private
  
  def recently_notified_users(notification_type, period)
    Noticed::Event.where(
      type: notification_type,
      created_at: period.ago..
    ).joins(:notifications).pluck('noticed_notifications.recipient_id').uniq
  end
  
  def recent_success_stories
    # Placeholder - implement based on your success story model
    []
  end
end
```

``app/jobs/check_abandoned_actions_job.rb``:

```ruby
class CheckAbandonedActionsJob < ApplicationJob
  queue_as :default
  
  def perform
    service = BehavioralTriggerService.new
    
    service.check_abandoned_job_postings
    service.check_abandoned_applications
  end
end
```

``app/jobs/check_milestones_job.rb``:

```ruby
class CheckMilestonesJob < ApplicationJob
  queue_as :low
  
  def perform
    BehavioralTriggerService.new.check_milestone_achievements
  end
end
```

**Add to scheduled tasks**

``lib/tasks/notifications.rake``:

```ruby
namespace :notifications do
  # ... existing tasks ...
  
  desc "Check for inactive users"
  task check_inactive_users: :environment do
    CheckInactiveUsersJob.perform_later
  end
  
  desc "Check for abandoned actions"
  task check_abandoned_actions: :environment do
    CheckAbandonedActionsJob.perform_later
  end
  
  desc "Check for milestone achievements"
  task check_milestones: :environment do
    CheckMilestonesJob.perform_later
  end
end
```

**Schedule with cron (using whenever gem or similar)**

``config/schedule.rb``:

```ruby
# Run every hour
every 1.hour do
  rake "notifications:send_realtime_alerts"
  rake "notifications:check_abandoned_actions"
end

# Run daily at 9am
every 1.day, at: '9:00 am' do
  rake "notifications:send_daily_alerts"
  rake "notifications:check_incomplete_profiles"
  rake "notifications:check_milestones"
end

# Run weekly on Monday at 9am
every :monday, at: '9:00 am' do
  rake "notifications:send_weekly_alerts"
  rake "notifications:send_weekly_summaries"
  rake "notifications:check_inactive_users"
end

# Run monthly on the 1st at 9am
every '0 9 1 * *' do
  rake "notifications:send_monthly_summaries"
end
```

**Unit Tests**

``spec/services/behavioral_trigger_service_spec.rb``:

```ruby
require 'rails_helper'

RSpec.describe BehavioralTriggerService do
  let(:service) { described_class.new }
  
  describe '#check_abandoned_job_postings' do
    let(:scout) { create(:scout) }
    let(:draft) { create(:job, :draft, scout: scout, updated_at: 90.minutes.ago) }
    
    it 'sends notification for abandoned drafts' do
      expect {
        service.check_abandoned_job_postings
      }.to change { Noticed::Event.count }.by(1)
    end
    
    it 'marks draft as notified' do
      service.check_abandoned_job_postings
      expect(draft.reload.abandonment_notified?).to be true
    end
    
    it 'does not send duplicate notifications' do
      draft.update!(abandonment_notified: true)
      
      expect {
        service.check_abandoned_job_postings
      }.not_to change { Noticed::Event.count }
    end
  end
  
  describe '#check_milestone_achievements' do
    let(:talent) { create(:talent) }
    
    context 'when talent completes first job' do
      before do
        create(:project, talent: talent, status: :completed)
      end
      
      it 'sends milestone notification' do
        expect {
          service.check_milestone_achievements
        }.to change { Noticed::Event.count }.by(1)
      end
      
      it 'records achievement' do
        service.check_milestone_achievements
        expect(talent.reload.milestones_achieved).to include('first_job')
      end
    end
  end
end
```

``spec/jobs/check_inactive_users_job_spec.rb``:

```ruby
require 'rails_helper'

RSpec.describe CheckInactiveUsersJob, type: :job do
  describe '#perform' do
    let!(:inactive_scout) { create(:user, :scout, last_sign_in_at: 31.days.ago) }
    let!(:inactive_talent) { create(:user, :talent, last_sign_in_at: 15.days.ago) }
    let!(:active_user) { create(:user, last_sign_in_at: 1.day.ago) }
    
    it 'notifies inactive scouts after 30 days' do
      expect {
        described_class.perform_now
      }.to change { Noticed::Event.where(type: 'InactiveUserNotification').count }.by(1)
    end
    
    it 'notifies inactive talent after 14 days' do
      expect {
        described_class.perform_now
      }.to change { 
        Noticed::Notification.where(recipient: inactive_talent).count 
      }.by(1)
    end
    
    it 'does not notify active users' do
      described_class.perform_now
      expect(
        Noticed::Notification.where(recipient: active_user).count
      ).to eq(0)
    end
  end
end
```