{"generated_at": "2025-07-09T12:50:55Z", "total_routes": 150, "user_types": {"talent": [{"path": "/talent/profile/edit", "name": "edit_talent_profile", "controller": "talent/profiles", "action": "edit", "requirements": {"controller": "talent/profiles", "action": "edit"}, "user_type": "talent", "priority": 12}, {"path": "/talent/jobs/1/job_applications/new", "name": "new_talent_job_job_application", "controller": "talent/job_applications", "action": "new", "requirements": {"controller": "talent/job_applications", "action": "new"}, "user_type": "talent", "priority": 11}, {"path": "/talent", "name": "talent_root", "controller": "talent/jobs", "action": "index", "requirements": {"controller": "talent/jobs", "action": "index"}, "user_type": "talent", "priority": 8}, {"path": "/talent/profile", "name": "talent_profile", "controller": "talent/profiles", "action": "show", "requirements": {"controller": "talent/profiles", "action": "show"}, "user_type": "talent", "priority": 7}, {"path": "/talent/jobs", "name": "talent_jobs", "controller": "talent/jobs", "action": "index", "requirements": {"controller": "talent/jobs", "action": "index"}, "user_type": "talent", "priority": 6}, {"path": "/talent/jobs/1", "name": "talent_job", "controller": "talent/jobs", "action": "show", "requirements": {"controller": "talent/jobs", "action": "show"}, "user_type": "talent", "priority": 6}, {"path": "/talent/conversations/archives/1/edit", "name": "edit_talent_archive", "controller": "talent/conversations/archives", "action": "edit", "requirements": {"controller": "talent/conversations/archives", "action": "edit"}, "user_type": "talent", "priority": 5}, {"path": "/talent/conversations/archives/new", "name": "new_talent_archive", "controller": "talent/conversations/archives", "action": "new", "requirements": {"controller": "talent/conversations/archives", "action": "new"}, "user_type": "talent", "priority": 5}, {"path": "/talent/job_applications/1/edit", "name": "edit_talent_job_application", "controller": "talent/job_applications", "action": "edit", "requirements": {"controller": "talent/job_applications", "action": "edit"}, "user_type": "talent", "priority": 5}, {"path": "/talent/chat_requests", "name": "talent_chat_requests", "controller": "talent/chat_requests", "action": "index", "requirements": {"controller": "talent/chat_requests", "action": "index"}, "user_type": "talent", "priority": 0}, {"path": "/talent/conversations", "name": "talent_conversations", "controller": "talent/conversations", "action": "index", "requirements": {"controller": "talent/conversations", "action": "index"}, "user_type": "talent", "priority": 0}, {"path": "/talent/conversations/1", "name": "talent_conversation", "controller": "talent/conversations", "action": "show", "requirements": {"controller": "talent/conversations", "action": "show"}, "user_type": "talent", "priority": 0}, {"path": "/talent/conversations/archives", "name": "talent_archives", "controller": "talent/conversations/archives", "action": "index", "requirements": {"controller": "talent/conversations/archives", "action": "index"}, "user_type": "talent", "priority": 0}, {"path": "/talent/conversations/archives/1", "name": "talent_archive", "controller": "talent/conversations/archives", "action": "show", "requirements": {"controller": "talent/conversations/archives", "action": "show"}, "user_type": "talent", "priority": 0}, {"path": "/talent/job_applications", "name": "talent_job_applications", "controller": "talent/job_applications", "action": "index", "requirements": {"controller": "talent/job_applications", "action": "index"}, "user_type": "talent", "priority": 0}, {"path": "/talent/job_applications/1", "name": "talent_job_application", "controller": "talent/job_applications", "action": "show", "requirements": {"controller": "talent/job_applications", "action": "show"}, "user_type": "talent", "priority": 0}, {"path": "/talent/messages", "name": "talent_messages", "controller": "talent/messages", "action": "index", "requirements": {"controller": "talent/messages", "action": "index"}, "user_type": "talent", "priority": 0}, {"path": "/talent/settings", "name": "talent_settings", "controller": "talent/settings", "action": "show", "requirements": {"controller": "talent/settings", "action": "show"}, "user_type": "talent", "priority": 0}, {"path": "/talent/settings/passwords", "name": "talent_settings_passwords", "controller": "talent/settings/passwords", "action": "show", "requirements": {"controller": "talent/settings/passwords", "action": "show"}, "user_type": "talent", "priority": 0}, {"path": "/talent/settings/subscription", "name": "talent_settings_subscription", "controller": "talent/settings/subscriptions", "action": "show", "requirements": {"controller": "talent/settings/subscriptions", "action": "show"}, "user_type": "talent", "priority": 0}, {"path": "/talent/subscription/cancel", "name": "cancel_talent_subscription", "controller": "talent/subscriptions", "action": "cancel", "requirements": {"controller": "talent/subscriptions", "action": "cancel"}, "user_type": "talent", "priority": 0}, {"path": "/talent/subscription/success", "name": "success_talent_subscription", "controller": "talent/subscriptions", "action": "success", "requirements": {"controller": "talent/subscriptions", "action": "success"}, "user_type": "talent", "priority": 0}], "scout": [{"path": "/scout/jobs/1/edit", "name": "edit_scout_job", "controller": "scout/jobs", "action": "edit", "requirements": {"controller": "scout/jobs", "action": "edit"}, "user_type": "scout", "priority": 11}, {"path": "/scout/jobs/new", "name": "new_scout_job", "controller": "scout/jobs", "action": "new", "requirements": {"controller": "scout/jobs", "action": "new"}, "user_type": "scout", "priority": 11}, {"path": "/scout", "name": "scout_root", "controller": "scout/jobs", "action": "index", "requirements": {"controller": "scout/jobs", "action": "index"}, "user_type": "scout", "priority": 8}, {"path": "/scout/jobs", "name": "scout_jobs", "controller": "scout/jobs", "action": "index", "requirements": {"controller": "scout/jobs", "action": "index"}, "user_type": "scout", "priority": 6}, {"path": "/scout/jobs/1", "name": "scout_job", "controller": "scout/jobs", "action": "show", "requirements": {"controller": "scout/jobs", "action": "show"}, "user_type": "scout", "priority": 6}, {"path": "/scout/jobs/1/applicants", "name": "scout_job_applicants", "controller": "scout/jobs/applicants", "action": "index", "requirements": {"controller": "scout/jobs/applicants", "action": "index"}, "user_type": "scout", "priority": 6}, {"path": "/scout/jobs/1/duplicate", "name": "duplicate_scout_job", "controller": "scout/jobs", "action": "duplicate", "requirements": {"controller": "scout/jobs", "action": "duplicate"}, "user_type": "scout", "priority": 6}, {"path": "/scout/jobs/1/payment/cancel", "name": "cancel_scout_job_payment", "controller": "scout/job_payments", "action": "cancel", "requirements": {"controller": "scout/job_payments", "action": "cancel"}, "user_type": "scout", "priority": 6}, {"path": "/scout/jobs/1/payment/success", "name": "success_scout_job_payment", "controller": "scout/job_payments", "action": "success", "requirements": {"controller": "scout/job_payments", "action": "success"}, "user_type": "scout", "priority": 6}, {"path": "/scout/jobs/1/preview", "name": "preview_scout_job", "controller": "scout/jobs", "action": "preview", "requirements": {"controller": "scout/jobs", "action": "preview"}, "user_type": "scout", "priority": 6}, {"path": "/scout/applicants/:applicant_id/notes/new", "name": "new_scout_applicant_note", "controller": "scout/applicant_notes", "action": "new", "requirements": {"controller": "scout/applicant_notes", "action": "new"}, "user_type": "scout", "priority": 5}, {"path": "/scout/chat_requests/new", "name": "scout_new_chat_request", "controller": "scout/chat_requests", "action": "new", "requirements": {"controller": "scout/chat_requests", "action": "new"}, "user_type": "scout", "priority": 5}, {"path": "/scout/invitations/new", "name": "new_scout_invitation", "controller": "scout/invitations", "action": "new", "requirements": {"controller": "scout/invitations", "action": "new"}, "user_type": "scout", "priority": 5}, {"path": "/scout/settings/edit", "name": "edit_scout_settings", "controller": "scout/settings", "action": "edit", "requirements": {"controller": "scout/settings", "action": "edit"}, "user_type": "scout", "priority": 5}, {"path": "/scout/talent", "name": "scout_talent_index", "controller": "scout/talent", "action": "index", "requirements": {"controller": "scout/talent", "action": "index"}, "user_type": "scout", "priority": 5}, {"path": "/scout/talent/1", "name": "scout_talent", "controller": "scout/talent", "action": "show", "requirements": {"controller": "scout/talent", "action": "show"}, "user_type": "scout", "priority": 5}, {"path": "/scout/applicants", "name": "scout_applicants", "controller": "scout/applicants", "action": "index", "requirements": {"controller": "scout/applicants", "action": "index"}, "user_type": "scout", "priority": 0}, {"path": "/scout/applicants/1", "name": "scout_applicant", "controller": "scout/applicants", "action": "show", "requirements": {"controller": "scout/applicants", "action": "show"}, "user_type": "scout", "priority": 0}, {"path": "/scout/applicants/1/details", "name": "details_scout_applicant", "controller": "scout/applicants", "action": "details", "requirements": {"controller": "scout/applicants", "action": "details"}, "user_type": "scout", "priority": 0}, {"path": "/scout/applicants/1/stage_change_form", "name": "stage_change_form_scout_applicant", "controller": "scout/applicants", "action": "stage_change_form", "requirements": {"controller": "scout/applicants", "action": "stage_change_form"}, "user_type": "scout", "priority": 0}, {"path": "/scout/applicants/placeholder", "name": "placeholder_scout_applicants", "controller": "scout/applicants", "action": "placeholder", "requirements": {"controller": "scout/applicants", "action": "placeholder"}, "user_type": "scout", "priority": 0}, {"path": "/scout/conversations", "name": "scout_conversations", "controller": "scout/conversations", "action": "index", "requirements": {"controller": "scout/conversations", "action": "index"}, "user_type": "scout", "priority": 0}, {"path": "/scout/conversations/1", "name": "scout_conversation", "controller": "scout/conversations", "action": "show", "requirements": {"controller": "scout/conversations", "action": "show"}, "user_type": "scout", "priority": 0}, {"path": "/scout/conversations/archives", "name": "scout_archives", "controller": "scout/conversations/archives", "action": "index", "requirements": {"controller": "scout/conversations/archives", "action": "index"}, "user_type": "scout", "priority": 0}, {"path": "/scout/conversations/archives/1", "name": "scout_archive", "controller": "scout/conversations/archives", "action": "show", "requirements": {"controller": "scout/conversations/archives", "action": "show"}, "user_type": "scout", "priority": 0}, {"path": "/scout/conversations/modal/:applicant_user_id", "name": "modal_scout_conversations", "controller": "scout/conversations", "action": "show_modal", "requirements": {"controller": "scout/conversations", "action": "show_modal"}, "user_type": "scout", "priority": 0}, {"path": "/scout/messages", "name": "scout_messages", "controller": "scout/messages", "action": "index", "requirements": {"controller": "scout/messages", "action": "index"}, "user_type": "scout", "priority": 0}, {"path": "/scout/settings", "name": "scout_settings", "controller": "scout/settings", "action": "show", "requirements": {"controller": "scout/settings", "action": "show"}, "user_type": "scout", "priority": 0}, {"path": "/scout/settings/account", "name": "scout_settings_account", "controller": "scout/settings/accounts", "action": "show", "requirements": {"controller": "scout/settings/accounts", "action": "show"}, "user_type": "scout", "priority": 0}, {"path": "/scout/settings/organization", "name": "scout_settings_organization", "controller": "scout/settings/organizations", "action": "show", "requirements": {"controller": "scout/settings/organizations", "action": "show"}, "user_type": "scout", "priority": 0}, {"path": "/scout/settings/subscription", "name": "scout_settings_subscription", "controller": "scout/settings/subscriptions", "action": "show", "requirements": {"controller": "scout/settings/subscriptions", "action": "show"}, "user_type": "scout", "priority": 0}, {"path": "/scout/subscription/cancel", "name": "cancel_scout_subscription", "controller": "scout/subscriptions", "action": "cancel", "requirements": {"controller": "scout/subscriptions", "action": "cancel"}, "user_type": "scout", "priority": 0}, {"path": "/scout/subscription/success", "name": "success_scout_subscription", "controller": "scout/subscriptions", "action": "success", "requirements": {"controller": "scout/subscriptions", "action": "success"}, "user_type": "scout", "priority": 0}], "superadmin": [{"path": "/super_admin", "name": "super_admin_root", "controller": "super_admin/dashboard", "action": "index", "requirements": {"controller": "super_admin/dashboard", "action": "index"}, "user_type": "superadmin", "priority": 8}, {"path": "/super_admin/admin_job_invitations/1/edit", "name": "edit_super_admin_admin_job_invitation", "controller": "super_admin/admin_job_invitations", "action": "edit", "requirements": {"controller": "super_admin/admin_job_invitations", "action": "edit"}, "user_type": "superadmin", "priority": 5}, {"path": "/super_admin/admin_jobs/1/edit", "name": "edit_super_admin_admin_job", "controller": "super_admin/admin_jobs", "action": "edit", "requirements": {"controller": "super_admin/admin_jobs", "action": "edit"}, "user_type": "superadmin", "priority": 5}, {"path": "/super_admin/admin_organization_memberships/1/edit", "name": "edit_super_admin_admin_organization_membership", "controller": "super_admin/admin_organization_memberships", "action": "edit", "requirements": {"controller": "super_admin/admin_organization_memberships", "action": "edit"}, "user_type": "superadmin", "priority": 5}, {"path": "/super_admin/admin_organizations/1/edit", "name": "edit_super_admin_admin_organization", "controller": "super_admin/admin_organizations", "action": "edit", "requirements": {"controller": "super_admin/admin_organizations", "action": "edit"}, "user_type": "superadmin", "priority": 5}, {"path": "/super_admin/admin_roles/1/edit", "name": "edit_super_admin_admin_role", "controller": "super_admin/admin_roles", "action": "edit", "requirements": {"controller": "super_admin/admin_roles", "action": "edit"}, "user_type": "superadmin", "priority": 5}, {"path": "/super_admin/admin_roles/new", "name": "new_super_admin_admin_role", "controller": "super_admin/admin_roles", "action": "new", "requirements": {"controller": "super_admin/admin_roles", "action": "new"}, "user_type": "superadmin", "priority": 5}, {"path": "/super_admin/admin_talent_notes/1/edit", "name": "edit_super_admin_admin_talent_note", "controller": "super_admin/admin_talent_notes", "action": "edit", "requirements": {"controller": "super_admin/admin_talent_notes", "action": "edit"}, "user_type": "superadmin", "priority": 5}, {"path": "/super_admin/admin_users/1/edit", "name": "edit_super_admin_admin_user", "controller": "super_admin/admin_users", "action": "edit", "requirements": {"controller": "super_admin/admin_users", "action": "edit"}, "user_type": "superadmin", "priority": 5}, {"path": "/super_admin/badge_types/1/edit", "name": "edit_super_admin_badge_type", "controller": "super_admin/badge_types", "action": "edit", "requirements": {"controller": "super_admin/badge_types", "action": "edit"}, "user_type": "superadmin", "priority": 5}, {"path": "/super_admin/badge_types/new", "name": "new_super_admin_badge_type", "controller": "super_admin/badge_types", "action": "new", "requirements": {"controller": "super_admin/badge_types", "action": "new"}, "user_type": "superadmin", "priority": 5}, {"path": "/super_admin/subscription_plans/1/edit", "name": "edit_super_admin_subscription_plan", "controller": "super_admin/subscription_plans", "action": "edit", "requirements": {"controller": "super_admin/subscription_plans", "action": "edit"}, "user_type": "superadmin", "priority": 5}, {"path": "/super_admin/subscription_plans/new", "name": "new_super_admin_subscription_plan", "controller": "super_admin/subscription_plans", "action": "new", "requirements": {"controller": "super_admin/subscription_plans", "action": "new"}, "user_type": "superadmin", "priority": 5}, {"path": "/super_admin/admin", "name": "super_admin_admin_dashboard", "controller": "super_admin/admin_dashboard", "action": "index", "requirements": {"controller": "super_admin/admin_dashboard", "action": "index"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/admin_audit_logs", "name": "super_admin_admin_audit_logs", "controller": "super_admin/admin_audit_logs", "action": "index", "requirements": {"controller": "super_admin/admin_audit_logs", "action": "index"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/admin_audit_logs/1", "name": "super_admin_admin_audit_log", "controller": "super_admin/admin_audit_logs", "action": "show", "requirements": {"controller": "super_admin/admin_audit_logs", "action": "show"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/admin_chat_requests", "name": "super_admin_admin_chat_requests", "controller": "super_admin/admin_chat_requests", "action": "index", "requirements": {"controller": "super_admin/admin_chat_requests", "action": "index"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/admin_chat_requests/1", "name": "super_admin_admin_chat_request", "controller": "super_admin/admin_chat_requests", "action": "show", "requirements": {"controller": "super_admin/admin_chat_requests", "action": "show"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/admin_conversations", "name": "super_admin_admin_conversations", "controller": "super_admin/admin_conversations", "action": "index", "requirements": {"controller": "super_admin/admin_conversations", "action": "index"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/admin_conversations/1", "name": "super_admin_admin_conversation", "controller": "super_admin/admin_conversations", "action": "show", "requirements": {"controller": "super_admin/admin_conversations", "action": "show"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/admin_files", "name": "super_admin_admin_files", "controller": "super_admin/admin_files", "action": "index", "requirements": {"controller": "super_admin/admin_files", "action": "index"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/admin_files/1", "name": "super_admin_admin_file", "controller": "super_admin/admin_files", "action": "show", "requirements": {"controller": "super_admin/admin_files", "action": "show"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/admin_files/1/download", "name": "download_super_admin_admin_file", "controller": "super_admin/admin_files", "action": "download", "requirements": {"controller": "super_admin/admin_files", "action": "download"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/admin_job_applications", "name": "super_admin_admin_job_applications", "controller": "super_admin/admin_job_applications", "action": "index", "requirements": {"controller": "super_admin/admin_job_applications", "action": "index"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/admin_job_applications/1", "name": "super_admin_admin_job_application", "controller": "super_admin/admin_job_applications", "action": "show", "requirements": {"controller": "super_admin/admin_job_applications", "action": "show"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/admin_job_invitations", "name": "super_admin_admin_job_invitations", "controller": "super_admin/admin_job_invitations", "action": "index", "requirements": {"controller": "super_admin/admin_job_invitations", "action": "index"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/admin_job_invitations/1", "name": "super_admin_admin_job_invitation", "controller": "super_admin/admin_job_invitations", "action": "show", "requirements": {"controller": "super_admin/admin_job_invitations", "action": "show"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/admin_jobs", "name": "super_admin_admin_jobs", "controller": "super_admin/admin_jobs", "action": "index", "requirements": {"controller": "super_admin/admin_jobs", "action": "index"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/admin_jobs/1", "name": "super_admin_admin_job", "controller": "super_admin/admin_jobs", "action": "show", "requirements": {"controller": "super_admin/admin_jobs", "action": "show"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/admin_messages", "name": "super_admin_admin_messages", "controller": "super_admin/admin_messages", "action": "index", "requirements": {"controller": "super_admin/admin_messages", "action": "index"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/admin_messages/1", "name": "super_admin_admin_message", "controller": "super_admin/admin_messages", "action": "show", "requirements": {"controller": "super_admin/admin_messages", "action": "show"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/admin_organization_memberships", "name": "super_admin_admin_organization_memberships", "controller": "super_admin/admin_organization_memberships", "action": "index", "requirements": {"controller": "super_admin/admin_organization_memberships", "action": "index"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/admin_organization_memberships/1", "name": "super_admin_admin_organization_membership", "controller": "super_admin/admin_organization_memberships", "action": "show", "requirements": {"controller": "super_admin/admin_organization_memberships", "action": "show"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/admin_organizations", "name": "super_admin_admin_organizations", "controller": "super_admin/admin_organizations", "action": "index", "requirements": {"controller": "super_admin/admin_organizations", "action": "index"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/admin_organizations/1", "name": "super_admin_admin_organization", "controller": "super_admin/admin_organizations", "action": "show", "requirements": {"controller": "super_admin/admin_organizations", "action": "show"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/admin_roles", "name": "super_admin_admin_roles", "controller": "super_admin/admin_roles", "action": "index", "requirements": {"controller": "super_admin/admin_roles", "action": "index"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/admin_roles/1", "name": "super_admin_admin_role", "controller": "super_admin/admin_roles", "action": "show", "requirements": {"controller": "super_admin/admin_roles", "action": "show"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/admin_saved_jobs", "name": "super_admin_admin_saved_jobs", "controller": "super_admin/admin_saved_jobs", "action": "index", "requirements": {"controller": "super_admin/admin_saved_jobs", "action": "index"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/admin_saved_jobs/1", "name": "super_admin_admin_saved_job", "controller": "super_admin/admin_saved_jobs", "action": "show", "requirements": {"controller": "super_admin/admin_saved_jobs", "action": "show"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/admin_sessions", "name": "super_admin_admin_sessions", "controller": "super_admin/admin_sessions", "action": "index", "requirements": {"controller": "super_admin/admin_sessions", "action": "index"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/admin_sessions/1", "name": "super_admin_admin_session", "controller": "super_admin/admin_sessions", "action": "show", "requirements": {"controller": "super_admin/admin_sessions", "action": "show"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/admin_talent_bookmarks", "name": "super_admin_admin_talent_bookmarks", "controller": "super_admin/admin_talent_bookmarks", "action": "index", "requirements": {"controller": "super_admin/admin_talent_bookmarks", "action": "index"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/admin_talent_bookmarks/1", "name": "super_admin_admin_talent_bookmark", "controller": "super_admin/admin_talent_bookmarks", "action": "show", "requirements": {"controller": "super_admin/admin_talent_bookmarks", "action": "show"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/admin_talent_notes", "name": "super_admin_admin_talent_notes", "controller": "super_admin/admin_talent_notes", "action": "index", "requirements": {"controller": "super_admin/admin_talent_notes", "action": "index"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/admin_talent_notes/1", "name": "super_admin_admin_talent_note", "controller": "super_admin/admin_talent_notes", "action": "show", "requirements": {"controller": "super_admin/admin_talent_notes", "action": "show"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/admin_talent_profiles", "name": "super_admin_admin_talent_profiles", "controller": "super_admin/admin_talent_profiles", "action": "index", "requirements": {"controller": "super_admin/admin_talent_profiles", "action": "index"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/admin_talent_profiles/1", "name": "super_admin_admin_talent_profile", "controller": "super_admin/admin_talent_profiles", "action": "show", "requirements": {"controller": "super_admin/admin_talent_profiles", "action": "show"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/admin_users", "name": "super_admin_admin_users", "controller": "super_admin/admin_users", "action": "index", "requirements": {"controller": "super_admin/admin_users", "action": "index"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/admin_users/1", "name": "super_admin_admin_user", "controller": "super_admin/admin_users", "action": "show", "requirements": {"controller": "super_admin/admin_users", "action": "show"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/badge_analytics", "name": "super_admin_badge_analytics", "controller": "super_admin/badge_analytics", "action": "index", "requirements": {"controller": "super_admin/badge_analytics", "action": "index"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/badge_analytics/distribution", "name": "distribution_super_admin_badge_analytics", "controller": "super_admin/badge_analytics", "action": "distribution", "requirements": {"controller": "super_admin/badge_analytics", "action": "distribution"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/badge_analytics/export", "name": "export_super_admin_badge_analytics", "controller": "super_admin/badge_analytics", "action": "export", "requirements": {"controller": "super_admin/badge_analytics", "action": "export"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/badge_analytics/performance", "name": "performance_super_admin_badge_analytics", "controller": "super_admin/badge_analytics", "action": "performance", "requirements": {"controller": "super_admin/badge_analytics", "action": "performance"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/badge_types", "name": "super_admin_badge_types", "controller": "super_admin/badge_types", "action": "index", "requirements": {"controller": "super_admin/badge_types", "action": "index"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/badge_types/1", "name": "super_admin_badge_type", "controller": "super_admin/badge_types", "action": "show", "requirements": {"controller": "super_admin/badge_types", "action": "show"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/csv_exports", "name": "super_admin_csv_exports", "controller": "super_admin/csv_exports", "action": "index", "requirements": {"controller": "super_admin/csv_exports", "action": "index"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/csv_exports/1", "name": "super_admin_csv_export", "controller": "super_admin/csv_exports", "action": "show", "requirements": {"controller": "super_admin/csv_exports", "action": "show"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/saved_searches", "name": "super_admin_saved_searches", "controller": "super_admin/saved_searches", "action": "index", "requirements": {"controller": "super_admin/saved_searches", "action": "index"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/saved_searches/1", "name": "super_admin_saved_search", "controller": "super_admin/saved_searches", "action": "show", "requirements": {"controller": "super_admin/saved_searches", "action": "show"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/security_alerts", "name": "super_admin_security_alerts", "controller": "super_admin/security_alerts", "action": "index", "requirements": {"controller": "super_admin/security_alerts", "action": "index"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/security_alerts/1", "name": "super_admin_security_alert", "controller": "super_admin/security_alerts", "action": "show", "requirements": {"controller": "super_admin/security_alerts", "action": "show"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/session_activities", "name": "super_admin_session_activities", "controller": "super_admin/session_activities", "action": "index", "requirements": {"controller": "super_admin/session_activities", "action": "index"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/session_activities/1", "name": "super_admin_session_activity", "controller": "super_admin/session_activities", "action": "show", "requirements": {"controller": "super_admin/session_activities", "action": "show"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/subscription_plan_sync", "name": "super_admin_subscription_plan_sync_index", "controller": "super_admin/subscription_plan_sync", "action": "index", "requirements": {"controller": "super_admin/subscription_plan_sync", "action": "index"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/subscription_plan_sync/history", "name": "history_super_admin_subscription_plan_sync_index", "controller": "super_admin/subscription_plan_sync", "action": "history", "requirements": {"controller": "super_admin/subscription_plan_sync", "action": "history"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/subscription_plan_sync/status", "name": "status_super_admin_subscription_plan_sync_index", "controller": "super_admin/subscription_plan_sync", "action": "status", "requirements": {"controller": "super_admin/subscription_plan_sync", "action": "status"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/subscription_plans", "name": "super_admin_subscription_plans", "controller": "super_admin/subscription_plans", "action": "index", "requirements": {"controller": "super_admin/subscription_plans", "action": "index"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/subscription_plans/1", "name": "super_admin_subscription_plan", "controller": "super_admin/subscription_plans", "action": "show", "requirements": {"controller": "super_admin/subscription_plans", "action": "show"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/subscription_plans/discover_products", "name": "discover_products_super_admin_subscription_plans", "controller": "super_admin/subscription_plans", "action": "discover_products", "requirements": {"controller": "super_admin/subscription_plans", "action": "discover_products"}, "user_type": "superadmin", "priority": 0}, {"path": "/super_admin/user_masquerade", "name": "super_admin_user_masquerade_index", "controller": "super_admin/user_masquerade", "action": "index", "requirements": {"controller": "super_admin/user_masquerade", "action": "index"}, "user_type": "superadmin", "priority": 0}], "public": [{"path": "/about", "name": "about", "controller": "home", "action": "about", "requirements": {"controller": "home", "action": "about"}, "user_type": "public", "priority": 8}, {"path": "/launchpad", "name": "launchpad", "controller": "launchpad", "action": "index", "requirements": {"controller": "launchpad", "action": "index"}, "user_type": "public", "priority": 8}, {"path": "/organizations", "name": "organizations", "controller": "organizations", "action": "index", "requirements": {"controller": "organizations", "action": "index"}, "user_type": "public", "priority": 8}, {"path": "/recede_historical_location", "name": "turbo_recede_historical_location", "controller": "turbo/native/navigation", "action": "recede", "requirements": {"controller": "turbo/native/navigation", "action": "recede"}, "user_type": "public", "priority": 8}, {"path": "/refresh_historical_location", "name": "turbo_refresh_historical_location", "controller": "turbo/native/navigation", "action": "refresh", "requirements": {"controller": "turbo/native/navigation", "action": "refresh"}, "user_type": "public", "priority": 8}, {"path": "/resume_historical_location", "name": "turbo_resume_historical_location", "controller": "turbo/native/navigation", "action": "resume", "requirements": {"controller": "turbo/native/navigation", "action": "resume"}, "user_type": "public", "priority": 8}, {"path": "/sessions", "name": "sessions", "controller": "sessions", "action": "index", "requirements": {"controller": "sessions", "action": "index"}, "user_type": "public", "priority": 8}, {"path": "/settings", "name": "settings", "controller": "home", "action": "settings", "requirements": {"controller": "home", "action": "settings"}, "user_type": "public", "priority": 8}, {"path": "/sign_in", "name": "sign_in", "controller": "sessions", "action": "new", "requirements": {"controller": "sessions", "action": "new"}, "user_type": "public", "priority": 8}, {"path": "/sign_up", "name": "sign_up", "controller": "registrations", "action": "new", "requirements": {"controller": "registrations", "action": "new"}, "user_type": "public", "priority": 8}, {"path": "/up", "name": "rails_health_check", "controller": "rails/health", "action": "show", "requirements": {"controller": "rails/health", "action": "show"}, "user_type": "public", "priority": 8}, {"path": "/account/profile/edit", "name": "edit_account_profile", "controller": "account_profiles", "action": "edit", "requirements": {"controller": "account_profiles", "action": "edit"}, "user_type": "public", "priority": 5}, {"path": "/identity/email/edit", "name": "edit_identity_email", "controller": "identity/emails", "action": "edit", "requirements": {"controller": "identity/emails", "action": "edit"}, "user_type": "public", "priority": 5}, {"path": "/identity/password_reset/edit", "name": "edit_identity_password_reset", "controller": "identity/password_resets", "action": "edit", "requirements": {"controller": "identity/password_resets", "action": "edit"}, "user_type": "public", "priority": 5}, {"path": "/identity/password_reset/new", "name": "new_identity_password_reset", "controller": "identity/password_resets", "action": "new", "requirements": {"controller": "identity/password_resets", "action": "new"}, "user_type": "public", "priority": 5}, {"path": "/organizations/1/edit", "name": "edit_organization", "controller": "organizations", "action": "edit", "requirements": {"controller": "organizations", "action": "edit"}, "user_type": "public", "priority": 5}, {"path": "/organizations/new", "name": "new_organization", "controller": "organizations", "action": "new", "requirements": {"controller": "organizations", "action": "new"}, "user_type": "public", "priority": 5}, {"path": "/password/edit", "name": "edit_password", "controller": "passwords", "action": "edit", "requirements": {"controller": "passwords", "action": "edit"}, "user_type": "public", "priority": 5}, {"path": "/", "name": "root", "controller": "launchpad", "action": "index", "requirements": {"controller": "launchpad", "action": "index"}, "user_type": "public", "priority": 0}, {"path": "/", "name": null, "controller": "rails/welcome", "action": "index", "requirements": {"controller": "rails/welcome", "action": "index"}, "user_type": "public", "priority": 0}, {"path": "/identity/email_verification", "name": "identity_email_verification", "controller": "identity/email_verifications", "action": "show", "requirements": {"controller": "identity/email_verifications", "action": "show"}, "user_type": "public", "priority": 0}, {"path": "/onboarding/organization", "name": "onboarding_organization", "controller": "onboarding", "action": "organization", "requirements": {"controller": "onboarding", "action": "organization"}, "user_type": "public", "priority": 0}, {"path": "/onboarding/personal", "name": "onboarding_personal", "controller": "onboarding", "action": "personal", "requirements": {"controller": "onboarding", "action": "personal"}, "user_type": "public", "priority": 0}, {"path": "/organizations/1", "name": "organization", "controller": "organizations", "action": "show", "requirements": {"controller": "organizations", "action": "show"}, "user_type": "public", "priority": 0}, {"path": "/sessions/1", "name": "session", "controller": "sessions", "action": "show", "requirements": {"controller": "sessions", "action": "show"}, "user_type": "public", "priority": 0}]}, "summary": {"talent": 22, "scout": 33, "superadmin": 70, "public": 25}}