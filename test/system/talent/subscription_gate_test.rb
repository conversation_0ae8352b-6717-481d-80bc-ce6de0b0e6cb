# frozen_string_literal: true

require 'application_system_test_case'

class Talent::SubscriptionGateTest < ApplicationSystemTestCase
  def setup
    @user = users(:talent)
    @organization = organizations(:one)
    @job =
      Job.create!(
        title: 'Test Job',
        description: 'Test job description',
        job_category: 'social_media',
        platform: 'linkedin',
        outcome: 'leads',
        social_media_goal_type: 'social_media_leads',
        social_media_understands_risk_acknowledged: true,
        budget_range: 'range_1000_2000',
        work_duration: 'one_time_project',
        status: 'published',
        organization: @organization,
      )
  end

  test 'subscription gate overlay renders correctly on jobs index' do
    capybara_sign_in_as @user
    visit talent_jobs_path

    # Should see the subscription gate overlay
    assert_selector '#subscription-gate', visible: true
    assert_text 'Upgrade to unlock messaging, jobs, and applications'
    assert_text 'Your profile stays visible and can receive chat requests'

    # Should see subscription plans if available
    # Note: May not have plans in test environment
  end

  test 'subscription gate overlay renders correctly on jobs show' do
    capybara_sign_in_as @user
    visit talent_job_path(@job)

    # Should see the subscription gate overlay
    assert_selector '#subscription-gate', visible: true
    assert_text 'Upgrade to unlock messaging, jobs, and applications'

    # Background content should be masked
    assert_text 'Premium job'
  end

  test 'chat requests index redirects to upgrade page' do
    capybara_sign_in_as @user
    visit talent_chat_requests_path

    # Should be redirected to upgrade page
    assert_current_path talent_upgrade_path
    assert_text 'Upgrade to access this feature.'
  end

  test 'upgrade page displays available plans' do
    capybara_sign_in_as @user
    visit talent_upgrade_path

    # Should see the subscription gate overlay
    assert_selector '#subscription-gate', visible: true
    assert_text 'Upgrade to unlock messaging, jobs, and applications'

    # Should see subscription plans if available
    # Note: This depends on the available_plans_for_new_subscriptions helper
    # returning plans in the test environment
  end

  private

  def capybara_sign_in_as(user)
    visit sign_in_path
    fill_in 'Email', with: user.email
    fill_in 'Password', with: 'Secret1*3*5*'
    click_button 'Log in'
  end

  def create_active_subscription_for(user)
    payment_processor = user.set_payment_processor(:stripe)
    payment_processor.subscriptions.create!(
      processor_id: 'sub_test',
      processor_plan: 'price_test',
      status: 'active',
      name: 'Test Subscription',
    )
  end
end
