require 'test_helper'

class SubscriptionHelperTest < ActionView::TestCase
  include SubscriptionHelper

  def setup
    # Clear cache before each test
    Rails.cache.clear

    # Clean up any existing test data
    SubscriptionPlan.where(
      stripe_price_id: %w[
        price_1R9Q55DYYVPVcCCrWQOwsKmT
        price_1R9Q66DYYVPVcCCrnqiXNafF
      ],
    ).delete_all

    # Create test subscription plans
    @standard_plan =
      SubscriptionPlan.create!(
        stripe_price_id: 'price_1R9Q55DYYVPVcCCrWQOwsKmT',
        name: 'Standard',
        description: 'Standard subscription plan with all essential features',
        amount: 9900,
        currency: 'usd',
        billing_interval: 'year',
        billing_interval_count: 1,
        features: [
          'Access to all job listings',
          'Direct messaging with scouts',
          'Profile visibility',
          'Standard support',
        ],
        active: true,
        legacy: false,
      )

    @premium_plan =
      SubscriptionPlan.create!(
        stripe_price_id: 'price_1R9Q66DYYVPVcCCrnqiXNafF',
        name: 'Premium',
        description: 'Premium subscription plan with advanced features',
        amount: 9900,
        currency: 'usd',
        billing_interval: 'month',
        billing_interval_count: 1,
        features: [
          'All Standard features',
          'Premium support',
          'Advanced analytics',
          'Priority listing',
        ],
        active: false,
        legacy: true,
      )
  end

  test 'plan_display_name returns correct names from database' do
    assert_equal 'Standard', plan_display_name('price_1R9Q55DYYVPVcCCrWQOwsKmT')
    assert_equal 'Premium', plan_display_name('price_1R9Q66DYYVPVcCCrnqiXNafF')
    assert_equal 'Unknown Plan', plan_display_name('invalid_plan_id')
  end

  test 'plan_description returns correct descriptions from database' do
    assert_equal 'Standard subscription plan with all essential features',
                 plan_description('price_1R9Q55DYYVPVcCCrWQOwsKmT')
    assert_equal 'Premium subscription plan with advanced features',
                 plan_description('price_1R9Q66DYYVPVcCCrnqiXNafF')
    assert_equal 'Subscription plan', plan_description('invalid_plan_id')
  end

  test 'plan_billing_cycle returns correct billing cycles from database' do
    assert_equal 'Year', plan_billing_cycle('price_1R9Q55DYYVPVcCCrWQOwsKmT')
    assert_equal 'Month', plan_billing_cycle('price_1R9Q66DYYVPVcCCrnqiXNafF')
    assert_equal 'Monthly', plan_billing_cycle('invalid_plan_id')
  end

  test 'plan_features returns correct features' do
    standard_features = plan_features('price_1R9Q55DYYVPVcCCrWQOwsKmT')
    assert_includes standard_features, 'Access to all job listings'
    assert_includes standard_features, 'Direct messaging with scouts'
    assert_includes standard_features, 'Profile visibility'
    assert_includes standard_features, 'Standard support'

    premium_features = plan_features('price_1R9Q66DYYVPVcCCrnqiXNafF')
    assert_includes premium_features, 'All Standard features'
    assert_includes premium_features, 'Premium support'
    assert_includes premium_features, 'Advanced analytics'
    assert_includes premium_features, 'Priority listing'

    assert_equal [], plan_features('invalid_plan_id')
  end

  test 'premium_plan? correctly identifies premium plan' do
    assert_not premium_plan?('price_1R9Q55DYYVPVcCCrWQOwsKmT')
    assert premium_plan?('price_1R9Q66DYYVPVcCCrnqiXNafF')
    assert_not premium_plan?('invalid_plan_id')
  end

  test 'standard_plan? correctly identifies standard plan' do
    assert standard_plan?('price_1R9Q55DYYVPVcCCrWQOwsKmT')
    assert_not standard_plan?('price_1R9Q66DYYVPVcCCrnqiXNafF')
    assert_not standard_plan?('invalid_plan_id')
  end

  test 'plan_available_for_new_subscriptions? only allows standard plan' do
    assert plan_available_for_new_subscriptions?(
             'price_1R9Q55DYYVPVcCCrWQOwsKmT',
           )
    assert_not plan_available_for_new_subscriptions?(
                 'price_1R9Q66DYYVPVcCCrnqiXNafF',
               )
    assert_not plan_available_for_new_subscriptions?('invalid_plan_id')
  end

  test 'available_plans_for_new_subscriptions only includes standard plan' do
    available_plans = available_plans_for_new_subscriptions
    assert_equal 1, available_plans.size
    assert available_plans.key?('price_1R9Q55DYYVPVcCCrWQOwsKmT')
    assert_not available_plans.key?('price_1R9Q66DYYVPVcCCrnqiXNafF')
  end

  test 'subscription_status_badge_classes returns correct classes' do
    active_classes = subscription_status_badge_classes('active')
    assert_includes active_classes, 'bg-stone-800'
    assert_includes active_classes, 'text-white'

    trialing_classes = subscription_status_badge_classes('trialing')
    assert_includes trialing_classes, 'bg-stone-200'
    assert_includes trialing_classes, 'text-stone-800'
  end

  test 'subscription_status_display returns correct display text' do
    assert_equal 'Active', subscription_status_display('active')
    assert_equal 'Trial Period', subscription_status_display('trialing')
    assert_equal 'Past Due', subscription_status_display('past_due')
    assert_equal 'Cancelled', subscription_status_display('canceled')
  end

  test 'subscription_card_classes returns correct styling for different plans' do
    standard_classes =
      subscription_card_classes('price_1R9Q55DYYVPVcCCrWQOwsKmT')
    assert_includes standard_classes, 'border-stone-200'
    assert_includes standard_classes, 'bg-stone-50'

    premium_classes =
      subscription_card_classes('price_1R9Q66DYYVPVcCCrnqiXNafF')
    assert_includes premium_classes, 'border-purple-200'
    assert_includes premium_classes, 'bg-gradient-to-br'

    unknown_classes = subscription_card_classes('invalid_plan_id')
    assert_includes unknown_classes, 'border-stone-200'
    assert_includes unknown_classes, 'bg-white'
  end

  test 'format_billing_date formats dates correctly' do
    test_date = Date.new(2024, 12, 25)
    assert_equal 'December 25, 2024', format_billing_date(test_date)

    test_time = Time.new(2024, 6, 15, 10, 30, 0)
    assert_equal 'June 15, 2024', format_billing_date(test_time)

    assert_equal 'Not available', format_billing_date(nil)
    assert_equal 'Not available', format_billing_date('')
  end

  test 'get_plan_data caches results' do
    # Temporarily enable caching for this test
    original_cache_store = Rails.cache
    Rails.cache = ActiveSupport::Cache::MemoryStore.new

    begin
      # First call should hit database
      plan_data = get_plan_data('price_1R9Q55DYYVPVcCCrWQOwsKmT')
      assert_equal 'Standard', plan_data[:name]

      # Verify data is cached
      cache_key = 'subscription_plan_data_price_1R9Q55DYYVPVcCCrWQOwsKmT'
      cached_data = Rails.cache.read(cache_key)
      assert_not_nil cached_data
      assert_equal 'Standard', cached_data[:name]
    ensure
      # Restore original cache store
      Rails.cache = original_cache_store
    end
  end

  test 'get_plan_data falls back to legacy mappings when plan not in database' do
    # Delete the plan from database
    SubscriptionPlan.where(stripe_price_id: 'price_1R9Q55DYYVPVcCCrWQOwsKmT')
      .delete_all
    Rails.cache.clear

    # Should fall back to legacy mapping
    plan_data = get_plan_data('price_1R9Q55DYYVPVcCCrWQOwsKmT')
    assert_equal 'Standard', plan_data[:name]
    assert_equal 'Annually', plan_data[:billing_cycle]
  end

  test 'invalidate_plan_cache clears cached data' do
    # Temporarily enable caching for this test
    original_cache_store = Rails.cache
    Rails.cache = ActiveSupport::Cache::MemoryStore.new

    begin
      # Cache some data
      get_plan_data('price_1R9Q55DYYVPVcCCrWQOwsKmT')

      # Verify it's cached
      cache_key = 'subscription_plan_data_price_1R9Q55DYYVPVcCCrWQOwsKmT'
      assert_not_nil Rails.cache.read(cache_key)

      # Invalidate cache
      invalidate_plan_cache('price_1R9Q55DYYVPVcCCrWQOwsKmT')

      # Verify it's cleared
      assert_nil Rails.cache.read(cache_key)
    ensure
      # Restore original cache store
      Rails.cache = original_cache_store
    end
  end

  test 'all_known_plans returns both database and legacy plans' do
    all_plans = all_known_plans

    # Should include database plans
    assert all_plans.key?('price_1R9Q55DYYVPVcCCrWQOwsKmT')
    assert all_plans.key?('price_1R9Q66DYYVPVcCCrnqiXNafF')

    # Database plans should be marked as such
    assert_equal 'database',
                 all_plans['price_1R9Q55DYYVPVcCCrWQOwsKmT'][:source]
    assert_equal 'database',
                 all_plans['price_1R9Q66DYYVPVcCCrnqiXNafF'][:source]
  end

  test 'plan_amount returns formatted amount from database' do
    amount = plan_amount('price_1R9Q55DYYVPVcCCrWQOwsKmT')
    assert_equal '$99.0', amount

    amount = plan_amount('price_1R9Q66DYYVPVcCCrnqiXNafF')
    assert_equal '$99.0', amount
  end
end
