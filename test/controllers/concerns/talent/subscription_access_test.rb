# frozen_string_literal: true

require 'test_helper'

class Talent::SubscriptionAccessTest < ActiveSupport::TestCase
  # Create a dummy controller to test the concern
  class DummyController
    include Talent::SubscriptionAccess

    attr_accessor :request

    def initialize
      @request = OpenStruct.new(request_method: 'GET')
    end

    def redirect_to(path, options = {})
      @redirect_path = path
      @redirect_options = options
    end

    def talent_upgrade_path
      '/talent/upgrade'
    end

    # Mock helper_method since we're not inheriting from ActionController::Base
    def self.helper_method(*methods)
      # No-op for testing
    end

    # Override Current.user for testing
    def Current
      OpenStruct.new(user: @test_user)
    end

    attr_accessor :test_user

    attr_reader :redirect_path, :redirect_options
  end

  def setup
    @controller = DummyController.new
    @user = users(:talent)
    @controller.test_user = @user
    Current.user = @user
  end

  def teardown
    Current.user = nil
  end

  test 'talent_subscription_active? returns true for active subscription' do
    # Create a payment processor with active subscription
    payment_processor = @user.set_payment_processor(:stripe)
    subscription =
      payment_processor.subscriptions.create!(
        processor_id: 'sub_test',
        processor_plan: 'price_test',
        status: 'active',
        name: 'Test Subscription',
      )

    # Debug: Check if subscription was created
    assert_equal 1, payment_processor.subscriptions.count
    assert_equal 'active', payment_processor.subscriptions.first.status

    assert @controller.talent_subscription_active?(@user)
  end

  test 'talent_subscription_active? returns false for trialing subscription' do
    # Create a payment processor with trialing subscription
    payment_processor = @user.set_payment_processor(:stripe)
    subscription =
      payment_processor.subscriptions.create!(
        processor_id: 'sub_test',
        processor_plan: 'price_test',
        status: 'trialing',
        name: 'Test Subscription',
      )

    assert_not @controller.talent_subscription_active?(@user)
  end

  test 'talent_subscription_active? returns false for past_due subscription' do
    # Create a payment processor with past_due subscription
    payment_processor = @user.set_payment_processor(:stripe)
    subscription =
      payment_processor.subscriptions.create!(
        processor_id: 'sub_test',
        processor_plan: 'price_test',
        status: 'past_due',
        name: 'Test Subscription',
      )

    assert_not @controller.talent_subscription_active?(@user)
  end

  test 'talent_subscription_active? returns false for canceled subscription' do
    # Create a payment processor with canceled subscription
    payment_processor = @user.set_payment_processor(:stripe)
    subscription =
      payment_processor.subscriptions.create!(
        processor_id: 'sub_test',
        processor_plan: 'price_test',
        status: 'canceled',
        name: 'Test Subscription',
      )

    assert_not @controller.talent_subscription_active?(@user)
  end

  test 'talent_subscription_active? returns false when no payment processor' do
    # User has no payment processor
    assert_not @controller.talent_subscription_active?(@user)
  end

  test 'require_talent_subscription! sets gate flags for GET request without subscription' do
    @controller.request.request_method = 'GET'

    @controller.require_talent_subscription!

    assert @controller.subscription_gate?
    assert @controller.mask_subscription_data?
  end

  test 'require_talent_subscription! sets gate flags for HEAD request without subscription' do
    @controller.request.request_method = 'HEAD'

    @controller.require_talent_subscription!

    assert @controller.subscription_gate?
    assert @controller.mask_subscription_data?
  end

  test 'require_talent_subscription! redirects for POST request without subscription' do
    @controller.request.request_method = 'POST'

    @controller.require_talent_subscription!

    assert_equal '/talent/upgrade', @controller.redirect_path
    assert_equal 'Upgrade to access this feature.',
                 @controller.redirect_options[:notice]
  end

  test 'require_talent_subscription! does nothing when subscription is active' do
    # Create active subscription
    payment_processor = @user.set_payment_processor(:stripe)
    subscription =
      payment_processor.subscriptions.create!(
        processor_id: 'sub_test',
        processor_plan: 'price_test',
        status: 'active',
        name: 'Test Subscription',
      )

    # Stub the subscription check to return true
    @controller.define_singleton_method(:talent_subscription_active?) { true }

    @controller.request.request_method = 'POST'

    @controller.require_talent_subscription!

    assert_nil @controller.redirect_path
    assert_not @controller.subscription_gate?
    assert_not @controller.mask_subscription_data?
  end

  test 'subscription_gate? returns false by default' do
    assert_not @controller.subscription_gate?
  end

  test 'mask_subscription_data? returns false by default' do
    assert_not @controller.mask_subscription_data?
  end
end
