require 'test_helper'

class Talent::ChatRequestsControllerTest < ActionDispatch::IntegrationTest
  setup do
    # Create organizations
    @organization = Organization.create!(name: 'Test Organization')

    # Find or create roles
    @scout_role = Role.find_or_create_by!(name: 'scout')
    @talent_role = Role.find_or_create_by!(name: 'talent')

    # Create scout user
    @scout =
      User.create!(
        email: "scout_#{SecureRandom.hex(8)}@example.com",
        password: 'password123123',
        password_confirmation: 'password123123',
        first_name: 'Scout',
        last_name: 'User',
        verified: true,
        signup_intent: 'scout',
        onboarding_step: 'completed',
      )

    # Create talent user
    @talent =
      User.create!(
        email: "talent_#{SecureRandom.hex(8)}@example.com",
        password: 'password123123',
        password_confirmation: 'password123123',
        first_name: 'Talent',
        last_name: 'User',
        verified: true,
        signup_intent: 'talent',
        talent_signup_completed: true,
        onboarding_step: 'completed',
      )

    # Assign roles
    UserRole.create!(user: @scout, role: @scout_role)
    UserRole.create!(user: @talent, role: @talent_role)

    # Create organization memberships
    [@scout, @talent].each do |user|
      OrganizationMembership.create!(
        user: user,
        organization: @organization,
        org_role: 'member',
      )
      user.update!(last_logged_in_organization_id: @organization.id)
    end

    # Create talent profile for the talent user
    TalentProfile.create!(
      user: @talent,
      bio: 'Test talent bio',
      price_range_min: 50,
      price_range_max: 100,
      availability_status: 'available',
    )

    # Sign in as talent user
    post sign_in_url,
         params: {
           email: @talent.email,
           password: 'password123123',
         }
  end

  def teardown
    ChatRequest.destroy_all
    ConversationParticipant.destroy_all
    Conversation.destroy_all
    TalentProfile.destroy_all
    BadgeAssignment.destroy_all
    ImpersonationLog.destroy_all
    OrganizationMembership.destroy_all
    UserRole.destroy_all
    Organization.destroy_all
    User.destroy_all
  end

  test 'should get index with pending chat requests' do
    # Stub subscription check for this test
    Talent::ChatRequestsController
      .any_instance
      .stubs(:talent_subscription_active?)
      .returns(true)

    # Create a pending chat request
    chat_request =
      @scout.sent_chat_requests.create!(
        talent: @talent,
        pitch: 'Test pitch message',
      )

    get talent_chat_requests_path
    assert_response :success
    assert_includes response.body, @scout.name
    assert_includes response.body, 'Chat Invitations'
    assert_includes response.body, 'Wants to start a conversation with you'
  end

  test 'should accept chat request and create conversation' do
    # Stub subscription check for this test
    Talent::ChatRequestsController
      .any_instance
      .stubs(:talent_subscription_active?)
      .returns(true)

    chat_request = @scout.sent_chat_requests.create!(talent: @talent)

    assert_difference 'Conversation.count', 1 do
      post accept_talent_chat_request_path(chat_request)
    end

    chat_request.reload
    assert_equal 'accepted', chat_request.status
    assert_not_nil chat_request.accepted_at

    # Should redirect to the new conversation
    conversation = Conversation.last
    assert_redirected_to talent_conversation_path(conversation)
    assert_includes flash[:notice], 'Chat request accepted!'
  end

  test 'should decline chat request and redirect to messages with chat requests filter' do
    # Stub subscription check for this test
    Talent::ChatRequestsController
      .any_instance
      .stubs(:talent_subscription_active?)
      .returns(true)

    chat_request = @scout.sent_chat_requests.create!(talent: @talent)

    post decline_talent_chat_request_path(chat_request)

    chat_request.reload
    assert_equal 'declined', chat_request.status
    assert_not_nil chat_request.declined_at

    # Should redirect to messages with chat requests filter
    assert_redirected_to talent_conversations_path(status: 'chat_requests')
    assert_includes flash[:notice], "Chat request from #{@scout.name} declined."
  end

  test 'should not allow access without talent signup completion' do
    # Create user without talent signup completed
    incomplete_user =
      User.create!(
        email: "incomplete_#{SecureRandom.hex(8)}@example.com",
        password: 'password123123',
        password_confirmation: 'password123123',
        first_name: 'Incomplete',
        last_name: 'User',
        verified: true,
        signup_intent: 'talent',
        talent_signup_completed: false,
        onboarding_step: 'completed',
        onboarding_completed: true,
      )

    UserRole.create!(user: incomplete_user, role: @talent_role)
    OrganizationMembership.create!(
      user: incomplete_user,
      organization: @organization,
      org_role: 'member',
    )
    incomplete_user.update!(last_logged_in_organization_id: @organization.id)

    # Sign in as incomplete user
    post sign_in_url,
         params: {
           email: incomplete_user.email,
           password: 'password123123',
         }

    get talent_chat_requests_path
    assert_redirected_to launchpad_path
    assert_includes flash[:alert],
                    'You need to complete Talent signup to access this area.'
  end

  test 'should not allow accepting non-existent chat request' do
    post accept_talent_chat_request_path(999_999)
    assert_response :not_found
  end

  test 'should not allow declining non-existent chat request' do
    post decline_talent_chat_request_path(999_999)
    assert_response :not_found
  end

  test 'should not allow accessing other users chat requests' do
    # Create another talent user
    other_talent =
      User.create!(
        email: "other_talent_#{SecureRandom.hex(8)}@example.com",
        password: 'password123123',
        password_confirmation: 'password123123',
        first_name: 'Other',
        last_name: 'Talent',
        verified: true,
        signup_intent: 'talent',
        talent_signup_completed: true,
        onboarding_step: 'completed',
      )

    UserRole.create!(user: other_talent, role: @talent_role)
    OrganizationMembership.create!(
      user: other_talent,
      organization: @organization,
      org_role: 'member',
    )

    # Create chat request for other talent
    chat_request = @scout.sent_chat_requests.create!(talent: other_talent)

    # Try to accept other user's chat request
    post accept_talent_chat_request_path(chat_request)
    assert_response :not_found
  end

  # Subscription gate tests
  test 'should redirect to upgrade page when accessing index without subscription' do
    get talent_chat_requests_path
    assert_redirected_to talent_upgrade_path
    assert_equal 'Upgrade to access this feature.', flash[:notice]
  end

  test 'should redirect to upgrade page when accepting chat request without subscription' do
    chat_request = @scout.sent_chat_requests.create!(talent: @talent)

    post accept_talent_chat_request_path(chat_request)
    assert_redirected_to talent_upgrade_path
    assert_equal 'Upgrade to access this feature.', flash[:notice]
  end

  test 'should redirect to upgrade page when declining chat request without subscription' do
    chat_request = @scout.sent_chat_requests.create!(talent: @talent)

    post decline_talent_chat_request_path(chat_request)
    assert_redirected_to talent_upgrade_path
    assert_equal 'Upgrade to access this feature.', flash[:notice]
  end

  private

  def create_active_subscription_for(user)
    payment_processor = user.set_payment_processor(:stripe)
    payment_processor.subscriptions.create!(
      processor_id: 'sub_test',
      processor_plan: 'price_test',
      status: 'active',
      name: 'Test Subscription',
    )
  end
end
