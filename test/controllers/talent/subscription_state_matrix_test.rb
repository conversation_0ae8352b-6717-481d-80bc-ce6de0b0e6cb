# frozen_string_literal: true

require 'test_helper'

class Talent::SubscriptionStateMatrixTest < ActionDispatch::IntegrationTest
  def setup
    @user = users(:talent)
    @organization = organizations(:one)
    @job = Job.create!(
      title: 'Test Job',
      description: 'Test job description',
      job_category: 'social_media',
      platform: 'linkedin',
      outcome: 'leads',
      social_media_goal_type: 'social_media_leads',
      social_media_understands_risk_acknowledged: true,
      budget_range: 'range_1000_2000',
      work_duration: 'one_time_project',
      status: 'published',
      organization: @organization
    )
    sign_in_as @user
  end

  test 'active subscription allows full access' do
    create_subscription_with_status(@user, 'active')
    
    # Stub the subscription check to return true for active status
    Talent::JobsController.any_instance.stubs(:talent_subscription_active?).returns(true)
    
    get talent_jobs_path
    assert_response :success
    assert_not assigns(:subscription_gate)
    assert_not assigns(:mask_subscription_data)
    
    # Test mutation works
    post save_talent_job_path(@job)
    assert_not_equal talent_upgrade_path, response.location
  end

  test 'trialing subscription triggers gate' do
    create_subscription_with_status(@user, 'trialing')
    
    get talent_jobs_path
    assert_response :success
    assert assigns(:subscription_gate)
    assert assigns(:mask_subscription_data)
    
    # Test mutation redirects
    post save_talent_job_path(@job)
    assert_redirected_to talent_upgrade_path
    assert_equal 'Upgrade to access this feature.', flash[:notice]
  end

  test 'past_due subscription triggers gate' do
    create_subscription_with_status(@user, 'past_due')
    
    get talent_jobs_path
    assert_response :success
    assert assigns(:subscription_gate)
    assert assigns(:mask_subscription_data)
    
    # Test mutation redirects
    post save_talent_job_path(@job)
    assert_redirected_to talent_upgrade_path
    assert_equal 'Upgrade to access this feature.', flash[:notice]
  end

  test 'canceled subscription triggers gate' do
    create_subscription_with_status(@user, 'canceled')
    
    get talent_jobs_path
    assert_response :success
    assert assigns(:subscription_gate)
    assert assigns(:mask_subscription_data)
    
    # Test mutation redirects
    post save_talent_job_path(@job)
    assert_redirected_to talent_upgrade_path
    assert_equal 'Upgrade to access this feature.', flash[:notice]
  end

  test 'incomplete subscription triggers gate' do
    create_subscription_with_status(@user, 'incomplete')
    
    get talent_jobs_path
    assert_response :success
    assert assigns(:subscription_gate)
    assert assigns(:mask_subscription_data)
    
    # Test mutation redirects
    post save_talent_job_path(@job)
    assert_redirected_to talent_upgrade_path
    assert_equal 'Upgrade to access this feature.', flash[:notice]
  end

  test 'no subscription triggers gate' do
    # User has no subscription at all
    
    get talent_jobs_path
    assert_response :success
    assert assigns(:subscription_gate)
    assert assigns(:mask_subscription_data)
    
    # Test mutation redirects
    post save_talent_job_path(@job)
    assert_redirected_to talent_upgrade_path
    assert_equal 'Upgrade to access this feature.', flash[:notice]
  end

  test 'subscription state matrix for messages controller' do
    # Test active subscription
    create_subscription_with_status(@user, 'active')
    Talent::MessagesController.any_instance.stubs(:talent_subscription_active?).returns(true)
    
    get talent_messages_path
    assert_response :success
    assert_not assigns(:subscription_gate)
    assert_not assigns(:mask_subscription_data)
    
    # Reset stubs
    Talent::MessagesController.any_instance.unstub(:talent_subscription_active?)
    
    # Test trialing subscription
    create_subscription_with_status(@user, 'trialing')
    
    get talent_messages_path
    assert_response :success
    assert assigns(:subscription_gate)
    assert assigns(:mask_subscription_data)
  end

  test 'subscription state matrix for chat requests controller' do
    # Test active subscription
    create_subscription_with_status(@user, 'active')
    Talent::ChatRequestsController.any_instance.stubs(:talent_subscription_active?).returns(true)
    
    get talent_chat_requests_path
    assert_response :success
    assert_not assigns(:subscription_gate)
    
    # Reset stubs
    Talent::ChatRequestsController.any_instance.unstub(:talent_subscription_active?)
    
    # Test trialing subscription (should redirect)
    create_subscription_with_status(@user, 'trialing')
    
    get talent_chat_requests_path
    assert_redirected_to talent_upgrade_path
    assert_equal 'Upgrade to access this feature.', flash[:notice]
  end

  test 'subscription state matrix for conversations controller' do
    # Test active subscription
    create_subscription_with_status(@user, 'active')
    Talent::ConversationsController.any_instance.stubs(:talent_subscription_active?).returns(true)
    
    get talent_conversations_path
    assert_response :success
    assert_not assigns(:subscription_gate)
    assert_not assigns(:mask_subscription_data)
    
    # Reset stubs
    Talent::ConversationsController.any_instance.unstub(:talent_subscription_active?)
    
    # Test canceled subscription
    create_subscription_with_status(@user, 'canceled')
    
    get talent_conversations_path
    assert_response :success
    assert assigns(:subscription_gate)
    assert assigns(:mask_subscription_data)
  end

  test 'subscription state matrix for job applications controller' do
    # Test active subscription
    create_subscription_with_status(@user, 'active')
    Talent::JobApplicationsController.any_instance.stubs(:talent_subscription_active?).returns(true)
    
    get talent_job_applications_path
    assert_response :success
    assert_not assigns(:subscription_gate)
    assert_not assigns(:mask_subscription_data)
    
    # Reset stubs
    Talent::JobApplicationsController.any_instance.unstub(:talent_subscription_active?)
    
    # Test past_due subscription
    create_subscription_with_status(@user, 'past_due')
    
    get talent_job_applications_path
    assert_response :success
    assert assigns(:subscription_gate)
    assert assigns(:mask_subscription_data)
  end

  private

  def create_subscription_with_status(user, status)
    # Clear any existing subscriptions
    user.payment_processor&.subscriptions&.destroy_all
    
    payment_processor = user.set_payment_processor(:stripe)
    payment_processor.subscriptions.create!(
      processor_id: "sub_test_#{status}",
      processor_plan: 'price_test',
      status: status,
      name: 'Test Subscription'
    )
  end
end
