require 'test_helper'

class Talent::SubscriptionsControllerTest < ActionDispatch::IntegrationTest
  self.use_transactional_tests = true

  def setup
    # Create test organization
    @organization = Organization.create!(name: 'Test Organization')

    # Create test user with talent profile
    @user =
      User.create!(
        email: "talent-#{SecureRandom.hex(4)}@example.com",
        password: 'Secret1*3*5*',
        name: 'Test Talent',
        verified: true,
        onboarding_completed: true,
        talent_signup_completed: true,
        last_logged_in_organization_id: @organization.id,
      )

    # Create talent profile
    @talent_profile =
      TalentProfile.create!(user: @user, bio: 'Test bio', is_premium: false)

    # Sign in the user
    sign_in_as(@user)
  end

  test 'should allow standard plan subscription' do
    # Mock Stripe checkout session
    mock_checkout_session =
      OpenStruct.new(url: 'https://checkout.stripe.com/test')

    # Mock the payment processor and checkout method
    mock_processor = OpenStruct.new
    mock_processor.define_singleton_method(:checkout) do |options|
      assert_equal 'subscription', options[:mode]
      assert_equal 'price_1R9Q55DYYVPVcCCrWQOwsKmT',
                   options[:line_items][0][:price]
      mock_checkout_session
    end

    @user.define_singleton_method(:payment_processor) { mock_processor }
    @user.define_singleton_method(:set_payment_processor) { |processor| }

    post talent_subscription_path,
         params: {
           plan: 'price_1R9Q55DYYVPVcCCrWQOwsKmT',
         }

    assert_redirected_to mock_checkout_session.url
  end

  test 'should reject premium plan subscription for new subscriptions' do
    post talent_subscription_path,
         params: {
           plan: 'price_1R9Q66DYYVPVcCCrnqiXNafF',
         }

    assert_redirected_to talent_settings_subscription_path
    assert_equal 'The selected subscription plan is no longer available for new subscriptions.',
                 flash[:alert]
  end

  test 'should reject subscription without plan parameter' do
    post talent_subscription_path

    assert_redirected_to talent_settings_subscription_path
    assert_equal 'Subscription plan not specified.', flash[:alert]
  end

  test 'should reject subscription with invalid plan ID' do
    post talent_subscription_path, params: { plan: 'invalid_plan_id' }

    assert_redirected_to talent_settings_subscription_path
    assert_equal 'The selected subscription plan is no longer available for new subscriptions.',
                 flash[:alert]
  end

  test 'should handle success callback' do
    get success_talent_subscription_path

    assert_redirected_to talent_settings_subscription_path
    assert_equal 'Subscription activated successfully!', flash[:notice]
  end

  test 'should handle cancel callback' do
    get cancel_talent_subscription_path

    assert_redirected_to talent_settings_subscription_path
    assert_equal 'Subscription process cancelled.', flash[:alert]
  end

  test 'should handle Pay::Error during subscription creation' do
    # Mock the payment processor to raise a Pay::Error
    mock_processor = OpenStruct.new
    mock_processor.define_singleton_method(:checkout) do |options|
      raise Pay::Error.new('Test error')
    end

    @user.define_singleton_method(:payment_processor) { mock_processor }
    @user.define_singleton_method(:set_payment_processor) { |processor| }

    post talent_subscription_path,
         params: {
           plan: 'price_1R9Q55DYYVPVcCCrWQOwsKmT',
         }

    assert_redirected_to cancel_talent_subscription_path
    assert_includes flash[:alert], 'Subscription failed: Test error'
  end
end
