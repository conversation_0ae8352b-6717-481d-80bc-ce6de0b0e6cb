# frozen_string_literal: true

require 'test_helper'

class Talent::JobsControllerTest < ActionDispatch::IntegrationTest
  def setup
    @user = users(:talent)
    @organization = organizations(:one)
    @job =
      Job.create!(
        title: 'Test Job',
        description: 'Test job description',
        job_category: 'social_media',
        platform: 'linkedin',
        outcome: 'leads',
        social_media_goal_type: 'social_media_leads',
        social_media_understands_risk_acknowledged: true,
        budget_range: 'range_1000_2000',
        work_duration: 'one_time_project',
        status: 'published',
        organization: @organization,
      )
    sign_in_as @user
  end

  test 'GET index without subscription shows overlay and masks data' do
    get talent_jobs_path

    assert_response :success
    assert assigns(:subscription_gate)
    assert assigns(:mask_subscription_data)
    assert_equal 0, assigns(:all_jobs_count)
    assert_equal 0, assigns(:saved_jobs_count)
    assert_equal [], assigns(:jobs)
  end

  test 'GET show without subscription shows overlay and masks data' do
    get talent_job_path(@job)

    assert_response :success
    assert assigns(:subscription_gate)
    assert assigns(:mask_subscription_data)
    assert_equal 'Premium job', assigns(:job).title
    assert_equal false, assigns(:is_saved)
    assert_equal false, assigns(:has_applied)
  end

  test 'POST save without subscription redirects to upgrade' do
    post save_talent_job_path(@job)

    assert_redirected_to talent_upgrade_path
    assert_equal 'Upgrade to access this feature.', flash[:notice]
  end

  test 'DELETE unsave without subscription redirects to upgrade' do
    delete unsave_talent_job_path(@job)

    assert_redirected_to talent_upgrade_path
    assert_equal 'Upgrade to access this feature.', flash[:notice]
  end

  test 'GET index with active subscription works normally' do
    create_active_subscription_for(@user)

    # Stub the subscription check to return true for this test
    Talent::JobsController
      .any_instance
      .stubs(:talent_subscription_active?)
      .returns(true)

    get talent_jobs_path

    assert_response :success
    assert_not assigns(:subscription_gate)
    assert_not assigns(:mask_subscription_data)

    # Should have real data
    assert assigns(:all_jobs_count) >= 0
    assert assigns(:saved_jobs_count) >= 0
    assert_not_nil assigns(:jobs)
  end

  test 'POST save with active subscription works normally' do
    create_active_subscription_for(@user)

    # Stub the subscription check to return true for this test
    Talent::JobsController
      .any_instance
      .stubs(:talent_subscription_active?)
      .returns(true)

    post save_talent_job_path(@job)

    # Should not redirect to upgrade page
    assert_not_equal talent_upgrade_path, response.location
  end

  private

  def create_active_subscription_for(user)
    payment_processor = user.set_payment_processor(:stripe)
    payment_processor.subscriptions.create!(
      processor_id: 'sub_test',
      processor_plan: 'price_test',
      status: 'active',
      name: 'Test Subscription',
    )
  end
end
