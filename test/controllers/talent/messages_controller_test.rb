# frozen_string_literal: true

require 'test_helper'

class Talent::MessagesControllerTest < ActionDispatch::IntegrationTest
  def setup
    @user = users(:talent)
    @conversation = conversations(:one)
    sign_in_as @user
  end

  test "GET index without subscription shows overlay and masks data" do
    get talent_messages_path
    
    assert_response :success
    assert assigns(:subscription_gate)
    assert assigns(:mask_subscription_data)
    assert_equal [], assigns(:conversations)
    assert_equal [], assigns(:recent_messages)
  end

  test "POST create without subscription redirects to upgrade" do
    post talent_conversation_messages_path(@conversation), params: {
      message: { body: 'Test message' }
    }
    
    assert_redirected_to talent_upgrade_path
    assert_equal 'Upgrade to access this feature.', flash[:notice]
  end

  test "GET index with active subscription works normally" do
    create_active_subscription_for(@user)
    
    get talent_messages_path
    
    assert_response :success
    assert_not assigns(:subscription_gate)
    assert_not assigns(:mask_subscription_data)
    # Should have real data
    assert_not_nil assigns(:conversations)
    assert_not_nil assigns(:recent_messages)
  end

  private

  def create_active_subscription_for(user)
    payment_processor = user.set_payment_processor(:stripe)
    payment_processor.subscriptions.create!(
      processor_id: 'sub_test',
      processor_plan: 'price_test',
      status: 'active',
      name: 'Test Subscription'
    )
  end
end
