require 'test_helper'

class Scout::Settings::OrganizationsControllerTest < ActionDispatch::IntegrationTest
  def setup
    # Clean up any existing data in proper order to avoid foreign key constraints
    BadgeAssignment.destroy_all
    ImpersonationLog.destroy_all
    OrganizationMembership.destroy_all
    Organization.destroy_all
    User.destroy_all

    # Create scout user with organization
    @scout =
      User.create!(
        email: '<EMAIL>',
        password: 'Secret1*3*5*',
        first_name: 'Scout',
        last_name: 'Organizations',
        scout_signup_completed: true,
        verified: true,
        onboarding_completed: true,
        onboarding_step: 'completed',
      )

    @organization =
      Organization.create!(
        name: 'Test Organization',
        bio: 'Test organization bio',
        email: '<EMAIL>',
        address_line_1: '123 Test St',
        city: 'Test City',
        state_province: 'Test State',
        postal_code: '12345',
        country: 'Test Country',
      )

    OrganizationMembership.create!(
      user: @scout,
      organization: @organization,
      org_role: 'admin',
    )

    sign_in_as(@scout)
  end

  def teardown
    BadgeAssignment.destroy_all
    ImpersonationLog.destroy_all
    OrganizationMembership.destroy_all
    Organization.destroy_all
    User.destroy_all
  end

  test 'should get organization settings page' do
    sign_in_as @scout
    get scout_settings_organization_path
    assert_response :success
    assert_select 'div.text-2xl', text: 'Settings'
    assert_select 'h2', text: 'Organization Information'
    assert_select 'input[name="organization[name]"]'
    assert_select 'input[name="organization[email]"]'
    assert_select 'textarea[name="organization[bio]"]'
  end

  test 'should display current organization data' do
    sign_in_as @scout
    get scout_settings_organization_path
    assert_response :success

    assert_select 'input[name="organization[name]"][value="Test Organization"]'
    assert_select 'input[name="organization[email]"][value="<EMAIL>"]'
    assert_select 'textarea[name="organization[bio]"]',
                  text: 'Test organization bio'
    assert_select 'input[name="organization[address_line_1]"][value="123 Test St"]'
    assert_select 'input[name="organization[city]"][value="Test City"]'
    assert_select 'input[name="organization[state_province]"][value="Test State"]'
    assert_select 'input[name="organization[postal_code]"][value="12345"]'
    assert_select 'input[name="organization[country]"][value="Test Country"]'
  end

  test 'should require organization membership' do
    sign_in_as @scout

    # Remove organization membership
    @scout.organization_memberships.destroy_all

    get scout_settings_organization_path

    assert_redirected_to organizations_path
    assert_equal 'Please choose an organization', flash[:alert]
  end

  test 'should display active tab in navigation' do
    sign_in_as @scout
    get scout_settings_organization_path
    assert_response :success

    # Check that Organization tab is active
    assert_select 'nav a[href="' + scout_settings_organization_path +
                    '"]' do |elements|
      # The active tab should have specific styling classes
      assert elements.any? { |el| el['class'].include?('text-stone-900') }
    end
  end

  test 'should include settings form controller for loading states' do
    sign_in_as @scout
    get scout_settings_organization_path

    assert_response :success
    assert_select 'form[data-controller="settings-form"]'
    assert_select 'input[data-settings-form-target="submit"]'
  end

  test 'should include HTML5 validation attributes' do
    sign_in_as @scout
    get scout_settings_organization_path

    assert_response :success
    assert_select 'input[name="organization[name]"][required]'
    assert_select 'input[name="organization[name]"][maxlength="255"]'
    assert_select 'input[name="organization[email]"][maxlength="255"]'
    assert_select 'textarea[name="organization[bio]"][maxlength="1000"]'
  end
end
