require 'test_helper'

class Scout::Settings::AccountsControllerTest < ActionDispatch::IntegrationTest
  def setup
    # Clean up any existing data in proper order to avoid foreign key constraints
    BadgeAssignment.destroy_all
    ImpersonationLog.destroy_all
    OrganizationMembership.destroy_all
    Organization.destroy_all
    User.destroy_all

    # Create scout user with organization
    @scout =
      User.create!(
        email: '<EMAIL>',
        password: 'Secret1*3*5*',
        first_name: 'Scout',
        last_name: 'Accounts',
        scout_signup_completed: true,
        verified: true,
        onboarding_completed: true,
        onboarding_step: 'completed',
      )

    @organization = Organization.create!(name: 'Test Organization')

    OrganizationMembership.create!(
      user: @scout,
      organization: @organization,
      org_role: 'admin',
    )

    sign_in_as(@scout)
  end

  def teardown
    BadgeAssignment.destroy_all
    ImpersonationLog.destroy_all
    OrganizationMembership.destroy_all
    Organization.destroy_all
    User.destroy_all
  end

  test 'should get account settings page' do
    sign_in_as @scout
    get scout_settings_account_path
    assert_response :success
    assert_select 'div.text-2xl', text: 'Settings'
    assert_select 'h2', text: 'Account Management'
    assert_select 'h3', text: 'Password'
    assert_select 'h3', text: 'Em<PERSON> Address'
    assert_select 'h3', text: 'Active Sessions'
    assert_select 'h3', text: 'Security Action'
  end

  test 'should display current user email' do
    sign_in_as @scout
    get scout_settings_account_path
    assert_response :success
    assert_select 'p', text: /scout_accounts_test@example\.com/
  end

  test 'should show logout all sessions button' do
    sign_in_as @scout
    get scout_settings_account_path
    assert_response :success
    assert_includes response.body, 'Logout All Sessions'
  end

  test 'should require organization membership' do
    sign_in_as @scout

    # Remove organization membership
    @scout.organization_memberships.destroy_all

    get scout_settings_account_path

    assert_redirected_to organizations_path
    assert_equal 'Please choose an organization', flash[:alert]
  end

  test 'should display active tab in navigation' do
    get scout_settings_account_path
    assert_response :success

    # Check that Account tab is active
    assert_select 'nav a[href="' + scout_settings_account_path +
                    '"]' do |elements|
      # The active tab should have specific styling classes
      assert elements.any? { |el| el['class'].include?('text-stone-900') }
    end
  end

  test 'should include tabbed interface structure' do
    get scout_settings_account_path
    assert_response :success

    # Check for tab navigation
    assert_select 'nav a[href="' + scout_settings_path + '"]', text: 'General'
    assert_select 'nav a[href="' + scout_settings_account_path + '"]',
                  text: 'Account'
    assert_select 'nav a[href="' + scout_settings_subscription_path + '"]',
                  text: 'Billing'
    assert_select 'nav a[href="' + scout_settings_organization_path + '"]',
                  text: 'Organization'
  end

  test 'should display manage sessions link' do
    sign_in_as @scout
    get scout_settings_account_path
    assert_response :success

    # Should display link to manage sessions
    assert_select 'a[href=?]', sessions_path, text: 'Manage Sessions'
  end

  test 'should handle user without sessions gracefully' do
    sign_in_as @scout

    get scout_settings_account_path
    assert_response :success

    # Page should still render successfully
    assert_select 'h2', text: 'Account Management'
  end
end
