require 'test_helper'

class SubscriptionInterfaceTest < ActionDispatch::IntegrationTest
  self.use_transactional_tests = true

  def setup
    # Create test organization
    @organization = Organization.create!(name: 'Test Organization')

    # Create test user with talent profile
    @user =
      User.create!(
        email: "talent-#{SecureRandom.hex(4)}@example.com",
        password: 'Secret1*3*5*',
        name: 'Test Talent',
        verified: true,
        onboarding_completed: true,
        talent_signup_completed: true,
        last_logged_in_organization_id: @organization.id,
      )

    # Create talent profile
    @talent_profile =
      TalentProfile.create!(user: @user, bio: 'Test bio', is_premium: false)

    # Clean up any existing subscription plans and create fresh ones for testing
    SubscriptionPlan.where(
      stripe_price_id: %w[
        price_1R9Q55DYYVPVcCCrWQOwsKmT
        price_1R9Q66DYYVPVcCCrnqiXNafF
      ],
    ).delete_all

    @standard_plan =
      SubscriptionPlan.create!(
        stripe_price_id: 'price_1R9Q55DYYVPVcCCrWQOwsKmT',
        name: 'Standard',
        description: 'Standard subscription plan with all essential features',
        amount: 9900,
        currency: 'usd',
        billing_interval: 'year',
        billing_interval_count: 1,
        features: [
          'Access to all job listings',
          'Direct messaging with scouts',
          'Profile visibility',
          'Standard support',
        ],
        active: true,
        legacy: false,
      )

    @premium_plan =
      SubscriptionPlan.create!(
        stripe_price_id: 'price_1R9Q66DYYVPVcCCrnqiXNafF',
        name: 'Premium',
        description: 'Premium subscription plan with advanced features',
        amount: 9900,
        currency: 'usd',
        billing_interval: 'month',
        billing_interval_count: 1,
        features: [
          'All Standard features',
          'Premium support',
          'Advanced analytics',
          'Priority listing',
        ],
        active: false,
        legacy: true,
      )
  end

  test 'subscription page shows only standard plan for new subscriptions' do
    sign_in_as(@user)
    get talent_settings_subscription_path

    assert_response :success

    # Should show standard plan
    assert_select 'h3', text: 'Standard'
    assert_select 'button', text: 'Get Started with Standard'

    # Should NOT show premium plan
    assert_select 'h3', text: 'Premium', count: 0
    assert_select 'button', text: 'Choose Premium', count: 0

    # Should show standard plan features
    assert_select 'li', text: /Access to all job listings/
    assert_select 'li', text: /Direct messaging with scouts/
    assert_select 'li', text: /Profile visibility/
    assert_select 'li', text: /Standard support/

    # Should NOT show premium-specific features in the plan selection
    assert_select 'li', text: /Advanced analytics/, count: 0
    assert_select 'li', text: /Priority listing/, count: 0
  end

  test 'subscription page shows current subscription for existing standard subscriber' do
    # Create a standard subscription with Stripe processor
    pay_customer = @user.set_payment_processor(:stripe)
    pay_customer.update!(processor_id: 'cus_test_standard')
    subscription =
      pay_customer.subscriptions.create!(
        name: 'default',
        processor_plan: 'price_1R9Q55DYYVPVcCCrWQOwsKmT',
        processor_id: 'sub_test_standard',
        status: 'active',
      )

    sign_in_as(@user)
    get talent_settings_subscription_path

    assert_response :success

    # Should show current subscription section
    assert_select 'h2', text: 'Current Subscription'
    assert_select 'h3', text: 'Standard'

    # Should show subscription status
    assert_select 'span', text: 'Active'

    # Should NOT show upgrade to premium button
    assert_select 'a', text: /Upgrade to Premium/, count: 0

    # Note: Billing portal button may not be available in test environment due to Stripe API requirements
  end

  test 'subscription page shows current subscription for existing premium subscriber' do
    # Create a premium subscription with Stripe processor
    pay_customer = @user.set_payment_processor(:stripe)
    pay_customer.update!(processor_id: 'cus_test_premium')
    subscription =
      pay_customer.subscriptions.create!(
        name: 'default',
        processor_plan: 'price_1R9Q66DYYVPVcCCrnqiXNafF',
        processor_id: 'sub_test_premium',
        status: 'active',
      )

    # Update talent profile to premium
    @talent_profile.update!(is_premium: true)

    sign_in_as(@user)
    get talent_settings_subscription_path

    assert_response :success

    # Should show current subscription section
    assert_select 'h2', text: 'Current Subscription'
    assert_select 'h3', text: 'Premium'

    # Should show subscription status
    assert_select 'span', text: 'Active'

    # Should show premium features
    assert_select 'li', text: /All Standard features/
    assert_select 'li', text: /Premium support/
    assert_select 'li', text: /Advanced analytics/
    assert_select 'li', text: /Priority listing/

    # Note: Billing portal button may not be available in test environment due to Stripe API requirements
  end

  test 'subscription page handles loading state' do
    sign_in_as(@user)

    # Mock the controller to show loading state
    get talent_settings_subscription_path + '?loading=true'

    assert_response :success
    # The loading state would be tested with JavaScript/system tests
    # This is a basic check that the page renders
  end

  test 'subscription page shows error when billing portal unavailable' do
    # Create a subscription but mock billing portal error
    pay_customer = @user.set_payment_processor(:stripe)
    subscription =
      pay_customer.subscriptions.create!(
        name: 'default',
        processor_plan: 'price_1R9Q55DYYVPVcCCrWQOwsKmT',
        processor_id: 'sub_test_billing_error',
        status: 'active',
      )

    sign_in_as(@user)
    get talent_settings_subscription_path

    assert_response :success

    # Should show current subscription
    assert_select 'h2', text: 'Current Subscription'

    # If billing portal fails, should show error message
    # This would need to be mocked in a more sophisticated way
    # For now, just verify the page renders correctly
  end
end
