# frozen_string_literal: true

require 'test_helper'

class DynamicPricingIntegrationTest < ActionDispatch::IntegrationTest
  setup { Rails.cache.clear }

  test 'subscription helper plan_amount returns valid amount format' do
    # Test the helper method directly
    amount =
      ApplicationController.helpers.plan_amount(
        'price_1R9Q55DYYVPVcCCrWQOwsKmT',
      )

    # Should return either the Stripe amount or fallback amount in correct format
    assert_match(/\$\d+\.\d{2}/, amount)
  end

  test 'subscription helper plan_amount handles invalid price ID' do
    amount = ApplicationController.helpers.plan_amount('invalid_price_id')
    assert_equal 'See billing portal', amount
  end

  test 'subscription helper plan_amount handles nil price ID' do
    amount = ApplicationController.helpers.plan_amount(nil)
    assert_equal 'Unknown Plan', amount
  end

  test 'webhook handlers can be instantiated' do
    # At minimum, verify the classes exist and can be instantiated
    assert_nothing_raised { Pay::Webhooks::Stripe::PriceUpdated.new }
    assert_nothing_raised { Pay::Webhooks::Stripe::ProductUpdated.new }
  end

  private

  # Helper method to create a mock Stripe event
  def create_mock_stripe_event(type, object_data)
    OpenStruct.new(
      type: type,
      data: OpenStruct.new(object: OpenStruct.new(object_data)),
    )
  end
end
