require 'test_helper'

class JobSubmissionTest < ActionDispatch::IntegrationTest
  def setup
    # Clean up any existing data first to avoid conflicts if tests are run multiple times or in a weird order
    # Using destroy_all to ensure callbacks are run, though delete_all is faster if no callbacks needed.
    # Given the FK issues, destroy_all is safer.
    Job.destroy_all
    OrganizationMembership.destroy_all
    User.destroy_all # This should destroy dependent sessions and org_memberships via User model callbacks
    Organization.destroy_all # This should destroy dependent jobs via Organization model callbacks

    # Create a test user with scout privileges
    @user =
      User.create!(
        email: '<EMAIL>',
        password: 'password123123',
        verified: true,
        first_name: 'Test',
        last_name: 'Scout',
        time_zone: 'UTC',
        onboarding_completed: true,
        onboarding_step: 'completed', # Important for redirection tests
        signup_intent: 'scout',
        scout_signup_completed: true,
        talent_signup_completed: false,
      )

    # Create an organization for the scout
    @organization = Organization.create!(name: 'Test Organization')

    # Add scout to organization
    OrganizationMembership.create!(
      user: @user,
      organization: @organization,
      org_role: 'admin',
    )

    @user.update!(last_logged_in_organization_id: @organization.id)
  end

  def teardown
    # Clean up test data
    # Using destroy_all to ensure callbacks (like dependent: :destroy for sessions) are triggered
    # Order might matter if not all dependent: :destroy are perfectly set up.
    # Typically, destroying parent records (User, Organization) should cascade.
    Job.destroy_all

    # OrganizationMembership.destroy_all # Should be handled by User/Org destruction
    # Session.destroy_all # Should be handled by User destruction
    @user.destroy if @user&.persisted? # Explicitly destroy what was created
    @organization.destroy if @organization&.persisted? # Explicitly destroy what was created

    # As a fallback, ensure everything is cleared if specific instance destruction missed something
    # or if tests create other records.
    # User.destroy_all
    # Organization.destroy_all
    # OrganizationMembership.destroy_all # Already covered if User/Org have dependent destroy
  end

  test 'can create job as draft' do
    # Sign in
    post sign_in_url, params: { email: @user.email, password: 'password123123' }
    assert_redirected_to '/launchpad'
    follow_redirect!

    # Submit job form as draft
    assert_difference 'Job.count', 1 do
      post scout_jobs_url,
           params: {
             job: {
               title: 'Test Draft Job',
               description: 'Test Description',
               job_category: 'social_media',
               platform: 'linkedin',
               outcome: 'leads',
             },
             commit: 'Save as Draft',
           }
    end

    # Check that job was created as draft
    job = Job.last
    assert_equal 'draft', job.status
    assert_equal 'Test Draft Job', job.title
    assert_equal @organization.id, job.organization_id
    assert_nil job.published_at

    # Check redirect and notice
    assert_redirected_to scout_jobs_path
    follow_redirect!
    assert_match /successfully saved as draft/, response.body
  end

  test 'can create job as published with complete data' do
    # Sign in
    post sign_in_url, params: { email: @user.email, password: 'password123123' }
    assert_redirected_to '/launchpad'
    follow_redirect!

    # Submit job form as published with all required fields
    assert_difference 'Job.count', 1 do
      post scout_jobs_url,
           params: {
             job: {
               title: 'Test Published Job',
               description: 'Test Description',
               job_category: 'social_media',
               platform: 'linkedin',
               outcome: 'leads',
               social_media_goal_type: 'social_media_leads',
               social_media_understands_risk_acknowledged: '1',
               budget_range: 'range_1000_2000',
               work_duration: 'short_term',
             },
             commit: 'Post Job',
           }
    end

    # Check that job was created as published
    job = Job.last
    assert_equal 'published', job.status
    assert_equal 'Test Published Job', job.title
    assert_equal @organization.id, job.organization_id
    assert_not_nil job.published_at

    # Check redirect and notice
    assert_redirected_to scout_jobs_path
    follow_redirect!
    assert_match /successfully posted and is now live/, response.body
  end

  test 'cannot publish job with incomplete data' do
    # Sign in
    post sign_in_url, params: { email: @user.email, password: 'password123123' }
    assert_redirected_to '/launchpad'
    follow_redirect!

    # Try to submit job form as published with missing required fields
    assert_no_difference 'Job.count' do
      post scout_jobs_url,
           params: {
             job: {
               title: 'Test Incomplete Job',
               description: 'Test Description',
               job_category: 'social_media',
               platform: 'linkedin',
               outcome: 'leads',
               # Missing social_media_goal_type and social_media_understands_risk_acknowledged
             },
             commit: 'Post Job',
           }
    end

    # Check that form is re-rendered with errors
    assert_response :success # Should be unprocessable_entity (422) if validation fails on server
    assert_match /Social media goal type can't be blank/, response.body
  end

  test 'can create newsletter job' do
    # Sign in
    post sign_in_url, params: { email: @user.email, password: 'password123123' }
    assert_redirected_to '/launchpad'
    follow_redirect!

    # Submit newsletter job form as published
    assert_difference 'Job.count', 1 do
      post scout_jobs_url,
           params: {
             job: {
               title: 'Test Newsletter Job',
               description: 'Test Description',
               job_category: 'newsletter',
               outcome: 'grow_email_list',
               newsletter_frequency: 'weekly',
               newsletter_length: 'words_300_600',
               budget_range: 'range_1000_2000',
               work_duration: 'long_term',
             },
             commit: 'Post Job',
           }
    end

    # Check that job was created as published
    job = Job.last
    assert_equal 'published', job.status
    assert_equal 'newsletter', job.job_category
    assert_equal 'weekly', job.newsletter_frequency
    assert_equal 'words_300_600', job.newsletter_length
  end

  test 'can create lead magnet job' do
    # Sign in
    post sign_in_url, params: { email: @user.email, password: 'password123123' }
    assert_redirected_to '/launchpad'
    follow_redirect!

    # Submit lead magnet job form as published
    assert_difference 'Job.count', 1 do
      post scout_jobs_url,
           params: {
             job: {
               title: 'Test Lead Magnet Job',
               description: 'Test Description',
               job_category: 'lead_magnet',
               outcome: 'grow_email_list',
               budget_range: 'range_1000_2000',
               work_duration: 'one_time_project',
               lead_magnet_type: 'ebook', # This was missing and causing the error
             },
             commit: 'Post Job',
           }
    end

    # Check that job was created as published
    job = Job.last
    assert_equal 'published', job.status
    assert_equal 'lead_magnet', job.job_category
    assert_equal 'grow_email_list', job.outcome
    assert_equal 'ebook', job.lead_magnet_type
  end

  test 'can create social media job with leads outcome' do
    # Sign in
    post sign_in_url, params: { email: @user.email, password: 'password123123' }
    assert_redirected_to '/launchpad'
    follow_redirect!

    # Submit social media job form with leads outcome as published
    assert_difference 'Job.count', 1 do
      post scout_jobs_url,
           params: {
             job: {
               title: 'Test Social Media Job',
               description: 'Test Description',
               job_category: 'social_media',
               platform: 'linkedin',
               outcome: 'leads',
               social_media_goal_type: 'social_media_leads', # This was missing and causing the error
               social_media_understands_risk_acknowledged: '1',
               budget_range: 'range_1000_2000',
               work_duration: 'one_time_project',
             },
             commit: 'Post Job',
           }
    end

    # Check that job was created as published
    job = Job.last
    assert_equal 'published', job.status
    assert_equal 'social_media', job.job_category
    assert_equal 'linkedin', job.platform
    assert_equal 'leads', job.outcome
    assert_equal 'social_media_leads', job.social_media_goal_type
    assert job.social_media_understands_risk_acknowledged
  end

  test 'social media job with leads outcome fails without social_media_goal_type' do
    # Sign in
    post sign_in_url, params: { email: @user.email, password: 'password123123' }
    assert_redirected_to '/launchpad'
    follow_redirect!

    # Submit social media job form with leads outcome but missing social_media_goal_type
    assert_no_difference 'Job.count' do
      post scout_jobs_url,
           params: {
             job: {
               title: 'Test Social Media Job',
               description: 'Test Description',
               job_category: 'social_media',
               platform: 'linkedin',
               outcome: 'leads',
               # social_media_goal_type: missing - this should cause validation error
               social_media_understands_risk_acknowledged: '1',
               budget_range: 'range_1000_2000',
               work_duration: 'one_time_project',
             },
             commit: 'Post Job',
           }
    end

    # Should render the form again with errors
    assert_response :unprocessable_entity
  end
end
