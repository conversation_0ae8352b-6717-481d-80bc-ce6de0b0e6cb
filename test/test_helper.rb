ENV['RAILS_ENV'] ||= 'test'
require_relative '../config/environment'
require 'rails/test_help'
require 'mocha/minitest'

class ActiveSupport::TestCase
  # Run tests in parallel with specified workers
  parallelize(workers: :number_of_processors)

  # Setup all fixtures in test/fixtures/*.yml for all tests in alphabetical order.
  fixtures :all

  # Disable Searchkick callbacks during tests to prevent index conflicts
  setup do
    if defined?(Searchkick)
      Searchkick.disable_callbacks

      # Ensure indexes exist for tests that need them
      if respond_to?(:ensure_searchkick_indexes_exist)
        ensure_searchkick_indexes_exist
      end
    end
  end

  teardown { Searchkick.enable_callbacks if defined?(Searchkick) }

  # Add more helper methods to be used by all tests here...
  def sign_in_as(user)
    post(sign_in_url, params: { email: user.email, password: 'Secret1*3*5*' })
    user
  end

  def sign_out(user = nil)
    delete global_sign_out_path
  end
end

# Add role management methods for tests
class User
  def add_role(role_name)
    role = Role.find_or_create_by(name: role_name.to_s)
    user_roles.find_or_create_by(role: role)
  end
end
