# frozen_string_literal: true

require 'test_helper'

class StripePricingServiceTest < ActiveSupport::TestCase
  setup do
    # Clear cache before each test
    Rails.cache.clear
  end

  test 'fetch_price_amount returns fallback for standard plan when Stripe unavailable' do
    # Test fallback behavior by using invalid Stripe configuration
    original_api_key = Stripe.api_key
    Stripe.api_key = nil

    amount =
      StripePricingService.fetch_price_amount('price_1R9Q55DYYVPVcCCrWQOwsKmT')
    assert_equal '$99.00', amount
  ensure
    Stripe.api_key = original_api_key
  end

  test 'fetch_price_amount returns fallback for premium plan when Stripe unavailable' do
    # Test fallback behavior by using invalid Stripe configuration
    original_api_key = Stripe.api_key
    Stripe.api_key = nil

    amount =
      StripePricingService.fetch_price_amount('price_1R9Q66DYYVPVcCCrnqiXNafF')
    assert_equal '$99.00', amount
  ensure
    Stripe.api_key = original_api_key
  end

  test "fetch_price_amount returns 'Unknown Plan' for invalid price ID" do
    amount = StripePricingService.fetch_price_amount('invalid_price_id')
    assert_equal 'See billing portal', amount
  end

  test "fetch_price_amount returns 'Unknown Plan' for nil price ID" do
    amount = StripePricingService.fetch_price_amount(nil)
    assert_equal 'Unknown Plan', amount
  end

  test 'cache_key generates correct key format' do
    key = StripePricingService.send(:cache_key, 'price_123')
    assert_equal 'stripe_price_price_123', key
  end

  test 'invalidate_price_cache calls Rails.cache.delete' do
    price_id = 'price_1R9Q55DYYVPVcCCrWQOwsKmT'

    # Mock Rails.cache.delete to verify it's called
    delete_called = false
    original_delete = Rails.cache.method(:delete)

    Rails
      .cache
      .define_singleton_method(:delete) do |key|
        delete_called = true if key == "stripe_price_#{price_id}"
        original_delete.call(key)
      end

    # Call invalidate
    StripePricingService.invalidate_price_cache(price_id)

    # Verify delete was called
    assert delete_called, 'Rails.cache.delete should be called with correct key'
  ensure
    # Restore original method
    Rails.cache.define_singleton_method(:delete, original_delete)
  end

  test 'supported_price? returns true for known price IDs' do
    assert StripePricingService.supported_price?(
             'price_1R9Q55DYYVPVcCCrWQOwsKmT',
           )
    assert StripePricingService.supported_price?(
             'price_1R9Q66DYYVPVcCCrnqiXNafF',
           )
  end

  test 'supported_price? returns false for unknown price IDs' do
    assert_not StripePricingService.supported_price?('unknown_price_id')
    assert_not StripePricingService.supported_price?(nil)
  end

  test 'format_amount handles USD currency correctly' do
    formatted = StripePricingService.send(:format_amount, 9900, 'usd')
    assert_equal '$99.00', formatted
  end

  test 'format_amount handles other currencies' do
    formatted = StripePricingService.send(:format_amount, 9900, 'eur')
    assert_equal '99.0 EUR', formatted
  end

  test 'format_amount handles invalid input' do
    formatted = StripePricingService.send(:format_amount, 'invalid', 'usd')
    assert_equal 'Invalid amount', formatted
  end
end
