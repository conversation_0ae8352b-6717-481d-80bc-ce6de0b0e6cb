# frozen_string_literal: true

require 'test_helper'

class SubscriptionPlanSyncServiceTest < ActiveSupport::TestCase
  def setup
    @service = SubscriptionPlanSyncService.new
    @stripe_price_id = 'price_test_123'
    @stripe_product_id = 'prod_test_123'

    # Clean up any existing test plans
    SubscriptionPlan.where(stripe_price_id: @stripe_price_id).delete_all

    # Mock Stripe configuration check
    @service.stubs(:stripe_configured?).returns(true)
  end

  def teardown
    # Clean up test data
    SubscriptionPlan.where(stripe_price_id: @stripe_price_id).delete_all
  end

  # Test initialization
  test 'initializes with default logger and generates sync_batch_id' do
    service = SubscriptionPlanSyncService.new
    assert_equal Rails.logger, service.logger
    assert_not_nil service.sync_batch_id
    assert_match(
      /\A[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\z/,
      service.sync_batch_id,
    )
  end

  test 'initializes with custom logger and sync_batch_id' do
    custom_logger = Logger.new(STDOUT)
    custom_batch_id = 'custom-batch-123'
    service =
      SubscriptionPlanSyncService.new(
        logger: custom_logger,
        sync_batch_id: custom_batch_id,
      )

    assert_equal custom_logger, service.logger
    assert_equal custom_batch_id, service.sync_batch_id
  end

  # Test Stripe configuration validation
  test 'sync_plan_from_stripe returns error when Stripe not configured' do
    @service.stubs(:stripe_configured?).returns(false)

    result = @service.sync_plan_from_stripe(@stripe_price_id)

    assert_not result[:success]
    assert_includes result[:errors], 'Stripe is not properly configured'
  end

  # Test individual plan sync with mocked Stripe API
  test 'sync_plan_from_stripe creates new plan when successful' do
    # Mock Stripe API response
    stripe_price = mock_stripe_price(@stripe_price_id, @stripe_product_id)
    Stripe::Price
      .expects(:retrieve)
      .with(@stripe_price_id)
      .returns(stripe_price)

    result = @service.sync_plan_from_stripe(@stripe_price_id)

    puts "DEBUG: Result = #{result.inspect}"
    assert result[:success], "Expected success but got: #{result.inspect}"
    assert_equal :created, result[:action]
    assert_equal @stripe_price_id, result[:stripe_price_id]

    # Verify plan was created in database
    plan = SubscriptionPlan.find_by(stripe_price_id: @stripe_price_id)
    assert_not_nil plan
    assert_equal 'Test Plan', plan.name
    assert_equal 999, plan.amount
  end

  test 'sync_plan_from_stripe handles Stripe API errors gracefully' do
    Stripe::Price
      .expects(:retrieve)
      .with(@stripe_price_id)
      .raises(Stripe::InvalidRequestError.new('Price not found', 'price'))

    result = @service.sync_plan_from_stripe(@stripe_price_id)

    assert_not result[:success]
    assert_includes result[:errors].first, 'Invalid Stripe price ID'
  end

  test 'handles unexpected errors gracefully' do
    Stripe::Price
      .expects(:retrieve)
      .with(@stripe_price_id)
      .raises(StandardError.new('Unexpected error'))

    result = @service.sync_plan_from_stripe(@stripe_price_id)

    assert_not result[:success]
    assert_includes result[:errors].first, 'Unexpected error'
  end

  test 'sync_plan_from_stripe updates existing plan with all fields including features' do
    # Create an existing plan with outdated data
    existing_plan =
      SubscriptionPlan.create!(
        stripe_price_id: @stripe_price_id,
        stripe_product_id: @stripe_product_id,
        name: 'Old Plan Name',
        description: 'Old description',
        amount: 500,
        currency: 'usd',
        billing_interval: 'month',
        billing_interval_count: 1,
        features: ['old feature 1', 'old feature 2'],
        metadata: {
          'old' => 'metadata',
        },
        active: false,
        legacy: false,
      )

    # Mock Stripe API response with updated data including features
    stripe_price =
      mock_stripe_price_with_features(
        @stripe_price_id,
        @stripe_product_id,
        name: 'Updated Plan Name',
        description: 'Updated description',
        price: 999,
        features: ['new feature 1', 'new feature 2', 'new feature 3'],
        active: true,
      )

    Stripe::Price
      .expects(:retrieve)
      .with(@stripe_price_id)
      .returns(stripe_price)

    result = @service.sync_plan_from_stripe(@stripe_price_id)

    assert result[:success], "Expected success but got: #{result.inspect}"
    assert_equal :updated, result[:action]

    # Verify all fields were updated
    updated_plan = SubscriptionPlan.find_by(stripe_price_id: @stripe_price_id)
    assert_not_nil updated_plan
    assert_equal 'Updated Plan Name', updated_plan.name
    assert_equal 'Updated description', updated_plan.description
    assert_equal 999, updated_plan.amount
    assert_equal ['new feature 1', 'new feature 2', 'new feature 3'],
                 updated_plan.features
    assert_equal true, updated_plan.active
    assert_not_nil updated_plan.last_synced_at
  end

  test 'force_sync_features_for_plan updates only specified fields' do
    # Create an existing plan
    existing_plan =
      SubscriptionPlan.create!(
        stripe_price_id: @stripe_price_id,
        stripe_product_id: @stripe_product_id,
        name: 'Old Plan Name',
        description: 'Old description',
        amount: 500,
        currency: 'usd',
        billing_interval: 'month',
        billing_interval_count: 1,
        features: [],
        metadata: {},
        active: false,
        legacy: false,
      )

    # Mock Stripe API response
    stripe_price =
      mock_stripe_price_with_features(
        @stripe_price_id,
        @stripe_product_id,
        features: ['feature 1', 'feature 2'],
      )

    Stripe::Price
      .expects(:retrieve)
      .with(@stripe_price_id)
      .returns(stripe_price)

    result = @service.force_sync_features_for_plan(@stripe_price_id)

    assert result[:success], "Expected success but got: #{result.inspect}"

    # Verify features were updated
    updated_plan = SubscriptionPlan.find_by(stripe_price_id: @stripe_price_id)
    assert_equal ['feature 1', 'feature 2'], updated_plan.features
  end

  test 'debug_plan_sync returns detailed comparison data' do
    # Create an existing plan
    existing_plan =
      SubscriptionPlan.create!(
        stripe_price_id: @stripe_price_id,
        stripe_product_id: @stripe_product_id,
        name: 'Local Plan Name',
        description: 'Local description',
        amount: 500,
        currency: 'usd',
        billing_interval: 'month',
        billing_interval_count: 1,
        features: ['local feature'],
        metadata: {},
        active: false,
        legacy: false,
      )

    # Mock Stripe API response with different data
    stripe_price =
      mock_stripe_price_with_features(
        @stripe_price_id,
        @stripe_product_id,
        name: 'Stripe Plan Name',
        features: ['stripe feature 1', 'stripe feature 2'],
      )

    Stripe::Price
      .expects(:retrieve)
      .with(@stripe_price_id)
      .returns(stripe_price)

    result = @service.debug_plan_sync(@stripe_price_id)

    assert result[:success]
    assert_equal @stripe_price_id, result[:stripe_price_id]
    assert result[:local_plan_exists]

    # Check comparison data
    comparison = result[:comparison]
    assert comparison[:name][:changed]
    assert_equal 'Local Plan Name', comparison[:name][:local]
    assert_equal 'Stripe Plan Name', comparison[:name][:stripe]

    assert comparison[:features][:changed]
    assert_equal ['local feature'], comparison[:features][:local]
    assert_equal ['stripe feature 1', 'stripe feature 2'],
                 comparison[:features][:stripe]
  end

  private

  # Helper method to create mock Stripe price objects
  def mock_stripe_price(
    price_id,
    product_id,
    name: 'Test Plan',
    price: 999,
    currency: 'usd',
    interval: 'month'
  )
    stripe_price =
      OpenStruct.new(
        id: price_id,
        type: 'recurring',
        unit_amount: price,
        currency: currency,
        recurring: OpenStruct.new(interval: interval, interval_count: 1),
        product:
          OpenStruct.new(
            id: product_id,
            name: name,
            description: 'Test plan description',
            metadata: {},
          ),
        metadata: {},
        active: true,
        created: Time.current.to_i,
      )
    stripe_price
  end

  # Helper method to create mock Stripe price objects with features
  def mock_stripe_price_with_features(
    price_id,
    product_id,
    name: 'Test Plan',
    description: 'Test plan description',
    price: 999,
    currency: 'usd',
    interval: 'month',
    features: [],
    active: true
  )
    # Convert features array to JSON string for metadata
    features_json = features.any? ? features.to_json : nil

    stripe_price =
      OpenStruct.new(
        id: price_id,
        type: 'recurring',
        unit_amount: price,
        currency: currency,
        recurring: OpenStruct.new(interval: interval, interval_count: 1),
        product:
          OpenStruct.new(
            id: product_id,
            name: name,
            description: description,
            metadata: features_json ? { 'features' => features_json } : {},
          ),
        metadata: features_json ? { 'features' => features_json } : {},
        active: active,
        created: Time.current.to_i,
      )
    stripe_price
  end
end
