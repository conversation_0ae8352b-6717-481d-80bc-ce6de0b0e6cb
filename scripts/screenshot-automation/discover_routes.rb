#!/usr/bin/env ruby

require 'json'
require 'pathname'

# Load Rails environment
ENV['RAILS_ENV'] ||= 'development'

# Change to Rails root directory
rails_root = File.expand_path('../../../', __FILE__)
Dir.chdir(rails_root)

require_relative '../../config/environment'

class RouteDiscovery
  def initialize
    @routes = []
    @excluded_patterns = [
      /^\/rails\//,
      /^\/cable$/,
      /^\/assets\//,
      /^\/packs\//,
      /^\/api\//,
      /\/:id\/edit$/,
      /\/:id$/,
      /\/new$/,
      /\.(json|xml|js)$/,
      /^\/admin\/mission_control/,
      /^\/admin\/flipper/
    ]
    @user_types = {
      'talent' => [],
      'scout' => [],
      'superadmin' => [],
      'public' => []
    }
  end

  def discover
    puts "🔍 Discovering routes..."
    
    Rails.application.routes.routes.each do |route|
      next unless route.verb.match?(/GET/)
      
      path = route.path.spec.to_s
      next if excluded_route?(path)
      
      # Clean up the path
      clean_path = clean_route_path(path)
      next if clean_path.nil?
      
      # Categorize by user type
      user_type = categorize_route(clean_path)
      
      route_info = {
        path: clean_path,
        name: route.name,
        controller: route.defaults[:controller],
        action: route.defaults[:action],
        requirements: route.requirements.except(:request_method),
        user_type: user_type,
        priority: calculate_priority(clean_path, user_type)
      }
      
      @user_types[user_type] << route_info
      @routes << route_info
    end
    
    # Sort routes by priority within each user type
    @user_types.each do |type, routes|
      @user_types[type] = routes.sort_by { |r| [-r[:priority], r[:path]] }
    end
    
    puts "📊 Route discovery complete:"
    @user_types.each do |type, routes|
      puts "  #{type.capitalize}: #{routes.length} routes"
    end
    
    self
  end

  def save_to_file(filename = 'routes.json')
    output_path = Rails.root.join('scripts', 'screenshot-automation', filename)
    
    output_data = {
      generated_at: Time.current.iso8601,
      total_routes: @routes.length,
      user_types: @user_types,
      summary: {
        talent: @user_types['talent'].length,
        scout: @user_types['scout'].length,
        superadmin: @user_types['superadmin'].length,
        public: @user_types['public'].length
      }
    }
    
    File.write(output_path, JSON.pretty_generate(output_data))
    puts "💾 Routes saved to: #{output_path}"
    puts "📈 Total routes discovered: #{@routes.length}"
    
    output_path
  end

  private

  def excluded_route?(path)
    @excluded_patterns.any? { |pattern| path.match?(pattern) }
  end

  def clean_route_path(path)
    # Remove format constraints
    clean_path = path.gsub(/\(\.:format\)$/, '')
    
    # Skip routes with complex parameters for now
    return nil if clean_path.include?('*') || clean_path.count(':') > 1
    
    # Replace single ID parameters with sample values
    clean_path = clean_path.gsub(/:id/, '1')
    clean_path = clean_path.gsub(/:user_id/, '1')
    clean_path = clean_path.gsub(/:organization_id/, '1')
    clean_path = clean_path.gsub(/:job_id/, '1')
    clean_path = clean_path.gsub(/:conversation_id/, '1')
    
    clean_path
  end

  def categorize_route(path)
    case path
    when /^\/talent/
      'talent'
    when /^\/scout/
      'scout'
    when /^\/super_admin/
      'superadmin'
    when /^\/sign_in/, /^\/sign_up/, /^\/sign_out/, /^\/$/
      'public'
    else
      # Default categorization based on common patterns
      if path.include?('/admin')
        'superadmin'
      elsif path.include?('/dashboard')
        'scout' # Most dashboards are scout-facing
      else
        'public'
      end
    end
  end

  def calculate_priority(path, user_type)
    priority = 0
    
    # Higher priority for main pages
    priority += 10 if path.match?(/\/(dashboard|home|index)$/)
    priority += 8 if path.match?(/^\/[^\/]+$/) # Root level paths
    priority += 5 if path.match?(/\/(show|edit|new)$/)
    
    # User type specific priorities
    case user_type
    when 'talent'
      priority += 7 if path.include?('/profile')
      priority += 6 if path.include?('/jobs')
    when 'scout'
      priority += 7 if path.include?('/dashboard')
      priority += 6 if path.include?('/jobs')
      priority += 5 if path.include?('/talent')
    when 'superadmin'
      priority += 8 if path.include?('/dashboard')
      priority += 6 if path.include?('/users')
    end
    
    priority
  end
end

# Run the discovery if this script is executed directly
if __FILE__ == $0
  discovery = RouteDiscovery.new
  discovery.discover
  discovery.save_to_file
  
  puts "\n✅ Route discovery completed successfully!"
  puts "Next step: Run screenshot capture with Playwright MCP"
end
