#!/usr/bin/env ruby

require 'json'
require 'fileutils'

# Add titleize method for strings
class String
  def titleize
    self.split(/[\s_-]+/).map(&:capitalize).join(' ')
  end
end

class DocumentationGenerator
  def initialize
    @routes_file = 'routes.json'
    @routes_data = JSON.parse(File.read(@routes_file))
    @user_types = ['talent', 'scout', 'superadmin']
  end

  def generate_all_documentation
    puts "Generating updated documentation with route trees..."
    
    @user_types.each do |user_type|
      generate_user_documentation(user_type)
    end
    
    generate_summary_documentation
    puts "Documentation generation complete!"
  end

  def generate_user_documentation(user_type)
    routes = @routes_data['user_types'][user_type] || []
    
    # Group routes by controller for better organization
    grouped_routes = group_routes_by_controller(routes)
    
    content = []
    content << "# #{user_type.capitalize} Routes Screenshots"
    content << ""
    content << "Generated on: #{Time.now.strftime('%Y-%m-%d %H:%M:%S')}"
    content << ""
    content << "## Overview"
    content << ""
    content << "This document contains screenshots of all #{user_type}-facing pages in the Ghostwrote application. These screenshots provide a comprehensive view of the #{user_type} user experience and interface design."
    content << ""
    content << "## Route Tree Structure"
    content << ""
    
    # Generate route tree
    content.concat(generate_route_tree(grouped_routes, user_type))
    content << ""
    content << "---"
    content << ""
    content << "## Detailed Screenshots"
    content << ""
    
    # Generate detailed sections
    grouped_routes.each do |controller_group, controller_routes|
      content.concat(generate_controller_section(controller_group, controller_routes, user_type))
    end
    
    # Write to file
    filename = "#{user_type}-routes-screenshots.md"
    File.write(filename, content.join("\n"))
    puts "Generated: #{filename}"
  end

  private

  def group_routes_by_controller(routes)
    # Group by controller and organize by logical sections
    grouped = {}
    
    routes.each do |route|
      controller = route['controller']
      
      # Create logical groupings
      group_name = determine_controller_group(controller)
      grouped[group_name] ||= []
      grouped[group_name] << route
    end
    
    # Sort routes within each group by action priority
    grouped.each do |group, group_routes|
      grouped[group] = group_routes.sort_by do |route|
        action_priority(route['action'])
      end
    end
    
    # Return groups in logical order
    sort_controller_groups(grouped)
  end

  def determine_controller_group(controller)
    case controller
    when /^talent\/jobs/
      "Job Management"
    when /^talent\/job_applications/
      "Job Applications"
    when /^talent\/conversations/
      "Conversations & Messaging"
    when /^talent\/profiles/
      "Profile Management"
    when /^talent\/settings/
      "Settings & Account"
    when /^talent\/subscriptions?/
      "Subscription Management"
    when /^talent\/chat_requests/
      "Chat Requests"
    when /^talent\/messages/
      "Direct Messages"
    when /^scout\/jobs/
      "Job Management"
    when /^scout\/talent/
      "Talent Discovery"
    when /^scout\/applicants/
      "Applicant Management"
    when /^scout\/conversations/
      "Conversations & Messaging"
    when /^scout\/settings/
      "Settings & Account"
    when /^scout\/subscriptions?/
      "Subscription Management"
    when /^scout\/chat_requests/
      "Chat Requests"
    when /^scout\/messages/
      "Direct Messages"
    when /^scout\/invitations/
      "Invitations"
    when /^super_admin\/dashboard/
      "Dashboard"
    when /^super_admin\/admin_dashboard/
      "Admin Interface"
    when /^super_admin\/admin_users/
      "User Management"
    when /^super_admin\/admin_organizations/
      "Organization Management"
    when /^super_admin\/admin_jobs/
      "Job Administration"
    when /^super_admin\/admin_job_applications/
      "Application Administration"
    when /^super_admin\/badge/
      "Badge System"
    when /^super_admin\/subscription_plan/
      "Subscription Management"
    when /^super_admin\/admin_audit/
      "Audit & Security"
    when /^super_admin\/admin_sessions/
      "Session Management"
    when /^super_admin\/csv_exports/
      "Data Export"
    when /^super_admin\/user_masquerade/
      "User Impersonation"
    else
      controller.split('/').map(&:titleize).join(' / ')
    end
  end

  def action_priority(action)
    case action
    when 'index' then 1
    when 'show' then 2
    when 'new' then 3
    when 'edit' then 4
    when 'create' then 5
    when 'update' then 6
    when 'destroy' then 7
    else 8
    end
  end

  def sort_controller_groups(grouped)
    # Define the order of groups for each user type
    ordered_groups = {
      'talent' => [
        "Profile Management",
        "Job Management", 
        "Job Applications",
        "Conversations & Messaging",
        "Direct Messages",
        "Chat Requests",
        "Settings & Account",
        "Subscription Management"
      ],
      'scout' => [
        "Job Management",
        "Talent Discovery", 
        "Applicant Management",
        "Conversations & Messaging",
        "Direct Messages",
        "Chat Requests",
        "Invitations",
        "Settings & Account",
        "Subscription Management"
      ],
      'superadmin' => [
        "Dashboard",
        "Admin Interface",
        "User Management",
        "Organization Management", 
        "Job Administration",
        "Application Administration",
        "Badge System",
        "Subscription Management",
        "Audit & Security",
        "Session Management",
        "Data Export",
        "User Impersonation"
      ]
    }
    
    result = {}
    
    # Add groups in preferred order
    current_user_type = determine_user_type_from_routes(grouped)
    preferred_order = ordered_groups[current_user_type] || []
    
    preferred_order.each do |group_name|
      if grouped[group_name]
        result[group_name] = grouped[group_name]
      end
    end
    
    # Add any remaining groups
    grouped.each do |group_name, routes|
      unless result[group_name]
        result[group_name] = routes
      end
    end
    
    result
  end

  def determine_user_type_from_routes(grouped)
    # Determine user type based on controller patterns
    sample_controller = grouped.values.first&.first&.dig('controller')
    return 'talent' if sample_controller&.start_with?('talent/')
    return 'scout' if sample_controller&.start_with?('scout/')
    return 'superadmin' if sample_controller&.start_with?('super_admin/')
    'unknown'
  end

  def generate_route_tree(grouped_routes, user_type)
    content = []
    
    grouped_routes.each do |group_name, routes|
      content << "### #{group_name}"
      content << ""
      
      routes.each do |route|
        action_name = route['action'].titleize
        path = route['path']
        
        # Show standard CRUD actions with clear indicators
        crud_indicator = case route['action']
        when 'index' then " (List/Index)"
        when 'show' then " (Show/Detail)"
        when 'new' then " (New/Create Form)"
        when 'edit' then " (Edit Form)"
        else ""
        end
        
        content << "- **#{action_name}#{crud_indicator}** - `#{path}`"
      end
      content << ""
    end
    
    content
  end

  def generate_controller_section(group_name, routes, user_type)
    content = []
    content << "### #{group_name}"
    content << ""
    
    routes.each do |route|
      action_name = route['action'].titleize
      path = route['path']
      controller = route['controller']
      
      content << "#### #{action_name} - `#{path}`"
      content << ""
      content << "**Controller:** `#{controller}`  "
      content << "**Action:** `#{route['action']}`  "
      content << "**HTTP Method:** `GET`  "
      content << "**Route Name:** `#{route['name']}`" if route['name']
      content << ""
      
      # Generate screenshot filename
      screenshot_filename = generate_screenshot_filename(controller, route['action'])
      screenshot_path = "screenshots/#{user_type}/#{screenshot_filename}"
      
      content << "![#{controller}_#{route['action']}](#{screenshot_path})"
      content << ""
      content << "*Screenshot placeholder - Image will be captured during screenshot automation process*"
      content << ""
      content << "---"
      content << ""
    end
    
    content
  end

  def generate_screenshot_filename(controller, action)
    # Convert controller/action to filename
    controller_part = controller.gsub('/', '_')
    "#{controller_part}_#{action}.png"
  end

  def generate_summary_documentation
    content = []
    content << "# Application Screenshots Summary"
    content << ""
    content << "Generated on: #{Time.now.strftime('%Y-%m-%d %H:%M:%S')}"
    content << ""
    content << "## Overview"
    content << ""
    content << "This document provides a summary of all screenshot documentation for the Ghostwrote application, organized by user type."
    content << ""
    
    @user_types.each do |user_type|
      routes = @routes_data['user_types'][user_type] || []
      content << "## #{user_type.capitalize} Routes"
      content << ""
      content << "- **Total Routes:** #{routes.length}"
      content << "- **Documentation:** [#{user_type.capitalize} Screenshots](#{user_type}-routes-screenshots.md)"
      content << ""
      
      # Show route breakdown by action type
      action_counts = routes.group_by { |r| r['action'] }.transform_values(&:count)
      content << "**Route Breakdown:**"
      ['index', 'show', 'new', 'edit'].each do |action|
        count = action_counts[action] || 0
        content << "- #{action.titleize}: #{count} routes"
      end
      content << ""
    end
    
    content << "## Screenshot Status"
    content << ""
    content << "📸 **Screenshot directories created:** `screenshots/talent/`, `screenshots/scout/`, `screenshots/superadmin/`"
    content << ""
    content << "⚠️  **Current Status:** Screenshot files are not yet captured. Documentation contains placeholder references."
    content << ""
    content << "## Next Steps"
    content << ""
    content << "1. **Start Rails Application:** Ensure the application is running on `localhost:5010`"
    content << "2. **Verify Test Users:** Confirm test users exist with proper credentials"
    content << "3. **Run Screenshot Capture:** Use Playwright MCP to capture screenshots for all routes"
    content << "4. **Validate Results:** Review captured screenshots and update documentation as needed"
    content << ""
    
    File.write('screenshots-summary.md', content.join("\n"))
    puts "Generated: screenshots-summary.md"
  end
end

# Run the generator
if __FILE__ == $0
  generator = DocumentationGenerator.new
  generator.generate_all_documentation
end
