#!/usr/bin/env ruby

require 'json'
require 'fileutils'

# This script continues the screenshot capture process

# Test user credentials
USERS = {
  'talent' => {
    email: '<EMAIL>',
    password: 'Secret1*3*5*',
  },
  'scout' => {
    email: '<EMAIL>',
    password: 'Secret1*3*5*',
  },
  'superadmin' => {
    email: '<EMAIL>',
    password: 'Secret1*3*5*',
  },
}

# Priority routes for each user type (focusing on main navigation pages)
PRIORITY_ROUTES = {
  'talent' => %w[
    /talent
    /talent/jobs
    /talent/profile
    /talent/profile/edit
    /talent/conversations
    /talent/job_applications
  ],
  'scout' => %w[
    /scout
    /scout/jobs
    /scout/jobs/new
    /scout/talent
    /scout/conversations
    /scout/settings
    /scout/settings/account
    /scout/settings/organization
    /scout/settings/subscription
  ],
  'superadmin' => %w[
    /super_admin
    /super_admin/users
    /super_admin/jobs
    /super_admin/subscription_plans
    /super_admin/statistics
  ],
}

def create_screenshot_directories
  %w[talent scout superadmin].each do |user_type|
    dir = "screenshots/#{user_type}"
    FileUtils.mkdir_p(dir) unless Dir.exist?(dir)
  end
end

def get_routes_for_user_type(routes_data, user_type)
  return [] unless routes_data['user_types'][user_type]

  # Get priority routes first, then others
  priority_routes = PRIORITY_ROUTES[user_type] || []
  all_routes = routes_data['user_types'][user_type].map { |r| r['path'] }

  # Return priority routes first, then remaining routes
  priority_routes + (all_routes - priority_routes)
end

def generate_screenshot_commands(user_type, routes)
  commands = []

  # Login commands
  commands << "# Login as #{user_type}"
  commands << 'browser_navigate_Playwright http://localhost:5010/sign_in'
  commands << "browser_type_Playwright email_field #{USERS[user_type][:email]}"
  commands <<
    "browser_type_Playwright password_field #{USERS[user_type][:password]}"
  commands << 'browser_click_Playwright login_button'
  commands << ''

  # Screenshot commands for each route
  routes.each_with_index do |route, index|
    # Skip routes with parameters that need specific IDs
    if route.include?(':') || route.include?('/1/') ||
         route.include?('/new') && route.include?('/')
      next
    end

    filename = route.gsub('/', '_').gsub(/^_/, '').gsub(/_$/, '')
    filename = "#{user_type}_#{filename}" if filename.empty?
    filename = "#{filename}.png"

    commands << "# Screenshot #{index + 1}: #{route}"
    commands << "browser_navigate_Playwright http://localhost:5010#{route}"
    commands <<
      "browser_take_screenshot_Playwright screenshots/#{user_type}/#{filename}"
    commands << ''
  end

  # Logout commands
  commands << '# Logout'
  commands << 'browser_click_Playwright user_menu'
  commands << 'browser_click_Playwright logout_button'
  commands << ''

  commands
end

def main
  # Load routes from the generated routes.json file
  routes_file = File.join(__dir__, 'routes.json')
  unless File.exist?(routes_file)
    puts 'Error: routes.json not found. Please run discover_routes.rb first.'
    exit 1
  end

  routes_data = JSON.parse(File.read(routes_file))

  puts 'Creating screenshot directories...'
  create_screenshot_directories

  puts 'Generating screenshot capture plan...'

  # Generate commands for each user type
  %w[talent scout superadmin].each do |user_type|
    puts "\n=== #{user_type.upcase} ROUTES ==="
    routes = get_routes_for_user_type(routes_data, user_type)

    puts "Priority routes for #{user_type}:"
    PRIORITY_ROUTES[user_type]&.each { |route| puts "  - #{route}" }

    puts "\nTotal routes to capture: #{routes.length}"

    # Save commands to file for reference
    commands = generate_screenshot_commands(user_type, routes)
    File.write(
      "scripts/screenshot-automation/#{user_type}_commands.txt",
      commands.join("\n"),
    )
    puts "Commands saved to #{user_type}_commands.txt"
  end

  puts "\n=== SUMMARY ==="
  puts 'Total routes by user type:'
  %w[talent scout superadmin].each do |user_type|
    routes = get_routes_for_user_type(routes_data, user_type)
    puts "  #{user_type}: #{routes.length} routes"
  end

  puts "\nNext steps:"
  puts '1. Use Playwright MCP to execute the commands for each user type'
  puts '2. Start with talent_commands.txt, then scout_commands.txt, then superadmin_commands.txt'
  puts '3. Manually navigate and screenshot any routes that require specific parameters'
end

main if __FILE__ == $0
