#!/usr/bin/env ruby

require 'fileutils'

class ScreenshotOrganizer
  def initialize
    @temp_dir = '/tmp/playwright-mcp-output'
    @target_dir = 'screenshots'
    @mapping = {
      'superadmin_dashboard' => 'screenshots/superadmin/super_admin_dashboard_index.png',
      'superadmin_admin_interface' => 'screenshots/superadmin/super_admin_admin_dashboard_index.png',
      'superadmin_admin_users' => 'screenshots/superadmin/super_admin_admin_users_index.png',
      'superadmin_admin_jobs' => 'screenshots/superadmin/super_admin_admin_jobs_index.png',
      'superadmin_admin_organizations' => 'screenshots/superadmin/super_admin_admin_organizations_index.png',
      'superadmin_badge_types' => 'screenshots/superadmin/super_admin_badge_types_index.png',
      'superadmin_subscription_plans' => 'screenshots/superadmin/super_admin_subscription_plans_index.png',
      'scout_dashboard' => 'screenshots/scout/scout_jobs_index.png',
      'scout_jobs' => 'screenshots/scout/scout_jobs_index.png',
      'scout_talent' => 'screenshots/scout/scout_talent_index.png',
      'scout_applicants' => 'screenshots/scout/scout_applicants_index.png',
      'scout_settings' => 'screenshots/scout/scout_settings_show.png',
      'talent_dashboard' => 'screenshots/talent/talent_jobs_index.png',
      'talent_jobs' => 'screenshots/talent/talent_jobs_index.png',
      'talent_profile' => 'screenshots/talent/talent_profiles_show.png',
      'talent_job_applications' => 'screenshots/talent/talent_job_applications_index.png',
      'talent_settings' => 'screenshots/talent/talent_settings_show.png'
    }
  end

  def find_and_organize_screenshots
    puts "Looking for screenshots in #{@temp_dir}..."
    
    # Find all PNG files in temp directories
    temp_files = Dir.glob("#{@temp_dir}/**/*.png")
    
    if temp_files.empty?
      puts "No PNG files found in temp directories"
      return
    end
    
    puts "Found #{temp_files.length} PNG files:"
    temp_files.each { |file| puts "  #{file}" }
    
    # Try to match and copy files
    temp_files.each do |temp_file|
      filename = File.basename(temp_file, '.png')
      
      # Try to find a mapping
      target_path = find_target_path(filename)
      
      if target_path
        copy_screenshot(temp_file, target_path)
      else
        puts "No mapping found for: #{filename}"
      end
    end
  end

  def find_target_path(filename)
    # Try exact match first
    return @mapping[filename] if @mapping[filename]
    
    # Try partial matches
    @mapping.each do |key, target|
      if filename.include?(key.gsub('_', '-')) || filename.include?(key)
        return target
      end
    end
    
    # Try to infer from filename patterns
    case filename
    when /dashboard/
      'screenshots/superadmin/super_admin_dashboard_index.png'
    when /admin.*interface/
      'screenshots/superadmin/super_admin_admin_dashboard_index.png'
    when /admin.*users/
      'screenshots/superadmin/super_admin_admin_users_index.png'
    when /admin.*jobs/
      'screenshots/superadmin/super_admin_admin_jobs_index.png'
    when /admin.*organizations/
      'screenshots/superadmin/super_admin_admin_organizations_index.png'
    when /badge/
      'screenshots/superadmin/super_admin_badge_types_index.png'
    when /subscription/
      'screenshots/superadmin/super_admin_subscription_plans_index.png'
    else
      nil
    end
  end

  def copy_screenshot(source, target)
    # Ensure target directory exists
    target_dir = File.dirname(target)
    FileUtils.mkdir_p(target_dir) unless Dir.exist?(target_dir)
    
    # Copy file
    begin
      FileUtils.cp(source, target)
      puts "✓ Copied #{File.basename(source)} -> #{target}"
    rescue => e
      puts "✗ Failed to copy #{source}: #{e.message}"
    end
  end

  def list_current_screenshots
    puts "\nCurrent screenshots in target directories:"
    
    ['talent', 'scout', 'superadmin'].each do |user_type|
      dir = "screenshots/#{user_type}"
      if Dir.exist?(dir)
        files = Dir.glob("#{dir}/*.png")
        puts "\n#{user_type.capitalize} (#{files.length} files):"
        files.each { |file| puts "  #{File.basename(file)}" }
      else
        puts "\n#{user_type.capitalize}: Directory not found"
      end
    end
  end

  def create_placeholder_screenshots
    puts "\nCreating placeholder screenshots for missing files..."
    
    routes_data = JSON.parse(File.read('routes.json'))
    
    ['talent', 'scout', 'superadmin'].each do |user_type|
      routes = routes_data['user_types'][user_type] || []
      
      routes.each do |route|
        controller = route['controller'].gsub('/', '_')
        action = route['action']
        filename = "#{controller}_#{action}.png"
        filepath = "screenshots/#{user_type}/#{filename}"
        
        unless File.exist?(filepath)
          # Create a simple placeholder file
          FileUtils.mkdir_p(File.dirname(filepath))
          File.write(filepath.gsub('.png', '.placeholder'), "Placeholder for #{route['path']}")
          puts "Created placeholder: #{filepath}.placeholder"
        end
      end
    end
  end
end

if __FILE__ == $0
  organizer = ScreenshotOrganizer.new
  
  puts "=== Screenshot Organization Tool ==="
  puts "1. Finding and organizing screenshots..."
  organizer.find_and_organize_screenshots
  
  puts "\n2. Listing current screenshots..."
  organizer.list_current_screenshots
  
  puts "\n3. Creating placeholders for missing files..."
  organizer.create_placeholder_screenshots
  
  puts "\nDone!"
end
