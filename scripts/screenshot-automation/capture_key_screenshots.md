# Key Screenshot Capture Guide

This guide provides step-by-step instructions for capturing key screenshots using Playwright MCP.

## Current Status

✅ **Rails Server:** Running on localhost:5010  
✅ **Test Users:** All test users exist and are properly configured  
✅ **Documentation:** Updated with comprehensive route trees  
⚠️  **Screenshots:** Need to be captured  

## Playwright MCP Screenshot Capture Process

### Phase 1: SuperAdmin Screenshots (Currently Active)

The browser is currently logged in as a superadmin user and on the dashboard.

#### Key SuperAdmin Routes to Capture:

1. **Dashboard** - `/super_admin` ✅ (Currently viewing)
2. **Admin Interface** - `/super_admin/admin`
3. **User Management** - `/super_admin/admin_users`
4. **Organization Management** - `/super_admin/admin_organizations`
5. **Job Administration** - `/super_admin/admin_jobs`
6. **Badge System** - `/super_admin/badge_types`
7. **Subscription Management** - `/super_admin/subscription_plans`

#### Capture Commands:

```
1. Take screenshot of current dashboard:
   browser_take_screenshot_Playwright with filename: "superadmin_dashboard.png"

2. Navigate to Admin Interface:
   browser_click_Playwright on "Admin Interface" link
   browser_take_screenshot_Playwright with filename: "superadmin_admin_interface.png"

3. Navigate to User Management:
   browser_navigate_Playwright to "http://localhost:5010/super_admin/admin_users"
   browser_take_screenshot_Playwright with filename: "superadmin_admin_users_index.png"

4. Navigate to Organization Management:
   browser_navigate_Playwright to "http://localhost:5010/super_admin/admin_organizations"
   browser_take_screenshot_Playwright with filename: "superadmin_admin_organizations_index.png"

5. Navigate to Job Administration:
   browser_navigate_Playwright to "http://localhost:5010/super_admin/admin_jobs"
   browser_take_screenshot_Playwright with filename: "superadmin_admin_jobs_index.png"
```

### Phase 2: Scout Screenshots

After completing superadmin screenshots, sign out and sign in as scout user.

#### Authentication:
- Email: <EMAIL>
- Password: Secret1*3*5*

#### Key Scout Routes:
1. **Scout Dashboard** - `/scout`
2. **Job Management** - `/scout/jobs`
3. **Talent Discovery** - `/scout/talent`
4. **Applicant Management** - `/scout/applicants`
5. **Settings** - `/scout/settings`

### Phase 3: Talent Screenshots

After completing scout screenshots, sign out and sign in as talent user.

#### Authentication:
- Email: <EMAIL>
- Password: Secret1*3*5*

#### Key Talent Routes:
1. **Talent Dashboard** - `/talent`
2. **Job Applications** - `/talent/job_applications`
3. **Profile** - `/talent/profile`
4. **Messages** - `/talent/messages`
5. **Settings** - `/talent/settings`

## Screenshot Organization

After capturing screenshots, they need to be organized into the correct directories:

```
screenshots/
├── superadmin/
│   ├── super_admin_dashboard_index.png
│   ├── super_admin_admin_dashboard_index.png
│   ├── super_admin_admin_users_index.png
│   └── ...
├── scout/
│   ├── scout_jobs_index.png
│   ├── scout_talent_index.png
│   └── ...
└── talent/
    ├── talent_jobs_index.png
    ├── talent_job_applications_index.png
    └── ...
```

## File Naming Convention

Screenshots should be named using the pattern:
`{controller}_{action}.png`

Examples:
- `super_admin_dashboard_index.png`
- `scout_jobs_index.png`
- `talent_profiles_show.png`

## Next Steps

1. **Continue SuperAdmin Capture:** Complete the remaining superadmin screenshots
2. **Scout User Session:** Sign out and capture scout screenshots
3. **Talent User Session:** Sign out and capture talent screenshots
4. **Organize Files:** Move screenshots to correct directories
5. **Update Documentation:** Replace placeholder references with actual screenshots
6. **Validation:** Review all captured screenshots for quality and completeness

## Quality Checklist

For each screenshot:
- [ ] Page fully loaded (no loading spinners)
- [ ] Proper viewport size (1920x1080 recommended)
- [ ] Clear, readable text
- [ ] All UI elements visible
- [ ] Consistent styling across screenshots
- [ ] Proper file naming convention followed

## Troubleshooting

### Common Issues:
1. **Authentication Failures:** Verify user credentials and account status
2. **Route Access Errors:** Check user permissions and route availability
3. **Page Load Issues:** Increase wait times or check for JavaScript errors
4. **Screenshot Quality:** Adjust viewport size or wait for animations to complete

### Error Recovery:
- If authentication fails, clear browser cookies and retry
- If routes return 404, verify the route exists in routes.json
- If screenshots are blank, increase wait time after navigation
