# Superadmin Routes Screenshots

Generated on: 2025-07-09 15:47:11

## Overview

This document contains screenshots of all superadmin-facing pages in the Ghostwrote application. These screenshots provide a comprehensive view of the superadmin user experience and interface design.

## Route Tree Structure

### Dashboard

- **Index (List/Index)** - `/super_admin`

### Admin Interface

- **Index (List/Index)** - `/super_admin/admin`

### User Management

- **Index (List/Index)** - `/super_admin/admin_users`
- **Show (Show/Detail)** - `/super_admin/admin_users/1`
- **Edit (Edit Form)** - `/super_admin/admin_users/1/edit`

### Organization Management

- **Index (List/Index)** - `/super_admin/admin_organizations`
- **Show (Show/Detail)** - `/super_admin/admin_organizations/1`
- **Edit (Edit Form)** - `/super_admin/admin_organizations/1/edit`

### Job Administration

- **Index (List/Index)** - `/super_admin/admin_jobs`
- **Show (Show/Detail)** - `/super_admin/admin_jobs/1`
- **Edit (Edit Form)** - `/super_admin/admin_jobs/1/edit`

### Application Administration

- **Index (List/Index)** - `/super_admin/admin_job_applications`
- **Show (Show/Detail)** - `/super_admin/admin_job_applications/1`

### Badge System

- **Index (List/Index)** - `/super_admin/badge_analytics`
- **Index (List/Index)** - `/super_admin/badge_types`
- **Show (Show/Detail)** - `/super_admin/badge_types/1`
- **New (New/Create Form)** - `/super_admin/badge_types/new`
- **Edit (Edit Form)** - `/super_admin/badge_types/1/edit`
- **Distribution** - `/super_admin/badge_analytics/distribution`
- **Export** - `/super_admin/badge_analytics/export`
- **Performance** - `/super_admin/badge_analytics/performance`

### Subscription Management

- **Index (List/Index)** - `/super_admin/subscription_plan_sync`
- **Index (List/Index)** - `/super_admin/subscription_plans`
- **Show (Show/Detail)** - `/super_admin/subscription_plans/1`
- **New (New/Create Form)** - `/super_admin/subscription_plans/new`
- **Edit (Edit Form)** - `/super_admin/subscription_plans/1/edit`
- **History** - `/super_admin/subscription_plan_sync/history`
- **Status** - `/super_admin/subscription_plan_sync/status`
- **Discover Products** - `/super_admin/subscription_plans/discover_products`

### Audit & Security

- **Index (List/Index)** - `/super_admin/admin_audit_logs`
- **Show (Show/Detail)** - `/super_admin/admin_audit_logs/1`

### Session Management

- **Index (List/Index)** - `/super_admin/admin_sessions`
- **Show (Show/Detail)** - `/super_admin/admin_sessions/1`

### Data Export

- **Index (List/Index)** - `/super_admin/csv_exports`
- **Show (Show/Detail)** - `/super_admin/csv_exports/1`

### User Impersonation

- **Index (List/Index)** - `/super_admin/user_masquerade`

### Super Admin / Admin Job Invitations

- **Index (List/Index)** - `/super_admin/admin_job_invitations`
- **Show (Show/Detail)** - `/super_admin/admin_job_invitations/1`
- **Edit (Edit Form)** - `/super_admin/admin_job_invitations/1/edit`

### Super Admin / Admin Organization Memberships

- **Index (List/Index)** - `/super_admin/admin_organization_memberships`
- **Show (Show/Detail)** - `/super_admin/admin_organization_memberships/1`
- **Edit (Edit Form)** - `/super_admin/admin_organization_memberships/1/edit`

### Super Admin / Admin Roles

- **Index (List/Index)** - `/super_admin/admin_roles`
- **Show (Show/Detail)** - `/super_admin/admin_roles/1`
- **New (New/Create Form)** - `/super_admin/admin_roles/new`
- **Edit (Edit Form)** - `/super_admin/admin_roles/1/edit`

### Super Admin / Admin Talent Notes

- **Index (List/Index)** - `/super_admin/admin_talent_notes`
- **Show (Show/Detail)** - `/super_admin/admin_talent_notes/1`
- **Edit (Edit Form)** - `/super_admin/admin_talent_notes/1/edit`

### Super Admin / Admin Chat Requests

- **Index (List/Index)** - `/super_admin/admin_chat_requests`
- **Show (Show/Detail)** - `/super_admin/admin_chat_requests/1`

### Super Admin / Admin Conversations

- **Index (List/Index)** - `/super_admin/admin_conversations`
- **Show (Show/Detail)** - `/super_admin/admin_conversations/1`

### Super Admin / Admin Files

- **Index (List/Index)** - `/super_admin/admin_files`
- **Show (Show/Detail)** - `/super_admin/admin_files/1`
- **Download** - `/super_admin/admin_files/1/download`

### Super Admin / Admin Messages

- **Index (List/Index)** - `/super_admin/admin_messages`
- **Show (Show/Detail)** - `/super_admin/admin_messages/1`

### Super Admin / Admin Saved Jobs

- **Index (List/Index)** - `/super_admin/admin_saved_jobs`
- **Show (Show/Detail)** - `/super_admin/admin_saved_jobs/1`

### Super Admin / Admin Talent Bookmarks

- **Index (List/Index)** - `/super_admin/admin_talent_bookmarks`
- **Show (Show/Detail)** - `/super_admin/admin_talent_bookmarks/1`

### Super Admin / Admin Talent Profiles

- **Index (List/Index)** - `/super_admin/admin_talent_profiles`
- **Show (Show/Detail)** - `/super_admin/admin_talent_profiles/1`

### Super Admin / Saved Searches

- **Index (List/Index)** - `/super_admin/saved_searches`
- **Show (Show/Detail)** - `/super_admin/saved_searches/1`

### Super Admin / Security Alerts

- **Index (List/Index)** - `/super_admin/security_alerts`
- **Show (Show/Detail)** - `/super_admin/security_alerts/1`

### Super Admin / Session Activities

- **Index (List/Index)** - `/super_admin/session_activities`
- **Show (Show/Detail)** - `/super_admin/session_activities/1`


---

## Detailed Screenshots

### Dashboard

#### Index - `/super_admin`

**Controller:** `super_admin/dashboard`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_root`

![super_admin/dashboard_index](screenshots/superadmin/super_admin_dashboard_index.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

### Admin Interface

#### Index - `/super_admin/admin`

**Controller:** `super_admin/admin_dashboard`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_admin_dashboard`

![super_admin/admin_dashboard_index](screenshots/superadmin/super_admin_admin_dashboard_index.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

### User Management

#### Index - `/super_admin/admin_users`

**Controller:** `super_admin/admin_users`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_admin_users`

![super_admin/admin_users_index](screenshots/superadmin/super_admin_admin_users_index.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Show - `/super_admin/admin_users/1`

**Controller:** `super_admin/admin_users`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_admin_user`

![super_admin/admin_users_show](screenshots/superadmin/super_admin_admin_users_show.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Edit - `/super_admin/admin_users/1/edit`

**Controller:** `super_admin/admin_users`  
**Action:** `edit`  
**HTTP Method:** `GET`  
**Route Name:** `edit_super_admin_admin_user`

![super_admin/admin_users_edit](screenshots/superadmin/super_admin_admin_users_edit.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

### Organization Management

#### Index - `/super_admin/admin_organizations`

**Controller:** `super_admin/admin_organizations`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_admin_organizations`

![super_admin/admin_organizations_index](screenshots/superadmin/super_admin_admin_organizations_index.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Show - `/super_admin/admin_organizations/1`

**Controller:** `super_admin/admin_organizations`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_admin_organization`

![super_admin/admin_organizations_show](screenshots/superadmin/super_admin_admin_organizations_show.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Edit - `/super_admin/admin_organizations/1/edit`

**Controller:** `super_admin/admin_organizations`  
**Action:** `edit`  
**HTTP Method:** `GET`  
**Route Name:** `edit_super_admin_admin_organization`

![super_admin/admin_organizations_edit](screenshots/superadmin/super_admin_admin_organizations_edit.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

### Job Administration

#### Index - `/super_admin/admin_jobs`

**Controller:** `super_admin/admin_jobs`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_admin_jobs`

![super_admin/admin_jobs_index](screenshots/superadmin/super_admin_admin_jobs_index.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Show - `/super_admin/admin_jobs/1`

**Controller:** `super_admin/admin_jobs`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_admin_job`

![super_admin/admin_jobs_show](screenshots/superadmin/super_admin_admin_jobs_show.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Edit - `/super_admin/admin_jobs/1/edit`

**Controller:** `super_admin/admin_jobs`  
**Action:** `edit`  
**HTTP Method:** `GET`  
**Route Name:** `edit_super_admin_admin_job`

![super_admin/admin_jobs_edit](screenshots/superadmin/super_admin_admin_jobs_edit.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

### Application Administration

#### Index - `/super_admin/admin_job_applications`

**Controller:** `super_admin/admin_job_applications`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_admin_job_applications`

![super_admin/admin_job_applications_index](screenshots/superadmin/super_admin_admin_job_applications_index.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Show - `/super_admin/admin_job_applications/1`

**Controller:** `super_admin/admin_job_applications`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_admin_job_application`

![super_admin/admin_job_applications_show](screenshots/superadmin/super_admin_admin_job_applications_show.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

### Badge System

#### Index - `/super_admin/badge_analytics`

**Controller:** `super_admin/badge_analytics`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_badge_analytics`

![super_admin/badge_analytics_index](screenshots/superadmin/super_admin_badge_analytics_index.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Index - `/super_admin/badge_types`

**Controller:** `super_admin/badge_types`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_badge_types`

![super_admin/badge_types_index](screenshots/superadmin/super_admin_badge_types_index.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Show - `/super_admin/badge_types/1`

**Controller:** `super_admin/badge_types`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_badge_type`

![super_admin/badge_types_show](screenshots/superadmin/super_admin_badge_types_show.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### New - `/super_admin/badge_types/new`

**Controller:** `super_admin/badge_types`  
**Action:** `new`  
**HTTP Method:** `GET`  
**Route Name:** `new_super_admin_badge_type`

![super_admin/badge_types_new](screenshots/superadmin/super_admin_badge_types_new.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Edit - `/super_admin/badge_types/1/edit`

**Controller:** `super_admin/badge_types`  
**Action:** `edit`  
**HTTP Method:** `GET`  
**Route Name:** `edit_super_admin_badge_type`

![super_admin/badge_types_edit](screenshots/superadmin/super_admin_badge_types_edit.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Distribution - `/super_admin/badge_analytics/distribution`

**Controller:** `super_admin/badge_analytics`  
**Action:** `distribution`  
**HTTP Method:** `GET`  
**Route Name:** `distribution_super_admin_badge_analytics`

![super_admin/badge_analytics_distribution](screenshots/superadmin/super_admin_badge_analytics_distribution.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Export - `/super_admin/badge_analytics/export`

**Controller:** `super_admin/badge_analytics`  
**Action:** `export`  
**HTTP Method:** `GET`  
**Route Name:** `export_super_admin_badge_analytics`

![super_admin/badge_analytics_export](screenshots/superadmin/super_admin_badge_analytics_export.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Performance - `/super_admin/badge_analytics/performance`

**Controller:** `super_admin/badge_analytics`  
**Action:** `performance`  
**HTTP Method:** `GET`  
**Route Name:** `performance_super_admin_badge_analytics`

![super_admin/badge_analytics_performance](screenshots/superadmin/super_admin_badge_analytics_performance.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

### Subscription Management

#### Index - `/super_admin/subscription_plan_sync`

**Controller:** `super_admin/subscription_plan_sync`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_subscription_plan_sync_index`

![super_admin/subscription_plan_sync_index](screenshots/superadmin/super_admin_subscription_plan_sync_index.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Index - `/super_admin/subscription_plans`

**Controller:** `super_admin/subscription_plans`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_subscription_plans`

![super_admin/subscription_plans_index](screenshots/superadmin/super_admin_subscription_plans_index.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Show - `/super_admin/subscription_plans/1`

**Controller:** `super_admin/subscription_plans`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_subscription_plan`

![super_admin/subscription_plans_show](screenshots/superadmin/super_admin_subscription_plans_show.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### New - `/super_admin/subscription_plans/new`

**Controller:** `super_admin/subscription_plans`  
**Action:** `new`  
**HTTP Method:** `GET`  
**Route Name:** `new_super_admin_subscription_plan`

![super_admin/subscription_plans_new](screenshots/superadmin/super_admin_subscription_plans_new.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Edit - `/super_admin/subscription_plans/1/edit`

**Controller:** `super_admin/subscription_plans`  
**Action:** `edit`  
**HTTP Method:** `GET`  
**Route Name:** `edit_super_admin_subscription_plan`

![super_admin/subscription_plans_edit](screenshots/superadmin/super_admin_subscription_plans_edit.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### History - `/super_admin/subscription_plan_sync/history`

**Controller:** `super_admin/subscription_plan_sync`  
**Action:** `history`  
**HTTP Method:** `GET`  
**Route Name:** `history_super_admin_subscription_plan_sync_index`

![super_admin/subscription_plan_sync_history](screenshots/superadmin/super_admin_subscription_plan_sync_history.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Status - `/super_admin/subscription_plan_sync/status`

**Controller:** `super_admin/subscription_plan_sync`  
**Action:** `status`  
**HTTP Method:** `GET`  
**Route Name:** `status_super_admin_subscription_plan_sync_index`

![super_admin/subscription_plan_sync_status](screenshots/superadmin/super_admin_subscription_plan_sync_status.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Discover Products - `/super_admin/subscription_plans/discover_products`

**Controller:** `super_admin/subscription_plans`  
**Action:** `discover_products`  
**HTTP Method:** `GET`  
**Route Name:** `discover_products_super_admin_subscription_plans`

![super_admin/subscription_plans_discover_products](screenshots/superadmin/super_admin_subscription_plans_discover_products.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

### Audit & Security

#### Index - `/super_admin/admin_audit_logs`

**Controller:** `super_admin/admin_audit_logs`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_admin_audit_logs`

![super_admin/admin_audit_logs_index](screenshots/superadmin/super_admin_admin_audit_logs_index.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Show - `/super_admin/admin_audit_logs/1`

**Controller:** `super_admin/admin_audit_logs`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_admin_audit_log`

![super_admin/admin_audit_logs_show](screenshots/superadmin/super_admin_admin_audit_logs_show.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

### Session Management

#### Index - `/super_admin/admin_sessions`

**Controller:** `super_admin/admin_sessions`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_admin_sessions`

![super_admin/admin_sessions_index](screenshots/superadmin/super_admin_admin_sessions_index.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Show - `/super_admin/admin_sessions/1`

**Controller:** `super_admin/admin_sessions`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_admin_session`

![super_admin/admin_sessions_show](screenshots/superadmin/super_admin_admin_sessions_show.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

### Data Export

#### Index - `/super_admin/csv_exports`

**Controller:** `super_admin/csv_exports`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_csv_exports`

![super_admin/csv_exports_index](screenshots/superadmin/super_admin_csv_exports_index.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Show - `/super_admin/csv_exports/1`

**Controller:** `super_admin/csv_exports`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_csv_export`

![super_admin/csv_exports_show](screenshots/superadmin/super_admin_csv_exports_show.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

### User Impersonation

#### Index - `/super_admin/user_masquerade`

**Controller:** `super_admin/user_masquerade`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_user_masquerade_index`

![super_admin/user_masquerade_index](screenshots/superadmin/super_admin_user_masquerade_index.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

### Super Admin / Admin Job Invitations

#### Index - `/super_admin/admin_job_invitations`

**Controller:** `super_admin/admin_job_invitations`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_admin_job_invitations`

![super_admin/admin_job_invitations_index](screenshots/superadmin/super_admin_admin_job_invitations_index.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Show - `/super_admin/admin_job_invitations/1`

**Controller:** `super_admin/admin_job_invitations`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_admin_job_invitation`

![super_admin/admin_job_invitations_show](screenshots/superadmin/super_admin_admin_job_invitations_show.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Edit - `/super_admin/admin_job_invitations/1/edit`

**Controller:** `super_admin/admin_job_invitations`  
**Action:** `edit`  
**HTTP Method:** `GET`  
**Route Name:** `edit_super_admin_admin_job_invitation`

![super_admin/admin_job_invitations_edit](screenshots/superadmin/super_admin_admin_job_invitations_edit.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

### Super Admin / Admin Organization Memberships

#### Index - `/super_admin/admin_organization_memberships`

**Controller:** `super_admin/admin_organization_memberships`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_admin_organization_memberships`

![super_admin/admin_organization_memberships_index](screenshots/superadmin/super_admin_admin_organization_memberships_index.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Show - `/super_admin/admin_organization_memberships/1`

**Controller:** `super_admin/admin_organization_memberships`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_admin_organization_membership`

![super_admin/admin_organization_memberships_show](screenshots/superadmin/super_admin_admin_organization_memberships_show.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Edit - `/super_admin/admin_organization_memberships/1/edit`

**Controller:** `super_admin/admin_organization_memberships`  
**Action:** `edit`  
**HTTP Method:** `GET`  
**Route Name:** `edit_super_admin_admin_organization_membership`

![super_admin/admin_organization_memberships_edit](screenshots/superadmin/super_admin_admin_organization_memberships_edit.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

### Super Admin / Admin Roles

#### Index - `/super_admin/admin_roles`

**Controller:** `super_admin/admin_roles`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_admin_roles`

![super_admin/admin_roles_index](screenshots/superadmin/super_admin_admin_roles_index.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Show - `/super_admin/admin_roles/1`

**Controller:** `super_admin/admin_roles`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_admin_role`

![super_admin/admin_roles_show](screenshots/superadmin/super_admin_admin_roles_show.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### New - `/super_admin/admin_roles/new`

**Controller:** `super_admin/admin_roles`  
**Action:** `new`  
**HTTP Method:** `GET`  
**Route Name:** `new_super_admin_admin_role`

![super_admin/admin_roles_new](screenshots/superadmin/super_admin_admin_roles_new.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Edit - `/super_admin/admin_roles/1/edit`

**Controller:** `super_admin/admin_roles`  
**Action:** `edit`  
**HTTP Method:** `GET`  
**Route Name:** `edit_super_admin_admin_role`

![super_admin/admin_roles_edit](screenshots/superadmin/super_admin_admin_roles_edit.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

### Super Admin / Admin Talent Notes

#### Index - `/super_admin/admin_talent_notes`

**Controller:** `super_admin/admin_talent_notes`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_admin_talent_notes`

![super_admin/admin_talent_notes_index](screenshots/superadmin/super_admin_admin_talent_notes_index.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Show - `/super_admin/admin_talent_notes/1`

**Controller:** `super_admin/admin_talent_notes`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_admin_talent_note`

![super_admin/admin_talent_notes_show](screenshots/superadmin/super_admin_admin_talent_notes_show.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Edit - `/super_admin/admin_talent_notes/1/edit`

**Controller:** `super_admin/admin_talent_notes`  
**Action:** `edit`  
**HTTP Method:** `GET`  
**Route Name:** `edit_super_admin_admin_talent_note`

![super_admin/admin_talent_notes_edit](screenshots/superadmin/super_admin_admin_talent_notes_edit.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

### Super Admin / Admin Chat Requests

#### Index - `/super_admin/admin_chat_requests`

**Controller:** `super_admin/admin_chat_requests`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_admin_chat_requests`

![super_admin/admin_chat_requests_index](screenshots/superadmin/super_admin_admin_chat_requests_index.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Show - `/super_admin/admin_chat_requests/1`

**Controller:** `super_admin/admin_chat_requests`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_admin_chat_request`

![super_admin/admin_chat_requests_show](screenshots/superadmin/super_admin_admin_chat_requests_show.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

### Super Admin / Admin Conversations

#### Index - `/super_admin/admin_conversations`

**Controller:** `super_admin/admin_conversations`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_admin_conversations`

![super_admin/admin_conversations_index](screenshots/superadmin/super_admin_admin_conversations_index.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Show - `/super_admin/admin_conversations/1`

**Controller:** `super_admin/admin_conversations`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_admin_conversation`

![super_admin/admin_conversations_show](screenshots/superadmin/super_admin_admin_conversations_show.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

### Super Admin / Admin Files

#### Index - `/super_admin/admin_files`

**Controller:** `super_admin/admin_files`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_admin_files`

![super_admin/admin_files_index](screenshots/superadmin/super_admin_admin_files_index.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Show - `/super_admin/admin_files/1`

**Controller:** `super_admin/admin_files`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_admin_file`

![super_admin/admin_files_show](screenshots/superadmin/super_admin_admin_files_show.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Download - `/super_admin/admin_files/1/download`

**Controller:** `super_admin/admin_files`  
**Action:** `download`  
**HTTP Method:** `GET`  
**Route Name:** `download_super_admin_admin_file`

![super_admin/admin_files_download](screenshots/superadmin/super_admin_admin_files_download.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

### Super Admin / Admin Messages

#### Index - `/super_admin/admin_messages`

**Controller:** `super_admin/admin_messages`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_admin_messages`

![super_admin/admin_messages_index](screenshots/superadmin/super_admin_admin_messages_index.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Show - `/super_admin/admin_messages/1`

**Controller:** `super_admin/admin_messages`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_admin_message`

![super_admin/admin_messages_show](screenshots/superadmin/super_admin_admin_messages_show.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

### Super Admin / Admin Saved Jobs

#### Index - `/super_admin/admin_saved_jobs`

**Controller:** `super_admin/admin_saved_jobs`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_admin_saved_jobs`

![super_admin/admin_saved_jobs_index](screenshots/superadmin/super_admin_admin_saved_jobs_index.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Show - `/super_admin/admin_saved_jobs/1`

**Controller:** `super_admin/admin_saved_jobs`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_admin_saved_job`

![super_admin/admin_saved_jobs_show](screenshots/superadmin/super_admin_admin_saved_jobs_show.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

### Super Admin / Admin Talent Bookmarks

#### Index - `/super_admin/admin_talent_bookmarks`

**Controller:** `super_admin/admin_talent_bookmarks`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_admin_talent_bookmarks`

![super_admin/admin_talent_bookmarks_index](screenshots/superadmin/super_admin_admin_talent_bookmarks_index.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Show - `/super_admin/admin_talent_bookmarks/1`

**Controller:** `super_admin/admin_talent_bookmarks`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_admin_talent_bookmark`

![super_admin/admin_talent_bookmarks_show](screenshots/superadmin/super_admin_admin_talent_bookmarks_show.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

### Super Admin / Admin Talent Profiles

#### Index - `/super_admin/admin_talent_profiles`

**Controller:** `super_admin/admin_talent_profiles`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_admin_talent_profiles`

![super_admin/admin_talent_profiles_index](screenshots/superadmin/super_admin_admin_talent_profiles_index.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Show - `/super_admin/admin_talent_profiles/1`

**Controller:** `super_admin/admin_talent_profiles`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_admin_talent_profile`

![super_admin/admin_talent_profiles_show](screenshots/superadmin/super_admin_admin_talent_profiles_show.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

### Super Admin / Saved Searches

#### Index - `/super_admin/saved_searches`

**Controller:** `super_admin/saved_searches`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_saved_searches`

![super_admin/saved_searches_index](screenshots/superadmin/super_admin_saved_searches_index.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Show - `/super_admin/saved_searches/1`

**Controller:** `super_admin/saved_searches`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_saved_search`

![super_admin/saved_searches_show](screenshots/superadmin/super_admin_saved_searches_show.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

### Super Admin / Security Alerts

#### Index - `/super_admin/security_alerts`

**Controller:** `super_admin/security_alerts`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_security_alerts`

![super_admin/security_alerts_index](screenshots/superadmin/super_admin_security_alerts_index.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Show - `/super_admin/security_alerts/1`

**Controller:** `super_admin/security_alerts`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_security_alert`

![super_admin/security_alerts_show](screenshots/superadmin/super_admin_security_alerts_show.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

### Super Admin / Session Activities

#### Index - `/super_admin/session_activities`

**Controller:** `super_admin/session_activities`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_session_activities`

![super_admin/session_activities_index](screenshots/superadmin/super_admin_session_activities_index.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---

#### Show - `/super_admin/session_activities/1`

**Controller:** `super_admin/session_activities`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `super_admin_session_activity`

![super_admin/session_activities_show](screenshots/superadmin/super_admin_session_activities_show.png)

*Screenshot placeholder - Image will be captured during screenshot automation process*

---
