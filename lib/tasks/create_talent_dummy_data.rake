namespace :dummy_data do
  desc 'Create dummy data for talent routes testing'
  task create_talent_data: :environment do
    puts 'Creating dummy data for talent routes...'

    # Find or create a talent user for testing
    talent_user =
      User.find_by(email: '<EMAIL>') ||
        User.create!(
          email: '<EMAIL>',
          password: 'password123',
          password_confirmation: 'password123',
          first_name: 'Test',
          last_name: 'Talent',
          role: 'talent',
          confirmed_at: Time.current,
        )

    # Find or create a scout user for conversations
    scout_user =
      User.find_by(email: '<EMAIL>') ||
        User.create!(
          email: '<EMAIL>',
          password: 'password123',
          password_confirmation: 'password123',
          first_name: 'Test',
          last_name: 'Scout',
          role: 'scout',
          confirmed_at: Time.current,
        )

    puts '✓ Created/found test users'

    # Create jobs if they don't exist
    if Job.count < 3
      3.times do |i|
        Job.create!(
          title: "Test Job #{i + 1}",
          description: "This is a test job description for job #{i + 1}",
          category: %w[newsletter lead_magnet other].sample,
          budget_min: 1000 + (i * 500),
          budget_max: 2000 + (i * 500),
          user: scout_user,
          status: 'published',
        )
      end
      puts '✓ Created test jobs'
    end

    # Create job applications for the talent user
    jobs = Job.limit(2)
    jobs.each do |job|
      unless JobApplication.exists?(user: talent_user, job: job)
        JobApplication.create!(
          user: talent_user,
          job: job,
          application_letter:
            "This is a test application letter for #{job.title}. I am very interested in this position and believe I would be a great fit.",
          status: 'applied',
        )
      end
    end
    puts '✓ Created job applications'

    # Create conversations with messages
    jobs.each_with_index do |job, index|
      conversation =
        Conversation.find_or_create_by(job: job) do |conv|
          conv.subject = "Discussion about #{job.title}"
        end

      # Add participants if not already added
      [talent_user, scout_user].each do |user|
        unless ConversationParticipant.exists?(
                 conversation: conversation,
                 user: user,
               )
          ConversationParticipant.create!(
            conversation: conversation,
            user: user,
          )
        end
      end

      # Create messages if conversation is empty
      if conversation.messages.empty?
        # Scout initiates conversation
        Message.create!(
          conversation: conversation,
          user: scout_user,
          body:
            "Hi #{talent_user.first_name}, I saw your application for #{job.title} and would like to discuss it further.",
        )

        # Talent responds
        Message.create!(
          conversation: conversation,
          user: talent_user,
          body:
            "Thank you for reaching out! I'm very excited about this opportunity and would love to learn more.",
        )

        # Scout follows up
        Message.create!(
          conversation: conversation,
          user: scout_user,
          body:
            "Great! Let's schedule a call to discuss the details. What's your availability this week?",
        )
      end

      # Archive one conversation for testing archives
      if index == 1
        ConversationParticipant
          .where(conversation: conversation, user: talent_user)
          .update_all(archived: true)
      end
    end
    puts '✓ Created conversations and messages'

    # Create subscription for the talent user
    unless talent_user.payment_processor.present?
      # Create a payment processor (using Pay gem)
      pay_customer = talent_user.set_payment_processor(:stripe)
      pay_customer.update!(processor_id: 'cus_test_talent')

      # Find or create a subscription plan
      plan =
        SubscriptionPlan.find_or_create_by(
          stripe_price_id: 'price_1R9Q55DYYVPVcCCrWQOwsKmT',
        ) do |p|
          p.name = 'Standard'
          p.description =
            'Standard subscription plan with all essential features'
          p.amount = 9900
          p.currency = 'usd'
          p.billing_interval = 'year'
          p.billing_interval_count = 1
          p.features = [
            'Access to all job listings',
            'Direct messaging with scouts',
            'Profile visibility',
            'Standard support',
          ]
          p.active = true
          p.legacy = false
        end

      # Create an active subscription
      unless talent_user.subscriptions.active.exists?
        subscription =
          pay_customer.subscriptions.create!(
            name: 'default',
            processor_plan: plan.stripe_price_id,
            processor_id: 'sub_test_talent',
            status: 'active',
          )
        puts '✓ Created active subscription'
      end
    end

    puts "\n🎉 Dummy data creation complete!"
    puts "\nCreated data for talent user: #{talent_user.email}"
    puts "- Job Applications: #{talent_user.job_applications.count}"
    puts "- Conversations: #{talent_user.conversations.count}"
    puts "- Archived Conversations: #{talent_user.conversations.joins(:conversation_participants).where('conversation_participants.archived = true').count}"
    puts "- Active Subscriptions: #{talent_user.subscriptions.active.count}"
    puts "\nYou can now access all talent routes with this user!"
  end

  desc 'Clean up dummy data'
  task clean_talent_data: :environment do
    puts 'Cleaning up dummy data...'

    # Find test users
    talent_user = User.find_by(email: '<EMAIL>')
    scout_user = User.find_by(email: '<EMAIL>')

    if talent_user
      # Clean up associated data
      talent_user.job_applications.destroy_all
      talent_user.conversation_participants.destroy_all
      talent_user.subscriptions.destroy_all
      talent_user.destroy
      puts '✓ Cleaned up talent user and associated data'
    end

    if scout_user
      scout_user.jobs.destroy_all
      scout_user.conversation_participants.destroy_all
      scout_user.destroy
      puts '✓ Cleaned up scout user and associated data'
    end

    puts '🧹 Cleanup complete!'
  end
end
