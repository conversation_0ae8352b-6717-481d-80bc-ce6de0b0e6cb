namespace :dummy_data do
  desc 'Create dummy data for scout routes testing'
  task create_scout_data: :environment do
    puts 'Creating dummy data for scout routes...'

    # Get or create roles
    scout_role = Role.find_or_create_by(name: 'scout')
    talent_role = Role.find_or_create_by(name: 'talent')

    # Find or create a scout user for testing
    scout_user =
      User.find_by(email: '<EMAIL>') ||
        User.create!(
          email: '<EMAIL>',
          password: 'password123456',
          password_confirmation: 'password123456',
          first_name: 'Test',
          last_name: 'Scout',
          verified: true,
          signup_intent: 'scout',
          scout_signup_completed: true,
          talent_signup_completed: false,
          onboarding_completed: true,
          onboarding_step: 'completed',
        )

    # Assign scout role if not already assigned
    unless scout_user.has_role?('scout')
      UserRole.create!(user: scout_user, role: scout_role)
    end

    # Find or create talent users for conversations and applications
    talent_users = []
    3.times do |i|
      talent_user =
        User.find_by(email: "talent#{i + 1}@example.com") ||
          User.create!(
            email: "talent#{i + 1}@example.com",
            password: 'password123456',
            password_confirmation: 'password123456',
            first_name: "Talent#{i + 1}",
            last_name: 'User',
            verified: true,
            signup_intent: 'talent',
            scout_signup_completed: false,
            talent_signup_completed: true,
            onboarding_completed: true,
            onboarding_step: 'completed',
          )

      # Assign talent role if not already assigned
      unless talent_user.has_role?('talent')
        UserRole.create!(user: talent_user, role: talent_role)
      end

      talent_users << talent_user
    end

    puts '✓ Created/found test users'

    # Create or find organization for scout user
    organization =
      Organization.find_or_create_by(name: 'Test Scout Organization') do |org|
        org.email = '<EMAIL>'
        org.bio = 'A test organization for scout route testing'
        org.size = 'small'
        org.operating_timezone = 'UTC'
      end

    # Add scout to organization if not already a member
    unless OrganizationMembership.exists?(
             user: scout_user,
             organization: organization,
           )
      OrganizationMembership.create!(
        user: scout_user,
        organization: organization,
        org_role: 'admin',
      )
    end

    # Set the scout's last logged in organization
    scout_user.update!(last_logged_in_organization_id: organization.id)

    # Create jobs for the organization
    jobs = []
    if organization.jobs.count < 3
      3.times do |i|
        category = %w[social_media lead_magnet newsletter].sample

        job_attrs = {
          title: "Scout Test Job #{i + 1}",
          description:
            "This is a comprehensive test job description for scout job #{i + 1}. We are looking for talented individuals to join our team.",
          job_category: category,
          budget_range: %w[
            range_1000_2000
            range_2000_3500
            range_3500_5000
          ].sample,
          work_duration: %w[one_time_project long_term short_term].sample,
          organization: organization,
          status: 'published'
        }

        # Add category-specific required fields
        case category
        when 'social_media'
          job_attrs.merge!(
            platform: 'linkedin',
            outcome: 'leads',
            social_media_goal_type: 'social_media_leads',
            social_media_understands_risk_acknowledged: true
          )
        when 'lead_magnet'
          job_attrs.merge!(
            lead_magnet_type: 'ebook',
            outcome: 'grow_email_list'
          )
        when 'newsletter'
          job_attrs.merge!(
            newsletter_frequency: 'weekly',
            newsletter_length: 'words_300_600',
            outcome: 'grow_email_list'
          )
        end

        job = Job.create!(job_attrs)
        jobs << job
        puts "✓ Created job: #{job.title}"
      end
    else
      jobs = organization.jobs.limit(3)
    end

    # Create job applications from talent users
    jobs.each_with_index do |job, job_index|
      talent_users.each_with_index do |talent_user, talent_index|
        # Create applications for first 2 jobs only to have variety
        next if job_index > 1

        unless JobApplication.exists?(user: talent_user, job: job)
          application =
            JobApplication.create!(
              user: talent_user,
              job: job,
              application_letter:
                "Dear #{scout_user.first_name}, I am very interested in the #{job.title} position. I believe my skills and experience make me an excellent candidate for this role.",
              status: %w[applied reviewed qualified].sample,
            )
          puts "✓ Created application from #{talent_user.first_name} for #{job.title}"
        end
      end
    end

    # Create conversations between scout and talent users
    jobs
      .first(2)
      .each_with_index do |job, index|
        talent_user = talent_users[index]

        conversation = Conversation.find_or_create_by(job: job)

        # Add participants if not already added
        [scout_user, talent_user].each do |user|
          unless ConversationParticipant.exists?(
                   conversation: conversation,
                   user: user,
                 )
            ConversationParticipant.create!(
              conversation: conversation,
              user: user,
            )
          end
        end

        # Create messages if conversation is empty
        if conversation.messages.empty?
          # Scout initiates conversation
          Message.create!(
            conversation: conversation,
            user: scout_user,
            body:
              "Hi #{talent_user.first_name}, I reviewed your application for #{job.title} and I'm impressed with your background. I'd like to discuss this opportunity further.",
          )

          # Talent responds
          Message.create!(
            conversation: conversation,
            user: talent_user,
            body:
              "Thank you for reaching out! I'm very excited about this opportunity and would love to learn more about the role and your team.",
          )

          # Scout follows up
          Message.create!(
            conversation: conversation,
            user: scout_user,
            body:
              "Excellent! Let's schedule a call to discuss the project details and timeline. What's your availability this week?",
          )

          puts "✓ Created conversation between #{scout_user.first_name} and #{talent_user.first_name}"
        end

        # Archive one conversation for testing archives
        if index == 1
          ConversationParticipant
            .where(conversation: conversation, user: scout_user)
            .update_all(archived: true)
          puts '✓ Archived conversation for testing'
        end
      end

    # Create subscription for the scout user
    unless scout_user.payment_processor.present?
      # Create a payment processor (using Pay gem)
      pay_customer = scout_user.set_payment_processor(:stripe)
      pay_customer.update!(processor_id: 'cus_test_scout')

      # Find or create a subscription plan
      plan =
        SubscriptionPlan.find_or_create_by(
          stripe_price_id: 'price_1R9Q66DYYVPVcCCrnqiXNafF',
        ) do |p|
          p.name = 'Premium'
          p.description = 'Premium subscription plan with advanced features'
          p.amount = 19_900
          p.currency = 'usd'
          p.billing_interval = 'year'
          p.billing_interval_count = 1
          p.features = [
            'Unlimited job postings',
            'Advanced talent search',
            'Priority support',
            'Analytics dashboard',
          ]
          p.active = true
          p.legacy = false
        end

      # Create an active subscription
      unless scout_user.subscriptions.active.exists?
        subscription =
          pay_customer.subscriptions.create!(
            name: 'default',
            processor_plan: plan.stripe_price_id,
            processor_id: 'sub_test_scout',
            status: 'active',
          )
        puts '✓ Created active subscription for scout'
      end
    end

    puts "\n🎉 Scout dummy data creation complete!"
    puts "\nCreated data for scout user: #{scout_user.email}"
    puts "- Organization: #{organization.name}"
    puts "- Jobs: #{organization.jobs.count}"
    puts "- Job Applications: #{JobApplication.joins(:job).where(jobs: { organization: organization }).count}"
    puts "- Conversations: #{scout_user.conversations.count}"
    puts "- Archived Conversations: #{scout_user.conversations.joins(:conversation_participants).where('conversation_participants.archived = true').count}"
    puts "- Active Subscriptions: #{scout_user.subscriptions.active.count}"
    puts "\nYou can now access all scout routes with this user!"
  end

  desc 'Clean up scout dummy data'
  task clean_scout_data: :environment do
    puts 'Cleaning up scout dummy data...'

    # Find test users
    scout_user = User.find_by(email: '<EMAIL>')
    talent_users =
      User.where(
        email: %w[<EMAIL> <EMAIL> <EMAIL>],
      )

    if scout_user
      # Clean up associated data
      scout_user.jobs.destroy_all
      scout_user.conversation_participants.destroy_all
      scout_user.subscriptions.destroy_all
      scout_user.destroy
      puts '✓ Cleaned up scout user and associated data'
    end

    talent_users.each do |talent_user|
      talent_user.job_applications.destroy_all
      talent_user.conversation_participants.destroy_all
      talent_user.destroy
      puts "✓ Cleaned up talent user: #{talent_user.email}"
    end

    puts '🧹 Scout cleanup complete!'
  end
end
