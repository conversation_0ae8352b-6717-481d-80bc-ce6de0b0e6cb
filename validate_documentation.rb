#!/usr/bin/env ruby

require 'json'
require 'fileutils'

class DocumentationValidator
  def initialize
    @errors = []
    @warnings = []
    @info = []
    @routes_data = load_routes_data
  end

  def validate_all
    puts "🔍 Starting Documentation Validation..."
    puts "=" * 50
    
    validate_file_structure
    validate_routes_data
    validate_documentation_files
    validate_screenshot_references
    generate_report
    
    puts "\n✅ Validation Complete!"
    puts "📊 Summary: #{@errors.length} errors, #{@warnings.length} warnings, #{@info.length} info items"
  end

  private

  def load_routes_data
    if File.exist?('routes.json')
      data = JSON.parse(File.read('routes.json'))
      # Handle the nested structure with user_types
      if data.is_a?(Hash) && data['user_types']
        routes = []
        data['user_types'].each do |user_type, user_routes|
          routes.concat(user_routes) if user_routes.is_a?(Array)
        end
        routes
      else
        data
      end
    else
      @errors << "routes.json file not found"
      []
    end
  end

  def validate_file_structure
    puts "\n📁 Validating File Structure..."
    
    required_files = [
      'routes.json',
      'talent-routes-screenshots.md',
      'scout-routes-screenshots.md', 
      'superadmin-routes-screenshots.md',
      'ghostwrote-ui-documentation-summary.md'
    ]
    
    required_files.each do |file|
      if File.exist?(file)
        @info << "✓ Found required file: #{file}"
        validate_file_size(file)
      else
        @errors << "✗ Missing required file: #{file}"
      end
    end
    
    # Check for screenshot directories
    screenshot_dirs = ['screenshots/talent', 'screenshots/scout', 'screenshots/superadmin']
    screenshot_dirs.each do |dir|
      if Dir.exist?(dir)
        @info << "✓ Found screenshot directory: #{dir}"
      else
        @warnings << "⚠ Screenshot directory not found: #{dir}"
      end
    end
  end

  def validate_file_size(file)
    size = File.size(file)
    if size == 0
      @errors << "✗ File is empty: #{file}"
    elsif size < 1000
      @warnings << "⚠ File seems small (#{size} bytes): #{file}"
    else
      @info << "✓ File size OK: #{file} (#{size} bytes)"
    end
  end

  def validate_routes_data
    puts "\n🛣️  Validating Routes Data..."

    return if @routes_data.empty?

    total_routes = @routes_data.length
    @info << "✓ Total routes discovered: #{total_routes}"

    # Count routes by user type
    user_types = {
      'talent' => 0,
      'scout' => 0,
      'super_admin' => 0,
      'public' => 0
    }

    @routes_data.each do |route|
      if route.is_a?(Hash)
        path = route['path'] || ''
        case path
        when /^\/talent/
          user_types['talent'] += 1
        when /^\/scout/
          user_types['scout'] += 1
        when /^\/super_admin/
          user_types['super_admin'] += 1
        else
          user_types['public'] += 1
        end
      end
    end

    user_types.each do |type, count|
      @info << "✓ #{type.capitalize} routes: #{count}"
    end

    # Validate route structure
    required_fields = ['path', 'controller', 'action']
    @routes_data.each_with_index do |route, index|
      if route.is_a?(Hash)
        required_fields.each do |field|
          unless route.key?(field)
            @warnings << "⚠ Route #{index + 1} missing field: #{field}"
          end
        end
      end
    end
  end

  def validate_documentation_files
    puts "\n📝 Validating Documentation Content..."
    
    doc_files = [
      'talent-routes-screenshots.md',
      'scout-routes-screenshots.md',
      'superadmin-routes-screenshots.md'
    ]
    
    doc_files.each do |file|
      next unless File.exist?(file)
      
      content = File.read(file)
      
      # Check for required sections
      required_sections = ['# ', '## Overview', '## Route Tree', '## Key Features']
      required_sections.each do |section|
        if content.include?(section)
          @info << "✓ #{file}: Found section '#{section.strip}'"
        else
          @warnings << "⚠ #{file}: Missing section '#{section.strip}'"
        end
      end
      
      # Check for screenshot references
      screenshot_count = content.scan(/!\[.*\]\(screenshots\/.*\.png\)/).length
      if screenshot_count > 0
        @info << "✓ #{file}: Contains #{screenshot_count} screenshot references"
      else
        @warnings << "⚠ #{file}: No screenshot references found"
      end
      
      # Check content length
      lines = content.lines.length
      if lines < 50
        @warnings << "⚠ #{file}: Content seems short (#{lines} lines)"
      else
        @info << "✓ #{file}: Good content length (#{lines} lines)"
      end
    end
  end

  def validate_screenshot_references
    puts "\n🖼️  Validating Screenshot References..."
    
    all_files = Dir.glob('*.md')
    screenshot_refs = []
    
    all_files.each do |file|
      content = File.read(file)
      refs = content.scan(/!\[.*?\]\((screenshots\/.*?\.png)\)/)
      screenshot_refs.concat(refs.flatten)
    end
    
    @info << "✓ Total screenshot references found: #{screenshot_refs.length}"
    
    # Check if referenced screenshots exist
    missing_screenshots = []
    screenshot_refs.each do |ref|
      unless File.exist?(ref)
        missing_screenshots << ref
      end
    end
    
    if missing_screenshots.empty?
      @info << "✓ All referenced screenshots exist"
    else
      @warnings << "⚠ Missing screenshots: #{missing_screenshots.length}"
      missing_screenshots.each do |missing|
        @warnings << "  - #{missing}"
      end
    end
  end

  def generate_report
    puts "\n📋 Generating Validation Report..."
    
    report = []
    report << "# Documentation Validation Report"
    report << ""
    report << "**Generated:** #{Time.now.strftime('%Y-%m-%d %H:%M:%S')}"
    report << "**Validator:** Augment Agent Documentation Validator"
    report << ""
    
    # Summary
    report << "## Summary"
    report << ""
    report << "| Category | Count |"
    report << "|----------|-------|"
    report << "| Errors | #{@errors.length} |"
    report << "| Warnings | #{@warnings.length} |"
    report << "| Info | #{@info.length} |"
    report << ""
    
    # Errors
    if @errors.any?
      report << "## ❌ Errors"
      report << ""
      @errors.each { |error| report << "- #{error}" }
      report << ""
    end
    
    # Warnings  
    if @warnings.any?
      report << "## ⚠️ Warnings"
      report << ""
      @warnings.each { |warning| report << "- #{warning}" }
      report << ""
    end
    
    # Info
    if @info.any?
      report << "## ℹ️ Information"
      report << ""
      @info.each { |info| report << "- #{info}" }
      report << ""
    end
    
    # Recommendations
    report << "## 🎯 Recommendations"
    report << ""
    
    if @errors.any?
      report << "### Critical Issues"
      report << "- Address all errors before proceeding with documentation review"
      report << "- Ensure all required files are present and properly formatted"
      report << ""
    end
    
    if @warnings.any?
      report << "### Improvements"
      report << "- Review warnings for potential content enhancements"
      report << "- Consider adding missing screenshots for complete documentation"
      report << "- Verify all screenshot references point to valid files"
      report << ""
    end
    
    report << "### Next Steps"
    report << "1. **Review Documentation**: Examine all generated markdown files"
    report << "2. **Capture Missing Screenshots**: Add any missing visual documentation"
    report << "3. **Content Review**: Ensure all sections are complete and accurate"
    report << "4. **Stakeholder Review**: Share documentation with relevant stakeholders"
    report << "5. **Implementation Planning**: Use documentation for development planning"
    
    File.write('validation_report.md', report.join("\n"))
    @info << "✓ Validation report saved to: validation_report.md"
  end
end

# Run validation if script is executed directly
if __FILE__ == $0
  validator = DocumentationValidator.new
  validator.validate_all
end
